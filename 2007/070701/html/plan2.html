﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>钟详汽贸</title>
<style type="text/css">
<!--
* {
	margin: 0px;
	padding: 0px;
	border: 0px;
}
a {
	color: #0000FF;
	text-decoration: none;
}
p {
	letter-spacing: 1px;
}
body {
	background-image: url(img/logobg.gif);
	font-family: "宋体";
	font-size: 12px;
	color: #000000;
	line-height: 20px;
}
a:hover {
	color: #00FF00;
	text-decoration: underline;
}
#divPage {
	width: 760px;
	margin-right: auto;
	margin-left: auto;
}
#divNav {
}
#divNav a {
	display:block;
	font-size: 14px;
	line-height: 25px;
	background-image: url(img/NavBtnBg.jpg);
	background-position: center;
	color: #FFFFFF;
	font-weight: bold;
}
#divNav a:hover {
	background-image: url(img/NavBtnBg2.jpg);
	background-position: center;
	text-decoration: none;
}
#divPage #InLink {
}
-->
</style>
<script type="text/JavaScript">
<!--
function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}

function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}
//-->
</script>
</head>
<body onload="MM_preloadImages('img/link05_2.jpg','img/link01_2.jpg','img/link04_2.jpg','img/link03_2.jpg','img/link02_2.jpg','img/link06_2.jpg')">

<SCRIPT FOR='EccoolAd' EVENT='fscommand()' LANGUAGE='javascript'>
AdLayer1.style.visibility='hidden';
AdLayer2.style.visibility='hidden';
</script>
<script>
function initEcAd() {
document.all.AdLayer1.style.posTop = -200;
document.all.AdLayer1.style.visibility = 'visible'
document.all.AdLayer2.style.posTop = -200;
document.all.AdLayer2.style.visibility = 'visible'
MoveLeftLayer('AdLayer1');
MoveRightLayer('AdLayer2');
}
function MoveLeftLayer(layerName) {
var x = 5;
var y = 50;
var diff = (document.body.scrollTop + y - document.all.AdLayer1.style.posTop)*.40;
var y = document.body.scrollTop + y - diff;
eval("document.all." + layerName + ".style.posTop = y");
eval("document.all." + layerName + ".style.posLeft = x");
setTimeout("MoveLeftLayer('AdLayer1');", 20);
}
function MoveRightLayer(layerName) {
var x = 5;
var y = 50;
var diff = (document.body.scrollTop + y - document.all.AdLayer2.style.posTop)*.40;
var y = document.body.scrollTop + y - diff;
eval("document.all." + layerName + ".style.posTop = y");
eval("document.all." + layerName + ".style.posRight = x");
setTimeout("MoveRightLayer('AdLayer2');", 20);
}
document.write("<div id=AdLayer1 style='position: absolute;visibility:hidden;z-index:1'><EMBED src='img/flash02.swf' quality=high WIDTH=100 HEIGHT=300 TYPE='application/x-shockwave-flash' id=dl></EMBED></div>"
+"<div id=AdLayer2 style='position: absolute;visibility:hidden;z-index:1'><EMBED src='img/flash02.swf' quality=high WIDTH=100 HEIGHT=300 TYPE='application/x-shockwave-flash' id=dl></EMBED></div>");
initEcAd()
</script>

<div id="divPage">
<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><embed src="img/flash01.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer" type="application/x-shockwave-flash" width="760" height="130"></embed></td>
  </tr>
</table>
<table id="divNav" width="100%" border="0" cellspacing="0" cellpadding="0">
   <tr align="center">
    <td><a href="plan2.html">公司首页</a></td>
    <td><a href="../sell.html">销售中心</a></td>
    <td><a href="#">空链接一</a></td>
    <td><a href="#">空链接二</a></td>
    <td><a href="#">空链接三</a></td>
  </tr>
</table>
<br />
<table id="CoInfo" width="760" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td><img src="img/smal.jpg" width="215" height="115" border="0" /></td>
    <td><p>&nbsp;&nbsp;&nbsp;&nbsp;六盘水钟祥亚飞汽车连锁有限公司成立于一九九九年七月。公司注册资金226万元，现有固定资产500万元，流动资金500万元。公司总部地址：六盘水市钟山大道一段（双水机动车交易市场旁）；分公司地址：六盘水荷城机动车交易市场。主要经营国产及进口轿车、越野车及其他整车业务。是华泰现代汽车、海南马自达的二级经销商，比亚迪 F3 汽车在六盘水地区的一级经销商。并取得了二类维修企业的资质等级。</p></td>
  </tr>
</table>
<br />
<table width="760" align="center" cellpadding="0" cellspacing="5">
  <tr align="center">
    <td><a href="http://www.huataixiandai.com/"><img src="img/link05.jpg" width="230" height="100" border="0" id="Image5" onmouseover="MM_swapImage('Image5','','img/link05_2.jpg',1)" onmouseout="MM_swapImgRestore()" /></a></td>
    <td><a href="http://www.bydauto.com.cn/" target="_blank"><img src="img/link01.jpg" width="230" height="100" border="0" id="Image1" onmouseover="MM_swapImage('Image1','','img/link01_2.jpg',1)" onmouseout="MM_swapImgRestore()" /></a></td>
    <td><a href="http://www.hnmazda.com/" target="_blank"><img src="img/link04.jpg" width="230" height="100" border="0" id="Image4" onmouseover="MM_swapImage('Image4','','img/link04_2.jpg',1)" onmouseout="MM_swapImgRestore()" /></a></td>
  </tr>
  <tr align="center">
    <td><a href="http://www.csvw.com/" target="_blank"><img src="img/link03.jpg" width="230" height="100" border="0" id="Image2" onmouseover="MM_swapImage('Image2','','img/link03_2.jpg',1)" onmouseout="MM_swapImgRestore()" /></a></td>
    <td><a href="http://www.faw-volkswagen.com/" target="_blank"><img src="img/link02.jpg" width="230" height="100" border="0" id="Image3" onmouseover="MM_swapImage('Image3','','img/link02_2.jpg',1)" onmouseout="MM_swapImgRestore()" /></a></td>
    <td><a href="http://www.shanghaigm.com/" target="_blank"><img src="img/link06.jpg" name="Image6" width="230" height="100" border="0" id="Image6" onmouseover="MM_swapImage('Image6','','img/link06_2.jpg',1)" onmouseout="MM_swapImgRestore()" /></a></td>
  </tr>
</table>
<br />
<table width="760" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td><p>公司竞争与发展优势<br />
      1．健全的公司机构设制<br />
      领导机构总经理1名，销售经理1名，售后经理1名，负责公司的总体发展思路和公司重要业务购销活动。公司财务部负责公司财务核算和财务收支管理。公司销售部，负责车辆验收、客户接待、销售、信贷、客户资源及信息反馈、售后咨询及服务。公司后勤部，负责公司的环境卫生、安全保卫工作。<br />
      2．务实的管理和发展思路<br />
      公司领导班子注重人才的引进和培养，公司自成立以来，先后输送员工12人次到北京、上海、重庆、贵阳等地进行业务知识培训，以不断提高员工素质、增进员工营销服务及售后服务新理念。同时，公司坚持每周一次的全体员工学习制度，对员工进行政治思想、法律知识、业务专业知识的学习，树立六盘水钟祥亚飞公司员工的良好形象。<br />
      3．公司的硬件设备及发展前景<br />
      公司具备先进的办公设备，设有计算机、传真机、复印机、数码相机，还具备了先进的维修设备，设有举升机10台、四柱举升机一台、烤漆房一台、大梁校正仪一台、四轮定位仪一台，动平衡机一台、氮气机一台等各种先进的维修设备，为高效的办公、维修质量和速度提供了必要的保障。</p></td>
  </tr>
</table>
<br />
<table width="760" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td><p>公司经营现有车辆展厅7000平方米，仓储222平方米，停车场1000平方米，办公面积450平方米。实现集汽车销售、售后服务、车辆维修、车辆配件一条龙的现代化汽车营销企业,为六盘水的经济建设做出贡献。 <br />
      公司现有员工52名。其中，具有高级技术职称2人，中级技术职称人员4名，初级职称8名，具有大专以上学历员工3名，中专以上学历12名。安排待业青年18名，国企下岗职工2名，公司法人代表钟英祥同志为六盘水市第四、五届人大代表，钟山区第三、四、五届政协常委。</p></td>
  </tr>
</table>
<br />
<hr size="2" color="#666666" width="90%" />
<table border="0" align="center" cellpadding="0" cellspacing="0" class="copr">
  <tr>
    <td> copyright &copy; 2006-2007 Liupanshui Zhongxiang Qimao </td>
  </tr>
</table>
</div>
</body>
</html>
