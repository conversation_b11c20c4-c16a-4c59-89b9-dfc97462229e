﻿// JavaScript Document
function FrmReSize(fm_name,fm_id)
{
    var frm = document.getElementById(fm_id);  
    var subWeb = document.frames?document.frames[fm_name].document:frm.contentDocument;  
    if(frm != null && subWeb != null)
	{ 
        frm.style.height = subWeb.documentElement.scrollHeight + "px"; 
        //如需自适应宽，去除下行的“//”注释即可
        //frm.style.width = subWeb.documentElement.scrollWidth+"px"; 
    }
}