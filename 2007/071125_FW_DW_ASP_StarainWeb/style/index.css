@charset "utf-8";
body {
	font-family: "宋体";
	font-size: 14px;
	color: #FFF;
	margin: 0px;
	padding: 0px;
}
a {
	color: #FFF;
	text-decoration: none;
}
a:hover{
	text-decoration: underline;
}
#page {
	height: 890px;
	width: 1000px;
	background-image: url(index/allpagbg.jpg);
	background-repeat: repeat-x;
	overflow:hidden;
}
#main {
	height: 875px;
	width: 930px;
	margin-right: auto;
	margin-left: auto;
	background-color: #000;
	background-image: url(index/hadshdo.jpg);
	background-repeat: repeat-x;
	background-position: 0px 0px;
	padding-top: 15px;
	overflow:hidden;
	border: 1px solid #4F87DA;
}
#title {
	text-align: right;
	height: 25px;
	line-height: 25px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 13px;
}
#navmenu {
	background-image: url(index/navbg.jpg);
	background-repeat: repeat-x;
	height: 100px;
}
#logotext {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 30px;
	text-align: right;
	float: left;
	width: 300px;
	padding-top:35px;
	padding-left:95px;
}
#logotext  p {
	font-size: 14px;
	color: #9ED8F2;
	margin: 0px;
}
#navbtn {
	float: right;
	font-family: "黑体", "微软雅黑";
	margin-right: 10px;
	height: 95px;
}
#navbtn     a{
	width:110px;
	height:40px;
	margin-right: 2px;
	background-image: url(index/navbtnbg.jpg);
	background-position: 0px 0px;
	background-repeat: repeat-x;
	float: left;
	text-align: right;
	padding-right: 10px;
	padding-top: 60px;
}
#navbtn a:hover{
	background-image:url(index/navbtnbg.jpg);
	background-position: 0px -95px;
	background-repeat: repeat-x;
}
#banner {
	height: 385px;
	margin-bottom: 10px;
	background-image: url(index/banner.jpg);
	background-repeat: no-repeat;
}
#banner #bannerflash {
	float: right;
	margin: 20px;
	height: 145px;
	width: 400px;
}

#column {
	height: 220px;
	clear: both;
}

#column p{
	line-height: 18px;
	font-size: 13px;
	letter-spacing: 2px;
	height: 90px;
	overflow: hidden;
	padding-top: 10px;
	padding-right: 10px;
	padding-bottom: 0px;
	padding-left: 10px;
}
#column .coltitle {
	line-height: 35px;
	font-family: "黑体", "微软雅黑";
	font-size: 15px;
	margin-left: 10px;
}
#column a {
	border-top-width: 1px;
	border-right-width: 1px;
	border-top-style: solid;
	border-right-style: solid;
	border-top-color: #06F;
	border-right-color: #06F;
	margin-right: 10px;
	text-align: right;
	padding: 5px;
	font-size: 12px;
	color: #CCC;
	width: 200px;
	float: right;
}
#column a:hover{
	background-color: #568FDE;

}

#columnL {
	float: left;
	height: 220px;
	width: 285px;
	background-image: url(index/columnbg.jpg);
}
#columnC {
	height: 220px;
	width: 320px;
	float: left;
	background-image: url(index/columnbg.jpg);
	margin-left: 20px;
}
#columnR {
	float: right;
	height: 220px;
	width: 285px;
	background-image: url(index/columnbg.jpg);
}
#copyright {
	width: 750px;
	margin-right: auto;
	margin-left: auto;
	border-top-width: 2px;
	border-top-style: solid;
	border-top-color: #666;
	margin-top: 15px;
	height: 60px;
	margin-bottom: 2px;
	color: #666;
	padding: 5px;
	clear: both;
}
#copyright    a {
	font-family: "黑体", "微软雅黑";
	font-size: 14px;
	line-height: 30px;
	text-decoration: none;
	border-right-width: 1px;
	border-right-style: solid;
	border-right-color: #333;
	color: #666;
	padding-right: 25px;
	padding-left: 25px;
	float: left;
}
#copyright  a:hover {
	text-decoration: underline;
}
#copytext {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	float: left;
	line-height: 30px;
	margin-left: 20px;
}
#footer {
	background-image: url(index/foterbg.jpg);
	background-repeat: repeat-x;
	height: 55px;
	text-align: left;
}
