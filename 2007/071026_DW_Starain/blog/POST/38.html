﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-思科 Cisco Linksys WRT54GC v2 版无线路由升级步骤</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate4 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/36.html">&laquo; 活动你的手指，男人游戏推荐！</a><a class="r" href="http://starain.net.cn/blog/post/39.html">贴纸设计 NDSL 贴纸作品 &raquo;</a></div>
	<h4 class="post-date">2007-9-16 23:57:35</h4>
	<h2 class="post-title">思科 Cisco Linksys WRT54GC v2 版无线路由升级步骤</h2>
	<div class="post-body"><p>今天入手了思科 Cisco Linksys WRT54GC v2 版无线路由，但目前只在英国的 Linksys 站点上看到有新的固件更新，主要更新内容如下：</p><p>version 1.01.0<br />- Resolves issue with page not being displayed correctly after firmware upgrade<br />- Resolves issue with router mode- Resolves issue with PPPoE on certain networks<br />- Resolves issue with UDP port security reported by Secunia and Danil Niggebrugge<br />- Resolves issue with daylight savings time change<br /></p><p>固件下载页面如下：<br /><a href="http://www-uk.linksys.com/servlet/Satellite?c=L_Download_C2&amp;childpagename=UK%2FLayout&amp;cid=1129319258422&amp;packedargs=sku%3D1154470207740&amp;pagename=Linksys%2FCommon%2FVisitorWrapper&amp;lid=5842214507B01">http://www-uk.linksys.com/servlet/Satellite?c=L_Download_C2&amp;childpagename=UK%2FLayout&amp;cid=1129319258422&amp;packedargs=sku%3D1154470207740&amp;pagename=Linksys%2FCommon%2FVisitorWrapper&amp;lid=5842214507B01</a></p><p>路由配置界面是英文的，里面尽是网络专业术语缩写，对于普通用户可能有点难度，可以参考 v1 版的中文配置说明。</p><p>整个升级过程很快，建议拥有该无线路由器的用户尽快升级固件：</p><p>登陆路由器管理界面后，点击 Administration 选项卡切换到管理界面，然后点击 Firmware Upgrade 进入固件升级界面如下：</p><p>&nbsp;<img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709200146546655.jpg" alt="" title=""/></p><p>然后点击&ldquo;浏览&rdquo;定位到你下载的固件目录后，点击 Upgrade 按钮会进入固件升级界面并提示会断开所有网络链接，上载固件完成后会自动进入升级固件的进程如下：</p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709200151481163.jpg" alt="" title=""/></p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709200152095061.jpg" alt="" title=""/></p><p>升级过程很快，期间会提示&ldquo;不要关闭电源和点触复位按钮&rdquo;，升级完成后会发现右上角的版本号已经变成了 1.01 了。</p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709200154227354.jpg" alt="" title=""/></p><p>其它具体配置文档会尽快发布出来，如果急于配置的话可以发邮件给我或参考 v1 版路由的中文界面进行配置。</p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn38"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn38=38,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=38" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"><p><a  href="http://starain.net.cn/blog/post/18.html">家庭网与接入网的协同技术 论文</a>&nbsp;&nbsp;(2007-6-14 13:29:45)</p></li>
</ul>

<div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=e381246f" >
	<input type="hidden" name="inpId" id="inpId" value="38" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-10-7 19:33:24 -->