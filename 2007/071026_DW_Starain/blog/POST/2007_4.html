﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-2007 April</title>
</head>
<body class="multi catalog">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
 <div class="post cate6 auth1">
	<h4 class="post-date">2007年4月28日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/45.html">中国古典传统花纹图样</a></h2>
	<div class="post-body">一套中国传统花纹图样的临摹，源文件是一张 jpg 图片，我用 Illustrator 描成 AI 的适量图形，方便以后在任何地方使用...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn45"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn45=45,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年4月24日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/46.html">时装临摹 巡航线留念</a></h2>
	<div class="post-body">源文件扫描自一本杂志，我使用 Illustrator 描摹成矢量图形并进行填色，感觉色彩搭配效果，用钢笔描这个东西还是花费了挺长时间的...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn46"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn46=46,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年4月21日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/28.html">网页改版，主要针对页面宽度</a></h2>
	<div class="post-body">由于越来越多的计算机使用更高分辨率的显示器，以前常用于 800&times;600 分辨率的网页，现在与我们普遍使用的 1024&times;768 和 1280&times;1024 不大相衬，于是我使用动态页面宽度对以往 800&times;600 的页面进行了改版重排使其能够自动适应多种平面分辨率。其主要难点在于固定宽度图片的伸缩问题，为此，我选取了小部分近似循环出现的图片内容作为动态宽度区域的背景部分，然后对该部分做了稍微修饰，整体效果很好，但如果仔细去观察，会发现那一小部分是重复循环出...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn28"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn28=28,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年4月18日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/30.html">呼朋唤友网站首页</a></h2>
	<div class="post-body">做为一个现代的交友网站，我们能靠什么吸引用户，在线聊天？为什么不用MSN或者QQ；论坛博客？已经有太多优秀的类似站点。而现在很多站点所忽略的却是线下活动，毕竟每个在网路上的用户，在线下，不都是一个生动有趣的人嘛，所以我们把线下活动做为主要部分，占用了大量的版面篇幅，而且我们把类似以前在学校那种课外兴趣小组的模式运用进来，让不同兴趣的人都能在我们的站点里面找到自己的&ldquo;组织&rdquo;，从而能吸引更多的用户登陆我们的站点，主页面添加了媒体播放器，可以选择是否背景播放，这样可以一边听音乐...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn30"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn30=30,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年4月15日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/31.html">贵妃纤体美容中心</a></h2>
	<div class="post-body">这个站点是一个主要以排毒养颜、纤体瘦身为服务项目的美容机构，站点主要以粉色、紫色为主色调，透漏出一种女人和高贵的气息，广告部分使用了美容院一角的照片做为主题，加上我们所提倡的专业、健康、人性、关怀、美学等信息，使顾客在浏览我们网站的同时犹如真实来到我们的美容院一样，受到我们的笑脸相迎。文案主要针对我们美容机构主要的服务项目排毒养颜、纤体瘦身为中心介绍了一些传统减肥、瘦身的误区，使顾客能够很快的被我们所提倡的健康、专业所吸引，利用一小块版面展示了近期的纤体明星照片，使顾客对我们的能力更加肯定。因为...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn31"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn31=31,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年4月13日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/32.html">福娃地暖工程技术首页</a></h2>
	<div class="post-body">这个站点并没有使用很花哨的色彩和效果搭配，主要是考虑到给普通老百姓去浏览的站点，希望能很容易一眼看到主题，并用简单的图片展示使用地热采暖的科学依据，整个站点朴素大方，不会给人轻浮的感觉，而是一副务实，诚信的态度为您服务。主题以一句&ldquo;以后的冬天，不再寒冷。&rdquo;这一普普通通的看似老土的广告语开头，但正是这句简单朴实的语言，更能让我们普通百姓信服，达到返璞归真的效果...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn32"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn32=32,"</script>
	</h6>
</div>
<div class="post pagebar">分页:<a href="http://starain.net.cn/blog/catalog.asp?date=2007-4&amp;page=1">[&laquo;]</a><span class="now-page">1</span><a href="http://starain.net.cn/blog/catalog.asp?date=2007-4&amp;page=2">[2]</a><a href="http://starain.net.cn/blog/catalog.asp?date=2007-4&amp;page=2">[&raquo;]</a></div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div><script language="JavaScript" src="http://starain.net.cn/blog/function/c_html_js.asp?date=2007-4" type="text/javascript"></script></div>
</div>

<div class="function" id="divCatalog">
<h3>网站目录</h3>
<ul>
<li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=2" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_2.html">默认分类<span class="article-nums"> (0)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=3" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_3.html">软件下载<span class="article-nums"> (1)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=4" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_4.html">技术文档<span class="article-nums"> (6)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=5" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_5.html">休闲影音<span class="article-nums"> (3)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=6" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_6.html">一些作品<span class="article-nums"> (28)</span></a></li>
</ul>
</div>

<div class="function" id="divArchives">
<h3>文章归档</h3>
<ul>
<li><a href="http://starain.net.cn/blog/post/2007_9.html">2007 September (4)</a></li><li><a href="http://starain.net.cn/blog/post/2007_8.html">2007 August (12)</a></li><li><a href="http://starain.net.cn/blog/post/2007_7.html">2007 July (3)</a></li>
</ul>
</div>

<div class="function" id="divTags">
<h3>Tags</h3>
<ul>
<li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计 (10)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作 (9)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计 (4)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%A0%87%E8%AF%86%E8%AE%BE%E8%AE%A1">标识设计 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护 (1)</a></li>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright starain. Some Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>