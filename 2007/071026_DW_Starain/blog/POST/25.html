﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-腾讯 QQ DS、TM DS、skype DS 卡带设计</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate6 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/20.html">&laquo; 教你用 Photoshop 做花布 技术文档</a><a class="r" href="http://starain.net.cn/blog/post/37.html">局域网消息工具 &raquo;</a></div>
	<h4 class="post-date">2007-8-27 23:52:51</h4>
	<h2 class="post-title">腾讯 QQ DS、TM DS、skype DS 卡带设计</h2>
	<div class="post-body"><p>今天看到有人在建议腾讯开发 DS 平台用的 QQ TM 版本，我认为如果国内有正版卡带会赚钱的话，那 QQ DS 必定会是其中之一，从技术层面上来说，并不难实现，智能手机平台上适用的 KjavaQQ 不是在免费提供的嘛，而且如果腾讯公司这次走在了 MSN 和 skype 的前面，其正面影响和战略意义也是巨大的，如果真有这样的卡带面市，我觉得我愿意买这么一盘正版卡带，最好支持在线软升级，其实做这样的卡带成本并不高，技术投入也不像有的人说的那么大，关键是得到官方的支持，我们希望腾讯能在这个问题上走在 MSN 和 skype 的前面，而不是把 TM 2008 做的像 MSN 又像 QQ 那么不伦不类&hellip;&hellip;</p><p>以下为恶搞的 QQ DS ：</p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200708272353532332.JPG" alt="" title=""/></p><p>顺便恶搞一张 skepe DS ：</p><p>&nbsp;<img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200708280000205665.JPG" alt="" title=""/></p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:3 | 引用:0 | 浏览:<span id="spn25"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn25=25,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=25" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"><p><a  href="http://starain.net.cn/blog/post/20.html">教你用 Photoshop 做花布 技术文档</a>&nbsp;&nbsp;(2007-8-25 15:21:17)</p><p><a  href="http://starain.net.cn/blog/post/5.html">Adobe Photoshop CS3 Extended</a>&nbsp;&nbsp;(2007-8-9 14:23:30)</p><p><a  href="http://starain.net.cn/blog/post/21.html">生烤牛肉丁包装</a>&nbsp;&nbsp;(2007-3-22 16:17:20)</p><p><a  href="http://starain.net.cn/blog/post/22.html">手机喷绘</a>&nbsp;&nbsp;(2007-3-15 16:28:42)</p></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt23">1</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h3t5t3p2%3A8%2F3%2F3s4t8a4r7i6a4n9%2E6n5e2t5%2E7c4n9%2F2" rel="nofollow" target="_blank">http://starian.net.cn/</a></li>
	<li class="msgarticle">再加上发布相对应的周边如：Q仔耳机话筒、QQ摄像头、QQDS挂饰，其高利润是肯定的。<br/>乍一看，还真跟原装正货一样呢……<br/>由 starain 于 2007-8-30 17:17:36 最后编辑</li>
	<li class="msgtime">2007-8-28 0:09:23&nbsp;<a href="#comment" onclick="RevertComment('23')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt25">2</a>.<a>常静</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=" rel="nofollow" target="_blank"></a></li>
	<li class="msgarticle">你现在在哪混呢啊？？也没个音讯啊</li>
	<li class="msgtime">2007-9-5 11:27:15&nbsp;<a href="#comment" onclick="RevertComment('25')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt26">3</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h9t9t5p5%3A6%2F0%2F1s2t0a1r4a5i5n7%2E0n4e0t3%2E6c5n5%2F3" rel="nofollow" target="_blank">http://starain.net.cn/</a></li>
	<li class="msgarticle">其实9月6号就已经过来北京这边，然后因为直到前几天才有装宽带，所以一直没有跟大家联系，^_^，这下好了，又可以跟大家见面了……</li>
	<li class="msgtime">2007-9-20 0:34:19&nbsp;<a href="#comment" onclick="RevertComment('26')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=04dcc6b7" >
	<input type="hidden" name="inpId" id="inpId" value="25" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-9-30 1:47:52 -->