﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-2007 September</title>
</head>
<body class="multi catalog">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
 <div class="post cate5 auth1">
	<h4 class="post-date">2007年9月29日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/51.html">歌曲《scarborough fair》——sarah brightman</a></h2>
	<div class="post-body">推荐歌曲《scarborough fair》</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:休闲影音 | 评论:2 | 引用:0 | 浏览:<span id="spn51"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn51=51,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年9月25日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/48.html">传媒网站海报设计</a></h2>
	<div class="post-body">为一个互动传媒网站设计的几个宣传海报。</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:1 | 引用:0 | 浏览:<span id="spn48"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn48=48,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年9月24日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/47.html">对于国内 IT 业的非“杞人忧天”式担忧！</a></h2>
	<div class="post-body">前些日子跟一个电子商务网站技术总监的简短交谈让我看到了来自国内一部分 IT 公司在安全及系统维护问题上的缩影，其现状相当令人担忧。当谈到如何来维护整个公司计算机和服务器及网络设备时，总监先生告诉我 Ghost 就行，电脑变慢了，系统故障了，只要 Ghost 就行了&hellip;&hellip;我们且不说这是个做为 IT 领头羊的电子商务类国际互联网站，即使任何一个普通公司里使用电脑的文员、任何一台为公司效力的电脑，我们是否真正可以仅仅就使用 Ghost 来帮我们解决一切问题？先把这个问...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn47"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn47=47,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年9月19日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/39.html">贴纸设计 NDSL 贴纸作品</a></h2>
	<div class="post-body">最近论坛在做一个 DSL 贴纸设计大赛，我就去凑凑热闹了，主要是活跃人气，参与为主，所以并没有花费很多时间在这个贴纸，重在参与咯</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:3 | 引用:0 | 浏览:<span id="spn39"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn39=39,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年9月16日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/38.html">思科 Cisco Linksys WRT54GC v2 版无线路由升级步骤</a></h2>
	<div class="post-body">思科 Cisco Linksys WRT54GC v2 版无线路由最新固件下载地址及更新固件步骤...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn38"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn38=38,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年9月13日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/36.html">活动你的手指，男人游戏推荐！</a></h2>
	<div class="post-body">我自己以前中文化的两个小游戏，原版是日文的比较难懂，于是做了中文化，这次把障碍菜单独立到菜单栏便于设置，闲来无事的时候推荐大家活动一下手指，都来男人一回^_^，游戏虽然简单，很好玩哦...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn36"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn36=36,"</script>
	</h6>
</div>
<div class="post pagebar">分页:<a href="http://starain.net.cn/blog/catalog.asp?date=2007-9&amp;page=1">[&laquo;]</a><span class="now-page">1</span><a href="http://starain.net.cn/blog/catalog.asp?date=2007-9&amp;page=2">[2]</a><a href="http://starain.net.cn/blog/catalog.asp?date=2007-9&amp;page=2">[&raquo;]</a></div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div><script language="JavaScript" src="http://starain.net.cn/blog/function/c_html_js.asp?date=2007-9" type="text/javascript"></script></div>
</div>

<div class="function" id="divCatalog">
<h3>网站目录</h3>
<ul>
<li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=2" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_2.html">默认分类<span class="article-nums"> (0)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=4" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_4.html">技术文档<span class="article-nums"> (7)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=5" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_5.html">休闲影音<span class="article-nums"> (3)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=6" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_6.html">一些作品<span class="article-nums"> (30)</span></a></li>
</ul>
</div>

<div class="function" id="divArchives">
<h3>文章归档</h3>
<ul>
<li><a href="http://starain.net.cn/blog/post/2007_10.html">2007 October (1)</a></li><li><a href="http://starain.net.cn/blog/post/2007_9.html">2007 September (7)</a></li><li><a href="http://starain.net.cn/blog/post/2007_8.html">2007 August (9)</a></li>
</ul>
</div>

<div class="function" id="divTags">
<h3>Tags</h3>
<ul>
<li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计 (12)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作 (8)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计 (4)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%A0%87%E8%AF%86%E8%AE%BE%E8%AE%A1">标识设计 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护 (2)</a></li>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>