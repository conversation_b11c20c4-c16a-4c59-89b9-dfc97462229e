﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-Adobe Photoshop CS3 Extended</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate4 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/15.html">&laquo; 网站留言模块</a><a class="r" href="http://starain.net.cn/blog/post/7.html">歌曲《甜甜圈》——小熏 阿本 &raquo;</a></div>
	<h4 class="post-date">2007-8-9 14:23:30</h4>
	<h2 class="post-title">Adobe Photoshop CS3 Extended</h2>
	<div class="post-body"><p>经过一天的使用，总的来说跟 Photoshop&nbsp;CS2 的操作模式没有很多区别，习惯 CS2 版本的用户可以放心方便的过渡到 CS3 ，通过我自己的使用发现了一些有意思的地方，现分享给大家：<br /><br /><font color="#3366ff">1、调板、界面、屏幕模式：<br /></font>本次对调板的界面调整非常科学和符合大众习惯，其中不难看到借鉴了 Macomedia Studio 8 中的一些思想，调板仍可以任意组合停靠，但将自动占据整个屏幕高度，你可以不用一直去调整各个高度，Ps 会自动帮你调整。工具箱可以单列、双列显示，甚至可以直接停靠到整个调板的右边，这样有利于整个工作区的完整化。<br />比较有意思的地方是&ldquo;更改屏幕模式（F）&rdquo;里面多出了一种叫做&ldquo;最大化屏幕模式&rdquo;的模式，非常棒，习惯 Macomedia 产品的用户会发现这种模式完全取材于 Macomedia 8 系列，这种模式可以不用担心画布会被调板区覆盖而带来的操作困难，推荐大家使用这种模式操作。<br /><br /><font color="#3366ff">2、快速选择工具（W）、调整边缘：<br /></font>细心的用户会发现以前&ldquo;魔棒工具（W）&rdquo;里面多出了这个&ldquo;快速选择工具&rdquo;，大家可以把这个工具看作是&ldquo;魔棒&rdquo;的升级版，而该工具会非常酷！，虽然同样基于魔棒运算，但&ldquo;快速选择工具&rdquo;可以快速的相似的多色彩区域，其操作结果相当于我们使用&ldquo;魔棒&rdquo;多次选取然后&ldquo;相加&rdquo;的效果，大家可以多多摸索，该工具会大量提高工作效率。<br />值得说明的是&ldquo;调整边缘...&rdquo;选项，本次升级针对所有的选区工具（如：矩形工具、套索、魔棒等等）增加了一个叫做&ldquo;调整边缘&rdquo;的选项，该选项位于&ldquo;选项&rdquo;面板（丛老师叫&ldquo;属性&rdquo;面板），使用选区工具选择区域后，点击&ldquo;调整边缘...&rdquo;按钮会调出调整边缘对话框，可以针对选区的：半径、对比度、平滑、羽化、收缩扩展 等选项进行细微调整，附带的几种预览模式可以让你方便清晰的看到选区即时状态，强烈推荐使用。<br /><br /><font color="#3366ff">3、黑白调整：<br /></font>我们可以认为是&ldquo;去色&rdquo;的升级版，命令位于&ldquo;图像－调整－黑白（Alt＋Shift＋Ctrl＋B）...&rdquo;，执行后会调出黑白选项卡，简单的说，利用这个工具我们可以&ldquo;调整图像中任意颜色的个别灰度值&rdquo;，平时用途不大，但使用的时候会给你带来极大的方便。<br /><br /><font color="#3366ff">4、智能对象：<br /></font>这个功能是我认为 CS3 版本中最革命性的升级，如果我告诉你，我把一段文字做任意修改（如：变形、切变、甚至使用滤镜！）之后我们仍可以修改文本的内容、颜色、字体等等等等，并能将修改后的文本内容即时反映到你的最终文档，你会怎么想。老早以前我们就在期待不是嘛，CS3 中使用了一种&ldquo;智能对象&rdquo;的方法来实现这一切，不仅仅是文字，甚至图像、图形、都是可以的，我们可以在矢量图形上添加滤镜后仍能修改该图形的形状、锚点等等。<br />具体命令在&ldquo;图层－智能对象－转化为智能对象&rdquo;如果不能理解的话，我们可以举这么个例子，智能图像有点类似于Office系列软件里面的SmartArt，其实我自己认为 PsCS3 中的智能对象更像我们在 Flash 里面用到的&ldquo;元件&rdquo;，对，非常贴切，我们先把一个&ldquo;对象&rdquo;（可以是文字，图形，图像，矢量等）转化为一个&ldquo;智能对象&rdquo;（类似元件），我们可以多次使用这个&ldquo;智能对象&rdquo;（和对这个对象使用多次滤镜等等），一旦我们去修改这个&ldquo;智能对象&rdquo;（元件），那么所有使用到该&ldquo;智能对象&rdquo;的地方将进行同步更新，有点像我们编程里面的&ldquo;组件&rdquo;，这样将利于我们&ldquo;并行&rdquo;去工作，因为你的团队每个人都可以使用这个&ldquo;智能对象&rdquo;达到事半功倍的效果。<br /><br />暂时，我个人仅仅看到了这些，大家可以来补充&hellip;&hellip;让我们所有人成为一个集体，体现团队的力量，共同进步。</p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:4 | 引用:0 | 浏览:<span id="spn5"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn5=5,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=5" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt5">1</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h7t5t5p2%3A3%2F7%2F0b7l8o7g0%2E4s8t7a3r9a8i0n9%2E3c5o7m0%2E5c4n2%2F6" rel="nofollow" target="_blank">http://blog.starain.com.cn/</a></li>
	<li class="msgarticle">5、仿制图章工具：Photoshop CS3 针对图章工具进行了强化，我们可以同时定义最多五个“仿制源”，并为每个仿制源定制“透明度、自动隐藏、位移、长宽比例、反相”等参数，并可以“显示叠加”，即时看到修改后的预览与各个方向的修改预测，具体命令位于“窗口－仿制源”打开“仿制源调板”。</li>
	<li class="msgtime">2007-8-9 16:24:33&nbsp;<a href="#comment" onclick="RevertComment('5')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt6">2</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h9t4t2p1%3A1%2F6%2F4b4l7o3g6%2E2s1t5a0r4a9i2n7%2E3c2o9m6%2E6c4n0%2F5" rel="nofollow" target="_blank">http://blog.starain.com.cn/</a></li>
	<li class="msgarticle">Tips:在菜单“编辑－首选项－显示－常规”栏目里面建议勾选“使用灰度工具栏图标”，这样可以避免彩色图标对我们设计时的干扰与误差……</li>
	<li class="msgtime">2007-8-9 16:34:52&nbsp;<a href="#comment" onclick="RevertComment('6')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt8">3</a>.<a>常静</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=" rel="nofollow" target="_blank"></a></li>
	<li class="msgarticle">已经使用cs3系列快两个月了...除了是英文版以外，其它的都还可以。界面要比以前强很多，工具栏，菜单面板的设计理念也很符合adobe一贯的风格。AS3.0因为不会所以没办法评价FLASH的好坏，但是DW真的是有很大的突破，更接近于标准化了，代码功能更强大，甚至还加入了常用的JS，和AJAX~~（虽然代码仍没有很好的优化），但相对于以前来说是更方便了。PHOTO嘛~~~没啥感觉，和CS2也差不了多少，但总体来说还是不错的，尤其是在面对更换vista的时候~~~</li>
	<li class="msgtime">2007-8-10 9:17:03&nbsp;<a href="#comment" onclick="RevertComment('8')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt9">4</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h9t6t5p3%3A1%2F7%2F4b7l5o8g0%2E2s0t1a3r1a0i5n6%2E5c8o0m1%2E6c4n3%2F1" rel="nofollow" target="_blank">http://blog.starain.com.cn/</a></li>
	<li class="msgarticle">我仍然认为 Vista 在未来一年内不是我们推荐使用的操作系统，或者至少要等到 Service Pack 1 的出现，作为设计者，我们不需要拥有华丽的窗口切换和绚丽色彩的“花瓶”，我们需要一个朴素到不至于影响我们视觉感和色彩感的核心操作系统，对于操作系统我本人仍然推荐 WindowsXP 虽然不像 2000&amp;2003 那么高的效率，但毕竟可以兼顾所有的用户习惯，与其现在去摸索 Vista 里面复杂的设置项目，不如，干脆等 2009 年下半年的 Windows 7 （Blackcomb） 微软自己也表示，Vista只是一个过渡，像 Windows Media Center 一样，只是一个过渡，希望大家在盲目追求新版本的同时，找到真正适合自己使用的“经典之作”，就像我，现在仍钟情于 ACD See 3.1 一样。</li>
	<li class="msgtime">2007-8-10 13:19:26&nbsp;<a href="#comment" onclick="RevertComment('9')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=6d1d8878" >
	<input type="hidden" name="inpId" id="inpId" value="5" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-10-7 1:43:39 -->