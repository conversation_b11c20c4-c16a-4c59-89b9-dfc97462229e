﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-贴纸设计 NDSL 贴纸作品</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate6 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/38.html">&laquo; 思科 Cisco Linksys WRT54GC v2 版无线路由升级步骤</a><a class="r" href="http://starain.net.cn/blog/post/47.html">对于国内 IT 业的非“杞人忧天”式担忧！ &raquo;</a></div>
	<h4 class="post-date">2007-9-19 23:56:20</h4>
	<h2 class="post-title">贴纸设计 NDSL 贴纸作品</h2>
	<div class="post-body"><p>最近论坛在做一个 DSL 贴纸设计大赛，我就去凑凑热闹了，主要是活跃人气，参与为主，所以并没有花费很多时间在这个贴纸，重在参与咯。</p><p>贴纸主题为几米先生的经典动漫《向左走&middot;向右走》，整体风格以清爽为主，没有很多花哨的装饰，跟冰蓝版的 DSL 会非常搭配，希望大家能喜欢。</p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709192356591064.JPG" alt="" title=""/></p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:3 | 引用:0 | 浏览:<span id="spn39"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn39=39,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=39" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"><p><a  href="http://starain.net.cn/blog/post/25.html">腾讯 QQ DS、TM DS、skype DS 卡带设计</a>&nbsp;&nbsp;(2007-8-27 23:52:51)</p><p><a  href="http://starain.net.cn/blog/post/20.html">教你用 Photoshop 做花布 技术文档</a>&nbsp;&nbsp;(2007-8-25 15:21:17)</p><p><a  href="http://starain.net.cn/blog/post/5.html">Adobe Photoshop CS3 Extended</a>&nbsp;&nbsp;(2007-8-9 14:23:30)</p><p><a  href="http://starain.net.cn/blog/post/21.html">生烤牛肉丁包装</a>&nbsp;&nbsp;(2007-3-22 16:17:20)</p><p><a  href="http://starain.net.cn/blog/post/22.html">手机喷绘</a>&nbsp;&nbsp;(2007-3-15 16:28:42)</p></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt31">1</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h5t1t6p9%3A4%2F9%2F6s6t2a9r1a6i4n6%2E0n7e6t9%2E1c7n6%2F7" rel="nofollow" target="_blank">http://starain.net.cn/</a></li>
	<li class="msgarticle">不经意为之竟为我赢得 YYJoy Diy 的邀请，很开心，毕竟是自己喜欢并希望为之贡献力量的团队，大家加油！</li>
	<li class="msgtime">2007-10-12 1:37:48&nbsp;<a href="#comment" onclick="RevertComment('31')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt32">2</a>.<a>桃花依旧笑春风</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=" rel="nofollow" target="_blank"></a></li>
	<li class="msgarticle">COME ON!<br/>貌似很漂亮的样子，希望有天出现在市面上。</li>
	<li class="msgtime">2007-10-16 20:51:37&nbsp;<a href="#comment" onclick="RevertComment('32')">回复该留言</a></li>
</ul><ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt33">3</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h0t1t5p1%3A0%2F3%2F7s1t8a3r1a6i7n8%2E7n7e5t0%2E7c8n6%2F8" rel="nofollow" target="_blank">http://starain.net.cn/</a></li>
	<li class="msgarticle">今天公布了获奖名单，里面有我的这副作品。<br/>被人肯定的感觉，还是很好的，^_^。</li>
	<li class="msgtime">2007-10-19 18:24:40&nbsp;<a href="#comment" onclick="RevertComment('33')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=969cc635" >
	<input type="hidden" name="inpId" id="inpId" value="39" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-10-19 13:20:44 -->