﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-技术文档</title>
</head>
<body class="multi catalog">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
 <div class="post cate4 auth1">
	<h4 class="post-date">2007年10月2日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/53.html">手工整合 Microsoft Office 2003 Service Pack 3 安装光盘</a></h2>
	<div class="post-body">微软 9 月 17 日发布了 Microsoft Office 2003 Service Pack 3 补丁包，我们教大家手工将 Service Pack 3 整合到已经有的 Office 2003 安装光盘...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn53"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn53=53,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年9月24日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/47.html">对于国内 IT 业的非“杞人忧天”式担忧！</a></h2>
	<div class="post-body">前些日子跟一个电子商务网站技术总监的简短交谈让我看到了来自国内一部分 IT 公司在安全及系统维护问题上的缩影，其现状相当令人担忧。当谈到如何来维护整个公司计算机和服务器及网络设备时，总监先生告诉我 Ghost 就行，电脑变慢了，系统故障了，只要 Ghost 就行了&hellip;&hellip;我们且不说这是个做为 IT 领头羊的电子商务类国际互联网站，即使任何一个普通公司里使用电脑的文员、任何一台为公司效力的电脑，我们是否真正可以仅仅就使用 Ghost 来帮我们解决一切问题？先把这个问...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn47"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn47=47,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年9月16日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/38.html">思科 Cisco Linksys WRT54GC v2 版无线路由升级步骤</a></h2>
	<div class="post-body">思科 Cisco Linksys WRT54GC v2 版无线路由最新固件下载地址及更新固件步骤...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn38"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn38=38,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年8月25日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/20.html">教你用 Photoshop 做花布 技术文档</a></h2>
	<div class="post-body"> 本教程将用简单的方法教会你制作一些花纹，这样你可以为你的平面或者网页作品锦上添花，掌握之后可以举一反三制作出更丰富的一些花纹类型，到时候别忘了发布给大家分享哦。</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn20"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn20=20,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年8月9日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/5.html">Adobe Photoshop CS3 Extended</a></h2>
	<div class="post-body">原创文章：体验 Adobe Photoshop CS3 的四个新特性，暂时只发现这些，以后慢慢添加</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:4 | 引用:0 | 浏览:<span id="spn5"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn5=5,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年7月30日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/40.html">配置文档 SMC 7804WBRA 无线有线调制解调路由器</a></h2>
	<div class="post-body">SMC 7804WBRA 是一款功能非常强大的民用路由设备，本身相当于 调制解调器＋路由器＋无线AP＋四口交换机了解这些设备的人会很容易看出，现在家里如果能有这么一个设备，直接将电话线接入 SMC 7804WBRA 然后便可同时使用四台有线网卡计算机（普通台式机）和上百台无线网计算机（迅驰系列笔记本）共享宽带网，而事实也确实如此，实际使用中根本不需要再使用其它网络设备便可以很容易的共享上网，从此可以告别一大堆的网线了&hellip;&hellip;但因为配置界面的英文的并夹杂很多网络专用缩写词语...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:1 | 引用:0 | 浏览:<span id="spn40"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn40=40,"</script>
	</h6>
</div>
<div class="post pagebar">分页:<a href="http://starain.net.cn/blog/catalog.asp?cate=4&amp;page=1">[&laquo;]</a><span class="now-page">1</span><a href="http://starain.net.cn/blog/catalog.asp?cate=4&amp;page=2">[2]</a><a href="http://starain.net.cn/blog/catalog.asp?cate=4&amp;page=2">[&raquo;]</a></div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div><div class="year2007 month10"><p class="y"><a href="http://starain.net.cn/blog/catalog.asp?date=2007-9">&lt;&lt;</a>  <a href="http://starain.net.cn/blog/catalog.asp?date=2007-10">2007-10</a>  <a href="http://starain.net.cn/blog/catalog.asp?date=2007-11">&gt;&gt;</a></p><p class="w">Sun</p><p class="w">Mon</p><p class="w">Tue</p><p class="w">Wed</p><p class="w">Thu</p><p class="w">Fri</p><p class="w">Sat</p><p class="nd"></p><p id="pCalendar_2007_10_1" class="d">1</p><p id="pCalendar_2007_10_2" class="yd"><a class="l" href="http://starain.net.cn/blog/catalog.asp?date=2007-10-2">2</a></p><p id="pCalendar_2007_10_3" class="d">3</p><p id="pCalendar_2007_10_4" class="d">4</p><p id="pCalendar_2007_10_5" class="d">5</p><p id="pCalendar_2007_10_6" class="d">6</p><p id="pCalendar_2007_10_7" class="d">7</p><p id="pCalendar_2007_10_8" class="d">8</p><p id="pCalendar_2007_10_9" class="d">9</p><p id="pCalendar_2007_10_10" class="d">10</p><p id="pCalendar_2007_10_11" class="d">11</p><p id="pCalendar_2007_10_12" class="d">12</p><p id="pCalendar_2007_10_13" class="d">13</p><p id="pCalendar_2007_10_14" class="d">14</p><p id="pCalendar_2007_10_15" class="d">15</p><p id="pCalendar_2007_10_16" class="d">16</p><p id="pCalendar_2007_10_17" class="d">17</p><p id="pCalendar_2007_10_18" class="d">18</p><p id="pCalendar_2007_10_19" class="d">19</p><p id="pCalendar_2007_10_20" class="d">20</p><p id="pCalendar_2007_10_21" class="d">21</p><p id="pCalendar_2007_10_22" class="d">22</p><p id="pCalendar_2007_10_23" class="d">23</p><p id="pCalendar_2007_10_24" class="d">24</p><p id="pCalendar_2007_10_25" class="d">25</p><p id="pCalendar_2007_10_26" class="d">26</p><p id="pCalendar_2007_10_27" class="d">27</p><p id="pCalendar_2007_10_28" class="d">28</p><p id="pCalendar_2007_10_29" class="d">29</p><p id="pCalendar_2007_10_30" class="d">30</p><p id="pCalendar_2007_10_31" class="d">31</p><p class="nd"></p><p class="nd"></p><p class="nd"></p></div></div>
</div>

<div class="function" id="divCatalog">
<h3>网站目录</h3>
<ul>
<li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=2" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_2.html">默认分类<span class="article-nums"> (0)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=4" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_4.html">技术文档<span class="article-nums"> (7)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=5" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_5.html">休闲影音<span class="article-nums"> (3)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=6" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_6.html">一些作品<span class="article-nums"> (30)</span></a></li>
</ul>
</div>

<div class="function" id="divArchives">
<h3>文章归档</h3>
<ul>
<li><a href="http://starain.net.cn/blog/post/2007_10.html">2007 October (1)</a></li><li><a href="http://starain.net.cn/blog/post/2007_9.html">2007 September (7)</a></li><li><a href="http://starain.net.cn/blog/post/2007_8.html">2007 August (9)</a></li>
</ul>
</div>

<div class="function" id="divTags">
<h3>Tags</h3>
<ul>
<li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计 (12)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作 (8)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计 (4)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%A0%87%E8%AF%86%E8%AE%BE%E8%AE%A1">标识设计 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护 (2)</a></li>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>