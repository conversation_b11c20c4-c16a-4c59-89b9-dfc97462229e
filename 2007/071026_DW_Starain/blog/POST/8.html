﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-UltraISO 8.6.3.2056 单文件绿色版</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate6 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/7.html">&laquo; 歌曲《甜甜圈》——小熏 阿本</a><a class="r" href="http://starain.net.cn/blog/post/9.html">MV《Lost Without You》——Delta Goodrem &raquo;</a></div>
	<h4 class="post-date">2007-8-13 10:48:30</h4>
	<h2 class="post-title">UltraISO 8.6.3.2056 单文件绿色版</h2>
	<div class="post-body"><p>UltraISO 8.6.3.2056 原创 绿色 单文件版<br /><br />软件大小：1.35M<br />软件类别：光碟工具<br />界面语言：中文英文<br />发布类型：绿色、单文件、共享版<br />运行环境：Win9x/Me/NT/2000/XP/2003<br />官方链接：<a target="_blank" href="http://www.ultraiso.com/">http://www.ultraiso.com/</a><br /><br />详细信息：UltraISO软碟通是一款功能强大而又方便实用的软碟文件制作/编辑/转换工具，它可以直接编辑软碟文件和从软碟中提取文件，也可以从CD-ROM直接制作软碟或者将硬盘上的文件制作成ISO文件。同时，你也可以处理ISO文件的启动信息，从而制作可引导光盘。使用UltraISO，你可以随心所欲地制作/编辑软碟文件，配合光盘刻录软件烧录出自己所需要的光碟。<br />本人在国外破解版的基础上制作了绿色单文件版更方便使用。本软体仅限于下载研究学习之用，并请于下载后24小时内删除，如用于商业用途，请联系开发商购买。<br />下载地址：<a href="http://starsoft.ys168.com/">http://starsoft.ys168.com/</a> 本人作品上传目录建议下载后查杀病毒（常识）并做指纹效验：<br /><br />File:UltraISO.exe<br />Size: 1421032 bytes<br />File Version: 8.6.3.2056<br />Modified: 2007年8月13日, 10:43:18<br />MD5: 5068D39B2E59067878110A170100D0E4<br />SHA1: DD22BE6E85B23AC9D3C00DA95C10CA89FEC85AC1<br />CRC32: 577246B5</p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:1 | 引用:0 | 浏览:<span id="spn8"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn8=8,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=8" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt18">1</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h6t8t4p1%3A8%2F3%2F3s7t2a4r2a8i6n3%2E3n8e5t9%2E5c3n8%2F2" rel="nofollow" target="_blank">http://starain.net.cn/</a></li>
	<li class="msgarticle">结果太绿色了，语言选择信息都不会保存到注册表，每次启动还要重新设置语言，觉得麻烦的话可以把以下内容保存为reg文件导入注册表：<br/><br/>Windows Registry Editor Version 5.00<br/>[HKEY_CURRENT_USER\Software\EasyBoot Systems\UltraISO\5.0]<br/>@=&quot;D:\\Program Files\\UltraISO&quot;<br/>&quot;Language&quot;=&quot;2052&quot;<br/><br/>很容易看得出来什么意思吧。^_^</li>
	<li class="msgtime">2007-8-20 23:18:50&nbsp;<a href="#comment" onclick="RevertComment('18')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=2559e581" >
	<input type="hidden" name="inpId" id="inpId" value="8" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-9-30 1:47:52 -->