﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-网页改版，主要针对页面宽度</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate6 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/30.html">&laquo; 呼朋唤友网站首页</a><a class="r" href="http://starain.net.cn/blog/post/46.html">时装临摹 巡航线留念 &raquo;</a></div>
	<h4 class="post-date">2007-4-21 15:59:38</h4>
	<h2 class="post-title">网页改版，主要针对页面宽度</h2>
	<div class="post-body"><p>由于越来越多的计算机使用更高分辨率的显示器，以前常用于 800&times;600 分辨率的网页，现在与我们普遍使用的 1024&times;768 和 1280&times;1024 不大相衬，于是我使用动态页面宽度对以往 800&times;600 的页面进行了改版重排使其能够自动适应多种平面分辨率。<br />其主要难点在于固定宽度图片的伸缩问题，为此，我选取了小部分近似循环出现的图片内容作为动态宽度区域的背景部分，然后对该部分做了稍微修饰，整体效果很好，但如果仔细去观察，会发现那一小部分是重复循环出现的。<br />整体效果看来非常好，旧版的页面是固定 1.1 比值的类正方形页面，改版之后我在 1024&times;768 的分辨率下获得了 1.54 的页面比例，单屏可完整显示，也更加符合人眼阅读习惯。</p><p>改版之前的效果如下：</p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709181602456441.jpg" alt="" title=""/></p><p>改版之后的效果如下：</p><p><img onload="ResizeImage(this,525)" src="http://starain.net.cn/blog/upload/200709181602520136.jpg" alt="" title=""/></p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn28"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn28=28,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=28" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"><p><a  href="http://starain.net.cn/blog/post/15.html">网站留言模块</a>&nbsp;&nbsp;(2007-8-5 19:3:23)</p><p><a  href="http://starain.net.cn/blog/post/27.html">告别我的旧站点</a>&nbsp;&nbsp;(2007-6-10 15:23:59)</p></li>
</ul>

<div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=150eb485" >
	<input type="hidden" name="inpId" id="inpId" value="28" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-9-30 1:47:52 -->