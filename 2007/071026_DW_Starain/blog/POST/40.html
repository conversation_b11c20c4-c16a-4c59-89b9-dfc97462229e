﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-配置文档 SMC 7804WBRA 无线有线调制解调路由器</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate4 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/41.html">&laquo; 简历封面设计</a><a class="r" href="http://starain.net.cn/blog/post/19.html">音乐媒体播放器 界面 &raquo;</a></div>
	<h4 class="post-date">2007-7-30 1:14:28</h4>
	<h2 class="post-title">配置文档 SMC 7804WBRA 无线有线调制解调路由器</h2>
	<div class="post-body"><p>SMC 7804WBRA 是一款功能非常强大的民用路由设备，本身相当于 调制解调器＋路由器＋无线AP＋四口交换机</p><p>了解这些设备的人会很容易看出，现在家里如果能有这么一个设备，直接将电话线接入 SMC 7804WBRA 然后便可同时使用四台有线网卡计算机（普通台式机）和上百台无线网计算机（迅驰系列笔记本）共享宽带网，而事实也确实如此，实际使用中根本不需要再使用其它网络设备便可以很容易的共享上网，从此可以告别一大堆的网线了，而且，SMC 公司是经常为美国军队提供军工产品的网络设备商，质量方面可以放心。感觉像在做广告，^_^，先声明，我可不卖这个东西，只是感觉是好东西所以拿出来给要入手无线路由的玩家一些意见和建议&hellip;&hellip;</p><p>但因为配置界面的英文的并夹杂很多网络专用缩写词语，使得普通用户无法正确的配置该路由器，导致经常掉线，脱网的后果，由于网上发布的前进网络的配置文档过于简单，我把自己写的配置文档放上来，希望对入手该款路由器的玩家有所帮助，文档中图片是直接用的源文档图片。</p><p>直接查看文档地址：<font color="#095c83"><a href="http://starain.net.cn/docx/20070730.html">http://starain.net.cn/docx/20070730.html</a></font></p><p>技术文档下载地址：<a href="http://starsoft.ys168.com/"><font color="#095c83">http://starsoft.ys168.com/</font></a></p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:1 | 引用:0 | 浏览:<span id="spn40"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn40=40,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=40" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"><p><a  href="http://starain.net.cn/blog/post/38.html">思科 Cisco Linksys WRT54GC v2 版无线路由升级步骤</a>&nbsp;&nbsp;(2007-9-16 23:57:35)</p><p><a  href="http://starain.net.cn/blog/post/18.html">家庭网与接入网的协同技术 论文</a>&nbsp;&nbsp;(2007-6-14 13:29:45)</p></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt30">1</a>.<a>同学</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=" rel="nofollow" target="_blank"></a></li>
	<li class="msgarticle">这些是你自己去找的迈?<br/>一天那来嫩个多时间去写哟,  也看不出来你还是学了黑多东西哈<div class="quote quote3"><div class="quote-title">starain 于 2007-10-12 1:00:04 回复</div>我可是花了好几百块钱买的这款路由呢，要是不把功能全部发挥出来，多浪费啊，^_^，……</div></li>
	<li class="msgtime">2007-10-12 1:00:04&nbsp;<a href="#comment" onclick="RevertComment('30')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=10872bee" >
	<input type="hidden" name="inpId" id="inpId" value="40" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-10-12 0:51:10 -->