﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-歌曲《甜甜圈》——小熏 阿本</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate5 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/5.html">&laquo; Adobe Photoshop CS3 Extended</a><a class="r" href="http://starain.net.cn/blog/post/8.html">UltraISO 8.6.3.2056 单文件绿色版 &raquo;</a></div>
	<h4 class="post-date">2007-8-11 21:46:32</h4>
	<h2 class="post-title">歌曲《甜甜圈》——小熏 阿本</h2>
	<div class="post-body">推荐歌曲《甜甜圈》来自&mdash;&mdash;小熏、阿本<object id="MediaPlayer1" codebase="http://activex.microsoft.com/activex/controls/mplayer/en/nsmp2inf.cab#Version=6,4,7,1112" type="application/x-oleobject" height="65" standby="Loading Microsoft Windows Media Player components..." width="530" align="baseline" border="0" classid="CLSID:6BF52A52-394A-11d3-B153-00C04F79FAA6"><param value="http://**************/tiantian.mp3" name="URL" /><param value="true" name="autoStart" /><param value="false" name="invokeURLs" /><param value="100" name="playCount" /><param value="datawindow" name="defaultFrame" /></object></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:休闲影音 | 评论:1 | 引用:0 | 浏览:<span id="spn7"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn7=7,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=7" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt13">1</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h6t3t1p1%3A0%2F4%2F9b5l4o9g2%2E3s3t2a5r1a5i9n5%2E9c6o4m6%2E0c7n7%2F4" rel="nofollow" target="_blank">http://blog.starain.com.cn/</a></li>
	<li class="msgarticle">Tips：顺便把歌词摆上来咯……<br/><br/>甜甜圈<br/><br/>演唱：小熏、阿本<br/><br/>（熏）天天非常想你的甜点  飞过城市的边缘  降落爱你的终点<br/>想念互道晚安的每一天  却又舍不得说再见  你的笑那么甜<br/>我的直觉就是那么坚决  不怕有暴风圈  因为你是我最晴朗的大晴天<br/>爱要你牵我的手每一天  我要非常用心感觉<br/>因为爱你  才是我最后最美丽的句点<br/>喜欢在你的肩膀盘旋  习惯两个人的世界<br/>因为爱你  才让我的心永远像甜甜圈<br/><br/>（本）牵你的手还是有点紧张<br/>看你双眼怎么好好说话  这次真的下定决心  鼓起勇气拥抱你<br/>让我们的爱连成完美圈圈<br/>曾经怀疑自己是否能保护你<br/>是你让我看见世界美丽  是你带我走向温暖生命<br/>因为有你我的天空放晴<br/>紧握住你的手一起迎接未来<br/>十八岁的蔚蓝环绕永远的爱<br/><br/>（熏）天天非常想你的甜点  飞过城市的边缘  降落爱你的终点<br/>想念互道晚安的每一天  却又舍不得说再见  你的笑那么甜<br/>我的直觉就是那么坚决  不怕有暴风圈  因为你是我最晴朗的大晴天<br/>爱要你牵我的手每一天  我要非常用心感觉<br/>因为爱你  才是我最后最美丽的句点<br/>喜欢在你的肩膀盘旋  习惯两个人的世界<br/>因为爱你  才让我的心永远像甜甜圈<br/><br/>（本）是否能够继续  因为你能让我真正充满勇气<br/>有你才可以真正面对自己  一起面对未来十八岁的蔚蓝<br/>两人一起拥抱永远的爱<br/>（熏）爱要你牵我的手每一天  我要非常用心感觉<br/>因为爱你  才是我最后最美丽的句点<br/>喜欢在你的肩膀盘旋  习惯两个人的世界<br/>因为爱你  才让我的心永远像甜甜圈<br/></li>
	<li class="msgtime">2007-8-11 22:10:58&nbsp;<a href="#comment" onclick="RevertComment('13')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=ea8fb2f4" >
	<input type="hidden" name="inpId" id="inpId" value="7" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-9-30 1:47:52 -->