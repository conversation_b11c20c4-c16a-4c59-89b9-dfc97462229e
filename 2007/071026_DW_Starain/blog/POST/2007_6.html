﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-2007 June</title>
</head>
<body class="multi catalog">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
 <div class="post cate6 auth1">
	<h4 class="post-date">2007年6月27日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/29.html">移动通信某分公司管理登陆页面</a></h2>
	<div class="post-body">我一位做后台的朋友让帮忙设计的一个某移动公司的管理层应用，后台实现由他来做，我仅仅设计了首页效果和一些静态实现，其实首页看似要简单的多但做的时候却花费了比二级页面多的多的时间，每个文案，每个线条，每个图片，一个像素的位置差异出来的效果都有很多不同...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn29"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn29=29,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年6月14日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/18.html">家庭网与接入网的协同技术 论文</a></h2>
	<div class="post-body">自2006年04月，国家主席胡锦涛同志访问美国参观微软&ldquo;未来之家&rdquo;，并对其高度赞扬以来，国内的相关行业也呈现出一幅繁荣景象。这个位于西雅图市市郊的雷德蒙&ldquo;未来之家&rdquo;，展示了微软认为将在5到10年内实现的智能居所模式。踏进&ldquo;未来之家&rdquo;的客厅，呈现在胡锦涛眼前的一个荧光屏，随即展示了一个典型家庭的数码照片。整个显示系统的开关竟然是一只花瓶！只要移动一个花瓶，照片便自动换成胡锦涛曾居住或工作过的地方，包括北京、西藏和他就读的清华大...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn18"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn18=18,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年6月10日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/27.html">告别我的旧站点</a></h2>
	<div class="post-body">今天正是停止我的站点，打算重新设计站点页面。</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn27"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn27=27,"</script>
	</h6>
</div>
<div class="post pagebar">分页:<a href="http://starain.net.cn/blog/catalog.asp?date=2007-6&amp;page=1">[&laquo;]</a><span class="now-page">1</span><a href="http://starain.net.cn/blog/catalog.asp?date=2007-6&amp;page=1">[&raquo;]</a></div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div><script language="JavaScript" src="http://starain.net.cn/blog/function/c_html_js.asp?date=2007-6" type="text/javascript"></script></div>
</div>

<div class="function" id="divCatalog">
<h3>网站目录</h3>
<ul>
<li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=2" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_2.html">默认分类<span class="article-nums"> (0)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=1" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_1.html">我的日志<span class="article-nums"> (1)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=3" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_3.html">软件下载<span class="article-nums"> (1)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=4" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_4.html">技术文档<span class="article-nums"> (4)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=5" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_5.html">休闲影音<span class="article-nums"> (3)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=6" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_6.html">一些作品<span class="article-nums"> (24)</span></a></li>
</ul>
</div>

<div class="function" id="divArchives">
<h3>文章归档</h3>
<ul>
<li><a href="http://starain.net.cn/blog/post/2007_9.html">2007 September (4)</a></li><li><a href="http://starain.net.cn/blog/post/2007_8.html">2007 August (12)</a></li><li><a href="http://starain.net.cn/blog/post/2007_7.html">2007 July (2)</a></li>
</ul>
</div>

<div class="function" id="divTags">
<h3>Tags</h3>
<ul>
<li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作 (9)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计 (6)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计 (4)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%8A%A0%E5%BC%BA">软件加强 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%A0%87%E8%AF%86%E8%AE%BE%E8%AE%A1">标识设计 (1)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护 (1)</a></li>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright starain. Some Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>