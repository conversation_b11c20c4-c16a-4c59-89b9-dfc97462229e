﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-家庭网与接入网的协同技术 论文</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate4 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/27.html">&laquo; 告别我的旧站点</a><a class="r" href="http://starain.net.cn/blog/post/29.html">移动通信某分公司管理登陆页面 &raquo;</a></div>
	<h4 class="post-date">2007-6-14 13:29:45</h4>
	<h2 class="post-title">家庭网与接入网的协同技术 论文</h2>
	<div class="post-body"><p>自2006年04月，国家主席胡锦涛同志访问美国参观微软&ldquo;未来之家&rdquo;，并对其高度赞扬以来，国内的相关行业也呈现出一幅繁荣景象。</p><p>这个位于西雅图市市郊的雷德蒙&ldquo;未来之家&rdquo;，展示了微软认为将在5到10年内实现的智能居所模式。踏进&ldquo;未来之家&rdquo;的客厅，呈现在胡锦涛眼前的一个荧光屏，随即展示了一个典型家庭的数码照片。整个显示系统的开关竟然是一只花瓶！只要移动一个花瓶，照片便自动换成胡锦涛曾居住或工作过的地方，包括北京、西藏和他就读的清华大学。<br />智能厨房应用了RFID技术和其它无线技术，是胡锦涛和夫人刘永清相当感兴趣的地方。刘永清从橱里拿了一包面粉，电脑系统立即显示连串可用面粉制作的菜肴食谱。如果面粉用完了，这个智慧厨房还会提醒主人，记得把面粉列在购物单上。胡锦涛笑问，有了无线识别（RFID）技术，还需要管家吗？书房的设计也叫胡锦涛赞叹不已。墙上的大型荧幕，可以让正在做天文学功课的学生，立即获取某个星球的互动照片，所需的资讯尽在弹指之间。另外微软人员还向胡锦涛演示了透过平板电脑呈现出另一头正在亚洲进行的研究工作，胡锦涛说，用普通打字的方式很难打出方程式，有了触控笔直接在电脑上书写就简单多了。微软&ldquo;未来之家&rdquo;的更衣室也大有玄机，一个看似平平无奇的镜子却是用电脑控制的神奇&ldquo;魔镜&rdquo;，能够在主人拿出衣服后展示当天的气温，以及穿这件衣服是否合适；甚至还可以向主人建议应该怎样搭配衣服。此外还有智能家居地板，地板中的传感器能在15厘米内跟踪到人的足迹，在感应到人来时会自动打开照明系统，在离去时自动关闭。微软发言人热洛斯说，&ldquo;未来之家&rdquo;是胡锦涛特别指定要参观的地方，他被展示内容&ldquo;深深吸引&rdquo;，在每一个参观阶段都会发问，参观行程也比原先估计的多了15分钟。<br />胡锦涛在结束访问前说：&ldquo;比尔盖茨是中国的朋友，我是微软的朋友。&rdquo;他也告诉记者，他对这次的行程&ldquo;留下深刻印象&rdquo;。胡锦涛说：&ldquo;我尤其感到高兴的是，微软和中国有着重要的合作关系。我本身和比尔盖茨都认为，这方面的合作应该扩大，而且应该搞得越来越好。&rdquo;（以上材料来自&ldquo;搜狐新闻&rdquo;）</p><p>而这些智能家居其最基本的技术依托便是强大的网络技术，具体来说便是家庭网（Home Network）、接入网（Access Network）的协同工作，我的文章也就这些相关技术做了一些阐述，并针对当前国内该行业比较突出的问题发表了一些自己的建议，希望能给准备架设智能家居网络的人们以一定的参考价值，同时也给国内相关行业一些建议与意见。</p><p>随本文附上论文答辩演示文稿，如需要论文原文请联系作者。</p><p><a href="http://starain.net.cn/blog/upload/200708251330063013.rar" target="_blank">点击下载论文演示文稿</a></p><p>整理日期2007年08月25日13点</p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn18"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn18=18,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=18" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"></li>
</ul>

<div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=750a39dd" >
	<input type="hidden" name="inpId" id="inpId" value="18" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-9-30 1:47:52 -->