﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title><PERSON><PERSON>'s blog-MV《Lost Without You》——Delta Goodrem</title>
</head>
<body class="single">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strFaceName="Haha|Hehe|Love|Misdoubt|Music|Nothing_to_say|Sad|Shame|Sleep|Smile|Stop|What|Adore|After_boom|Angry|Cool|Cry|Effort|Faint|Grimace";
	var strFaceSize="48";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
<div class="post cate5 auth1">
	<div class="post-nav"><a class="l" href="http://starain.net.cn/blog/post/8.html">&laquo; UltraISO 8.6.3.2056 单文件绿色版</a><a class="r" href="http://starain.net.cn/blog/post/14.html">Windows Preinstallation Environment &raquo;</a></div>
	<h4 class="post-date">2007-8-13 21:22:12</h4>
	<h2 class="post-title">MV《Lost Without You》——Delta Goodrem</h2>
	<div class="post-body"><p><object height="390" width="450" classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"><param value="transparent" name="wmode" /><param value="http://www.56.com/n_v197_/fcs5_/15_/28_/starlight_/zhajm_118701119171x_/246018_/0_/16783299.swf" name="movie" /></object></p><p>Lrc：<br />Lost without you<br />From: Delta Goodrem</p><p>I know I can be a little stubborn sometimes<br />A little righteous and too proud<br />I just want to find a way to compromise<br />Cos I believe that we can work things out</p><p>I thought I had all the answers never giving in<br />But baby since you've gone I admit that I was wrong</p><p>All I know is I'm lost without you I'm not gonna lie<br />How my going to be strong without you I need you by my side<br />If we ever say we'll never be together and we ended with goodbye<br />don't know what I'd do ...</p><p>I'm lost without you<br />I keep trying to find my way<br />but all I know is I'm lost without you<br />I keep trying to face the day I'm lost without you</p><p>How my ever gonna get rid of these blues<br />Baby I'm so lonely all the time<br />Everywhere I go I get so confused<br />You're the only thing that's on my mind<br />Oh my beds so cold at night and I miss you more each day<br />Only you can make it right no I'm not too proud to say</p><p>All I know is I'm lost without you I'm not gonna lie<br />How my going to be strong without you I need you by my side<br />If we ever say we'll never be together and we ended with goodbye<br />don't know what I'd do ...</p><p>I'm lost without you<br />I keep trying to find my way<br />but all I know is I'm lost without you<br />I keep trying to face the day lost without you</p><p>If I could only hold you now<br />and make the pain just go away<br />Can't stop the tears from running down my face</p><p>All I know is I'm lost without you I'm not gonna lie<br />How my going to be strong without you I need you by my side<br />If we ever say we'll never be together and we ended with goodbye<br />don't know what I'd do ...</p><p>I'm lost without you<br />I keep trying to find my way<br />but all I know is I'm lost without you<br />I keep trying to face the day lost without you<br />... ...<br />I'm lost without you... ...</p></div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:休闲影音 | 评论:1 | 引用:0 | 浏览:<span id="spn9"></span>
		<script language="JavaScript" type="text/javascript">strBatchCount+="spn9=9,"</script>
	</h6>
</div>
<ul class="msg trackback">
	<li class="tbname"><a href="http://starain.net.cn/blog/cmd.asp?act=gettburl&id=9" target="_blank">点击这里获取该日志的TrackBack引用地址</a></li>
</ul>
<ul class="msg mutuality">
	<li class="tbname">相关文章:</li>
	<li class="msgarticle"><p><a  href="http://starain.net.cn/blog/post/7.html">歌曲《甜甜圈》——小熏 阿本</a>&nbsp;&nbsp;(2007-8-11 21:46:32)</p></li>
</ul>

<ul class="msg">
	<li class="msgname"><span class="comment-quote-icon"><a onclick="InsertQuote(this.parentNode.parentNode.parentNode.getElementsByTagName('a')[2].innerHTML,this.parentNode.parentNode.parentNode.getElementsByTagName('li')[2].innerHTML);return false;" href=""><img src="http://starain.net.cn/blog/image/common/quote.gif" height="9" width="9" alt="quote" title="quote" /></a></span>&nbsp;<a name="cmt14">1</a>.<a>starain</a></li>
	<li class="msgurl"><a href="http://starain.net.cn/blog/function/c_urlredirect.asp?url=h7t5t5p2%3A3%2F7%2F0b7l8o7g0%2E4s8t7a3r9a8i0n9%2E3c5o7m0%2E5c4n2%2F6" rel="nofollow" target="_blank">http://blog.starain.com.cn/</a></li>
	<li class="msgarticle">来自澳洲的Delta Goodrem ，年仅 18 岁便以单曲《 Born To Try 》、《 Lost Without You 》拿下澳洲排行冠军，被传媒誉为下个明日之星。首支单曲《 Born To Try 》在全英金榜拿下 Top3 ，作为第二支抒情单曲的《 Lost Without You 》也来势汹汹大有作为，专辑《 Innocent Eyes 》中的作品，绝大多数听起来就好象是从她日记中撕落的篇幅，她独自或者与人合作谱写出几近所有的曲子，“每首歌都抓住了一段别具深刻意义的回忆” Delta 解释道。“企图创造出一张能反映我生命中这段时光的专辑。”<br/>由 starain 于 2007-8-13 21:56:17 最后编辑</li>
	<li class="msgtime">2007-8-13 21:53:55&nbsp;<a href="#comment" onclick="RevertComment('14')">回复该留言</a></li>
</ul><div style="display:none;" id="divAjaxComment"></div>
<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment">发表评论:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="http://starain.net.cn/blog/cmd.asp?act=cmt&key=259a692d" >
	<input type="hidden" name="inpId" id="inpId" value="9" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName">名称(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail">邮箱</label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage">网站链接</label></p>
		<p><input type="text" name="inpVerify" id="inpVerify" class="text" value="" size="28" tabindex="4" /> <label for="inpVerify">验证(*)</label> <img style="border:1px solid black" src="http://starain.net.cn/blog/function/c_validcode.asp?name=commentvalid" height="20" width="60" alt="" title=""/></p>
	<p><label for="txaArticle">正文(*)(留言最长字数:1000)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="提交" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember">记住我,下次回复时不用重新输入个人信息</label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom">◎欢迎参与讨论，请在这里发表您的看法、交流您的观点。</p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div id="divCalendar2"><script language="JavaScript" type="text/javascript">strBatchInculde+="divCalendar2=calendar,"</script></div>
</div>

<div class="function" id="divComments">
<h3>最新回复</h3>
<ul id="ulComments">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulComments=comments,"</script>
</ul>
</div>

<div class="function" id="divPrevious">
<h3>最近发表</h3>
<ul id="ulPrevious">
<script language="JavaScript" type="text/javascript">strBatchInculde+="ulPrevious=previous,"</script>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>
<!-- 2007-10-1 22:50:40 -->