﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="zh-CN" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/style/default.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="http://starain.net.cn/blog/css/print.css" type="text/css" media="print" />
	<script language="JavaScript" src="http://starain.net.cn/blog/script/common.js" type="text/javascript"></script>
	<title>star<PERSON>'s blog-2007 August</title>
</head>
<body class="multi catalog">
<script language="JavaScript" type="text/javascript">
	var str00="http://starain.net.cn/blog/";
	var str01="名称或邮箱不能为空";
	var str02="名称或邮箱格式不对";
	var str03="留言不能为空或过长";
	var str06="显示UBB表情>>";
	var intMaxLen="1000";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><a href="http://starain.net.cn/blog/">starain's blog</a></h1>
			<h2 id="BlogSubTitle">千金难求珍宝 家和易得欢笑 人生自是有情痴 愿做双飞鸟 情两难分付 是一丝烦恼 蓦然回首神仙地 还道人间好</h2>
		</div>
		<div id="divNavBar">
<h3>导航</h3>
<ul>
<li><a href="../../../index.html">starain.net.cn</a></li>
<li><a href="http://starain.net.cn/blog/">博客（Blog）</a></li>
<li><a href="http://starain.net.cn/blog/search.asp">搜索（Search）</a></li>
<li><a href="http://starain.net.cn/blog/tags.asp">标签（TagCloud）</a></li>
<li><a href="http://starain.net.cn/blog/guestbook.asp">留言（GuestBook）</a></li>
<li><a href="http://starain.net.cn/blog/rss.xml">订阅（RSS2.0）</a></li>
<li><a href="http://starain.net.cn/blog/cmd.asp?act=login">管理（Admin）</a></li>
</ul>
		</div>
		<div id="divMain">
 <div class="post cate6 auth1">
	<h4 class="post-date">2007年8月27日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/25.html">腾讯 QQ DS、TM DS、skype DS 卡带设计</a></h2>
	<div class="post-body">如果真有这样的卡带面市，我觉得我愿意买这么一盘正版卡带，最好支持在线软升级，其实做这样的卡带成本并不高，技术投入也不像有的人说的那么大，关键是得到官方的支持，我们希望腾讯能在这个问题上走在 MSN 和 skype 的前面...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:3 | 引用:0 | 浏览:<span id="spn25"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn25=25,"</script>
	</h6>
</div> <div class="post cate4 auth1">
	<h4 class="post-date">2007年8月25日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/20.html">教你用 Photoshop 做花布 技术文档</a></h2>
	<div class="post-body"> 本教程将用简单的方法教会你制作一些花纹，这样你可以为你的平面或者网页作品锦上添花，掌握之后可以举一反三制作出更丰富的一些花纹类型，到时候别忘了发布给大家分享哦。</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:技术文档 | 评论:0 | 引用:0 | 浏览:<span id="spn20"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn20=20,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年8月24日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/14.html">Windows Preinstallation Environment</a></h2>
	<div class="post-body">基于 Windows 2003 Service Pack 2 Enterprise Edition 制作的 WinPE ，光盘镜像中提供了我个人认为非常常用的一些工具软件，紫光拼音中文输入法支持、压缩解压支持、Ghost 11、WinNT Password Renew（功能比较邪恶）、File Hasher chs（本人做了中文化）、Paragon Partition Manager 服务器版、Virtual Drive Manager、WinXP Setup Launcher、原版的记事本、画...</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:0 | 引用:0 | 浏览:<span id="spn14"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn14=14,"</script>
	</h6>
</div> <div class="post cate5 auth1">
	<h4 class="post-date">2007年8月13日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/9.html">MV《Lost Without You》——Delta Goodrem</a></h2>
	<div class="post-body">影音推荐《Lost Without You》来自 Delta Goodrem</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:休闲影音 | 评论:1 | 引用:0 | 浏览:<span id="spn9"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn9=9,"</script>
	</h6>
</div> <div class="post cate6 auth1">
	<h4 class="post-date">2007年8月13日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/8.html">UltraISO 8.6.3.2056 单文件绿色版</a></h2>
	<div class="post-body">UltraISO 8.6.3.2056 最新的软碟通单文件绿色版，非常好用的光盘镜像编辑工具，可直接刻录光盘镜像。</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:一些作品 | 评论:1 | 引用:0 | 浏览:<span id="spn8"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn8=8,"</script>
	</h6>
</div> <div class="post cate5 auth1">
	<h4 class="post-date">2007年8月11日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/7.html">歌曲《甜甜圈》——小熏 阿本</a></h2>
	<div class="post-body">影音推荐《甜甜圈》来自 小熏、阿本</div>
	<h5 class="post-tags">Tags: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		发布:starain | 分类:休闲影音 | 评论:1 | 引用:0 | 浏览:<span id="spn7"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn7=7,"</script>
	</h6>
</div>
<div class="post pagebar">分页:<a href="http://starain.net.cn/blog/catalog.asp?date=2007-8&amp;page=1">[&laquo;]</a><span class="now-page">1</span><a href="http://starain.net.cn/blog/catalog.asp?date=2007-8&amp;page=2">[2]</a><a href="http://starain.net.cn/blog/catalog.asp?date=2007-8&amp;page=2">[&raquo;]</a></div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3>日历</h3>
<div><script language="JavaScript" src="http://starain.net.cn/blog/function/c_html_js.asp?date=2007-8" type="text/javascript"></script></div>
</div>

<div class="function" id="divCatalog">
<h3>网站目录</h3>
<ul>
<li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=2" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_2.html">默认分类<span class="article-nums"> (0)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=4" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_4.html">技术文档<span class="article-nums"> (7)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=5" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_5.html">休闲影音<span class="article-nums"> (3)</span></a></li><li><span class="feed-icon"><a href="http://starain.net.cn/blog/sydication.asp?cate=6" target="_blank"><img title="rss" width="20" height="12" src="http://starain.net.cn/blog/IMAGE/LOGO/rss.png" border="0" alt="rss" /></a>&nbsp;</span><a href="http://starain.net.cn/blog/post/cat_6.html">一些作品<span class="article-nums"> (30)</span></a></li>
</ul>
</div>

<div class="function" id="divArchives">
<h3>文章归档</h3>
<ul>
<li><a href="http://starain.net.cn/blog/post/2007_10.html">2007 October (1)</a></li><li><a href="http://starain.net.cn/blog/post/2007_9.html">2007 September (7)</a></li><li><a href="http://starain.net.cn/blog/post/2007_8.html">2007 August (9)</a></li>
</ul>
</div>

<div class="function" id="divTags">
<h3>Tags</h3>
<ul>
<li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%B5%81%E6%B0%B4%E6%97%A5%E8%AE%B0">流水日记 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E5%B9%B3%E9%9D%A2%E8%AE%BE%E8%AE%A1">平面设计 (12)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作 (8)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计 (4)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E8%BD%AF%E4%BB%B6%E5%A2%9E%E5%80%BC">软件增值 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E7%BB%9C%E5%B7%A5%E7%A8%8B">网络工程 (3)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E6%A0%87%E8%AF%86%E8%AE%BE%E8%AE%A1">标识设计 (2)</a></li><li><a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%B3%BB%E7%BB%9F%E7%BB%B4%E6%8A%A4">系统维护 (2)</a></li>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><hr size=2 width=60% color=Silver /></h3>
			<h2 id="BlogCopyRight">Copyright Starain Studio All Rights Reserved.</h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "http://starain.net.cn/blog/function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>