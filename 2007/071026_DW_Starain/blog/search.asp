<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    search.asp
'// 开始时间:    2005.02.17
'// 最后修改:    
'// 备    注:    站内搜索
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="c_option.asp" -->
<!-- #include file="function/c_function.asp" -->
<!-- #include file="function/c_function_md5.asp" -->
<!-- #include file="function/c_system_lib.asp" -->
<!-- #include file="function/c_system_base.asp" -->
<%

Call System_Initialize()

'检查权限
If Not CheckRights("Search") Then Call ShowError(6)

Dim strQuestion
strQuestion=TransferHTML(Request.QueryString("q"),"[nohtml]")

Dim ArtList
Set ArtList=New TArticleList

ArtList.LoadCache

ArtList.template="SEARCH"

If ArtList.Search(strQuestion) Then

	ArtList.Title=strQuestion

	ArtList.Build

	Response.Write ArtList.html

End If

%><!-- <%=RunTime()%> --><%
If Err.Number<>0 then
	Call ShowError(0)
End If
%>