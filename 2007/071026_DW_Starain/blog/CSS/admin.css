*{
	font-size:12px;
}
body{
	margin:0;
	padding:0;
	color: #000000;
	font-size:12px;
	background:#F9F9F9;
	font-family:"宋体","黑体";
}
h1,h2,h3,h4,h5,h6{
	font-size:18px;
	padding:0;
	margin:0;
}
a{
	text-decoration: none;
}
a:link {
	color:#0066CC;
	text-decoration: none;
}
a:visited {
	color:#0066CC;
	text-decoration: none;
}
a:hover {
	color:#FF7F50;
	text-decoration: underline;
}
a:active {
	color:#FF7F50;
	text-decoration: underline;
}
p{
	margin:0;
	padding:5px;
}
table {
	border-collapse: collapse;
	border:1px solid #333333;
	background:#ffffff;
	margin-top:10px;
}
td{
	border:1px solid #333333;
	margin:0;
	padding:3px;
}
img{
	border:0;
}
hr{
	border:0px;
	border-top:1px solid #666666;
	background:#666666;
	margin:2px 0 4px 0;
	padding:0;
	height:0px;
}
img{
	margin:0;
	padding:0;
}
form{
	margin:0;
	padding:0;
}
input{
	background:#eeeeee;
}
select{
	background:#eeeeee;
}
textarea{
	background:#eeeeee;
}
input.button{
	background:url("../image/edit/fade-butt.png");
	border: 3px double #909090;
	border-left-color: #c0c0c0;
	border-top-color: #c0c0c0;
	color: #333;
	padding: 0.05em 0.25em 0.05em 0.25em;
}








#divSidebar{
	padding:0;
	margin:0 0 0 0;
	text-align:center;
	float:left;
}
#divTitle{
	padding:0 0 0 0;
	margin:0;
	float:left;
	width:100%;
	background:#EDF4FB url("../image/edit/titlebg.gif") repeat-x;
	height:60px;
	text-align:left;
}
#divManage{
	padding:0 0 0 0;
	margin:0;
	float:left;
	width:100%;
	background:#DAE2E8;
	border-top:1px solid #969EA4;
	border-bottom:1px solid #969EA4;
	background:url("../image/edit/navbg.gif") repeat-x center top;
}
#divManage h3{
	text-align:center;
	padding:10px;
	margin:0;
}
#divManage ul{
	margin:0 0 0 0;
	padding:0 0 0 0;
}
#divManage li{
	float:left;
	margin: 0 0 0 0;
	padding:0 0 0 0;
	list-style-type:none;
	list-style-position:outside;
	background:url("../image/edit/navbg2.gif") no-repeat right top;
}
#divManage a{
	float:left;
	padding:5px 8px 3px 8px;
	margin:0;
	font-weight:normal;
	text-decoration: none;
	height:16px;
}
#divManage a:link {
	color:black;
	text-decoration: none;
}
#divManage a:visited {
	color:black;
	text-decoration: none;
}
#divManage a:hover {
	color:white;
	background:#DC143C;
}
#divManage a:active {
	color:white;
	background:#DC143C;
}
#divMain{
	float:left;
	width:100%;
	padding:0;
	text-align:left;
}
#divMain2{
	float:left;
	padding:10px 3% 10px 2%;
	text-align:left;
	width:95%;
}
div.Header{
	padding:6px 0 0 2%;
	margin:0;
	width:98%;
	background:#CFD9DF;
	height:22px;
	text-align:left;
	font-size:16px;
}
div.form{
	width:750px;
	margin:10px auto;
}
#edit{
	border:1px solid #333333;
	padding:4px 1% 4px 1%;
	margin:0px;
	width:97%;
	text-align:left;
}
#txaContent {
	height:260px;
	width:715px;
}
#txaIntro {
	height:120px;
	width:715px;
}
#edtTrackBack{
	width:585px;
}
#btnSend{
	width:120px;
}
#edtTitle{
	width:340px;
}
#edtTag{
	width:340px;
}
select.edit{
	width:345px;
}
select.full{
	width:100%;
}








#divUBB{
	padding:5px 0 5px 0;
	width:100%;
}
#divUBB p{
	background:#eaeaea;
}
#divUBB ul{
	list-style-type:none;
	list-style-position : outside;
	margin:0 0 0 0;
	padding:2px 0 5px 0;
	width:100%;
	float:left;
	clear:both;
	background:#eaeaea;
}
#divUBB ul li{
	float:left;
	margin:2px 0 0 5px;
	white-space:nowrap;
}
#divUBB img.separator{
	margin:0 0 0 2px;
	background:url();
	padding:0;
	width:2px;
	height:22px;
}
#divUBB img{
	background:url("../image/edit/button.gif");
	padding:3px 4px 3px 3px;
	height:16px;
	width:16px;
	cursor:pointer;
}
#divUBB span{
	cursor:pointer;
}
#ulTag{
	line-height:150%;
}








body.login{
	margin:0;
	padding:0;
	background-color:#EDF5FB;
}
#frmLogin{
	position:absolute;
	left: 50%;
	top: 50%;
	margin: -150px 0px 0px -200px;
	padding:0;
	overflow:hidden;
	width:400px;
	height:300px;
	background-color:white;
	border:1px solid #B3C3CD;
}
#frmLogin ul{
	margin:0;
}
#frmLogin h3{
	padding:10px 0 5px 0;
	margin:1px 1px 30px 1px;
	text-align:center;
	color:black;
	background:#A1B0B9;
}
#frmLogin select{
	width:225px;
}
#frmLogin input{
	width:220px;
}
#frmLogin #edtCheckOut{
	width:140px;
}
#frmLogin li{
	margin:5px 0 15px 0;
	padding:0 20px 0 20px;
	list-style-type:none;
	text-align:center;
}
#frmLogin table {
	border-collapse:separate;
	border:0;
	background:none;
}
#frmLogin td{
	border:0;
	margin:0;
	padding:5px;
}