<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog 彩虹网志个人版
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    edit_fckeditor.asp
'// 开始时间:    2005.04.06
'// 最后修改:    
'// 备    注:    编辑页
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="../c_option.asp" -->
<!-- #include file="../function/c_function.asp" -->
<!-- #include file="../function/c_system_lib.asp" -->
<!-- #include file="../function/c_system_base.asp" -->
<%

Call System_Initialize()

'检查非法链接
Call CheckReference("")

'检查权限
If Not CheckRights("ArticleEdt") Then Call ShowError(6)

Dim EditArticle

Set EditArticle=New TArticle

If Not IsEmpty(Request.QueryString("id")) Then
	If EditArticle.LoadInfobyID(Request.QueryString("id")) Then
		If EditArticle.AuthorID<>BlogUser.ID Then
			If CheckRights("Root")=False Then 
				Call ShowError(6)
			End If
		End If
	Else
		Call ShowError(9)
	End If
Else
	EditArticle.AuthorID=BlogUser.ID
End If


On Error Resume Next
BlogTitle=EditArticle.HtmlUrl

If Err.Number=0 Then

	EditArticle.Title=TransferHTML(EditArticle.Title,"[html-japan]")
	EditArticle.Content=TransferHTML(EditArticle.Content,"[html-japan]")
	EditArticle.Intro=TransferHTML(EditArticle.Intro,"[html-japan]")

	EditArticle.Title=TransferHTML(EditArticle.Title,"[html-format]")
	EditArticle.Content=TransferHTML(EditArticle.Content,"[textarea]")
	EditArticle.Intro=TransferHTML(EditArticle.Intro,"[textarea]")

Else

	GetCategory()
	GetUser()

	EditArticle.Title=EditArticle.Title
	EditArticle.Content=TransferHTML(EditArticle.Content,"[&]")
	EditArticle.Intro=TransferHTML(EditArticle.Intro,"[&]")

End If
Err.Clear

BlogTitle=ZC_BLOG_TITLE & ZC_MSG044 & ZC_MSG047

%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<%=ZC_BLOG_LANGUAGE%>" lang="<%=ZC_BLOG_LANGUAGE%>">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="Content-Language" content="<%=ZC_BLOG_LANGUAGE%>" />
	<link rel="stylesheet" rev="stylesheet" href="../CSS/admin.css" type="text/css" media="screen" />
	<script language="JavaScript" src="../script/common.js" type="text/javascript"></script>
	<script type="text/javascript" src="../admin/FCKeditor/fckeditor.js"></script>
	<title><%=BlogTitle%></title>
</head>
<body>

<div id="divMain">
<div class="Header"><%=ZC_MSG047%></div>

<div class="form">
<form id="edit" name="edit" method="post">
	<input type="hidden" name="edtID" id="edtID" value="<%=EditArticle.ID%>">
	<p><%=ZC_MSG060%>:<input type="text" name="edtTitle" id="edtTitle" size="56" value="<%=EditArticle.Title%>" /></p>
	<p><%=ZC_MSG012%>:<select class="edit" size="1" id="cmbCate" onchange="edtCateID.value=this.options[this.selectedIndex].value"><option value="0"></option>
<%
	Dim Category
	For Each Category in Categorys
		If IsObject(Category) Then
			Response.Write "<option value="""&Category.ID&""" "
			If EditArticle.CateID=Category.ID Then Response.Write "selected=""selected"""
			Response.Write ">"&Category.Name&"</option>"
		End If
	Next
%>
	</select><input type="hidden" name="edtCateID" id="edtCateID" value="<%=EditArticle.CateID%>"></p>
	<p><%=ZC_MSG003%>:<select class="edit" size="1" id="cmbUser" onchange="edtAuthorID.value=this.options[this.selectedIndex].value"><option value="0"></option>
<%
	Dim User
	For Each User in Users
		If IsObject(User) Then

			If User.ID=EditArticle.AuthorID Then
				Response.Write "<option value="""&User.ID&""" "
				Response.Write "selected=""selected"""
				Response.Write ">"&User.Name&"</option>"
			End If

		End If
	Next
%>
	</select><input type="hidden" name="edtAuthorID" id="edtAuthorID" value="<%=EditArticle.AuthorID%>"></p>
	<p><%=ZC_MSG061%>:<select class="edit" size="1" id="cmbArticleLevel" onchange="edtLevel.value=this.options[this.selectedIndex].value">
<%
	Dim ArticleLevel
	Dim i:i=0
	For Each ArticleLevel in ZVA_Article_Level_Name
		Response.Write "<option value="""& i &""" "
		If EditArticle.Level=i Then Response.Write "selected=""selected"""
		Response.Write ">"& ZVA_Article_Level_Name(i) &"</option>"
		i=i+1
	Next
%>
	</select><input type="hidden" name="edtLevel" id="edtLevel" value="<%=EditArticle.Level%>" />
<%
Err.Clear
On Error Resume Next
BlogTitle=EditArticle.Istop

If Err.Number=0 Then
%>
&nbsp;<%=ZC_MSG051%>
<%If EditArticle.Istop Then%>
<input type="checkbox" name="edtIstop" id="edtIstop" value="True" checked=""/>
<%Else%>
<input type="checkbox" name="edtIstop" id="edtIstop" value="True"/>
<%End If%>
<%
End If
Err.Clear
%>
	</p>
	<p><%=ZC_MSG062%>:<input type="text" name="edtYear" id="edtYear" size="10" value="<%=Year(EditArticle.PostTime)%>" />-<input type="text" name="edtMonth" id="edtMonth" size="10" value="<%=Month(EditArticle.PostTime)%>" />-<input type="text" name="edtDay" id="edtDay" size="10" value="<%=Day(EditArticle.PostTime)%>" />-<input type="text" name="edtTime" id="edtTime" size="12" value="<%= Hour(EditArticle.PostTime)&":"&Minute(EditArticle.PostTime)&":"&Second(EditArticle.PostTime)%>" /></p>
<%
Err.Clear
On Error Resume Next
BlogTitle=EditArticle.Tag

If Err.Number=0 Then
%>
	<p><%=ZC_MSG138%>:<input type="text" size="56" name="edtTag" id="edtTag" value="<%=TransferHTML(EditArticle.TagToName,"[html-format]")%>"> <span style="cursor:pointer;" onclick="if(document.getElementById('ulTag').style.display=='none'){document.getElementById('ulTag').style.display='block';}else{document.getElementById('ulTag').style.display='none'};"><%=ZC_MSG139%>&gt;&gt;</span>
	<ul id="ulTag" style="display:none;">
<%
	GetTags()
	Dim Tag
	For Each Tag in Tags
		If IsObject(Tag) Then
			Response.Write "<span style='cursor:pointer;' onclick='AddKey(""" & Tag.Name & """)' />"& Tag.Name &"&nbsp;&nbsp;</span>"
		End If
	Next
%>
	</ul></p>
<%
End If
Err.Clear
%>
<%
Err.Clear
On Error Resume Next
BlogTitle=EditArticle.Alias
If Err.Number=0 Then
%>
	<p><%=ZC_MSG147%>:<input type="text" size="56" name="edtAlias" id="edtAlias" value="<%=EditArticle.Alias%>">.<%=ZC_STATIC_TYPE%>
<%
End If
Err.Clear
%>
<%If CheckRights("FileSnd") Then%>
	<p><iframe frameborder="0" height="102" marginheight="0" marginwidth="0" scrolling="no" width="100%" src="../cmd.asp?act=FileSnd"></iframe></p>
<%Else%>
	<br/>
<%End If%>
<p><%=ZC_MSG055%>:<span id="timemsg"></span><span id="msg2"></span><span id="msg"></span></p>
	<p>
	<textarea style="display:none" rows="4" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" name="txaContent" id="txaContent"><%=EditArticle.Content%></textarea>
	<input type="hidden" id="MyEditor___Config" value="Key1=Value1&Key2=Value2&... (Key/Value:HTML encoded)">
	<iframe id="MyEditor___Frame" src="FCKeditor/editor/fckeditor.html?InstanceName=txaContent&Toolbar=Default" width="100%" height="400" frameborder="no" scrolling="no"></iframe>
	</p>
	<p><%=ZC_MSG016%>:<SCRIPT LANGUAGE="JavaScript" src="c_autosaverjs.asp?act=edit&type=fckeditor"></SCRIPT></p>
	<p><textarea rows="4" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" name="txaIntro" id="txaIntro"><%=EditArticle.Intro%></textarea></p>
	<p><input class="button" type="submit" value="<%=ZC_MSG087%>" id="btnPost" onclick='return checkArticleInfo();' /></p>
	<p></p><hr/><p><input type="text" name="edtTrackBack" id="edtTrackBack" value="" />&nbsp;<input class="button" type="submit" value="<%=ZC_MSG064%>" id="btnSend" onclick='if(document.getElementById("edtID").value==0){alert(str12);return false};document.getElementById("edit").action="../cmd.asp?act=TrackBackSnd";' /></p>
	<p></p>
</form>
</div>

			</div>

</body>
<script>

	objActive="txaContent";

	var str10="<%=ZC_MSG115%>";
	var str11="<%=ZC_MSG116%>";
	var str12="<%=ZC_MSG117%>";

	function checkArticleInfo(){
		document.getElementById("edit").action="../cmd.asp?act=ArticlePst&type=fckeditor";

		if(document.getElementById("edtCateID").value==0){
			alert(str10);
			return false
		}

	}

	function AddKey(i) {
		var strKey=document.getElementById("edtTag").value;
		var strNow=i+" "

		if(strKey.indexOf(strNow)==-1){
			strKey=strKey+strNow;
		}
		document.getElementById("edtTag").value=strKey;
	}
	function DelKey(i) {
		var strKey=document.getElementById("edtTag").value;
		var strNow="{"+i+"}"
		if(strKey.indexOf(strNow)!=-1){

			strKey=strKey.substring(0,strKey.indexOf(strNow))+strKey.substring(strKey.indexOf(strNow)+strNow.length,strKey.length)

		}
		document.getElementById("edtTag").value=strKey;
	}

</script>
</html>
<% 
Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>