<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog 彩虹网志个人版
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    edit_setting.asp
'// 开始时间:    2005.03.16
'// 最后修改:    
'// 备    注:    编辑设置页
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="../c_option.asp" -->
<!-- #include file="../function/c_function.asp" -->
<!-- #include file="../function/c_system_lib.asp" -->
<!-- #include file="../function/c_system_base.asp" -->
<%

Call System_Initialize()

'检查非法链接
Call CheckReference("")

'检查权限
If Not CheckRights("SettingMng") Then Call ShowError(6)

Dim EditArticle

BlogTitle=ZC_BLOG_TITLE & ZC_MSG044 & ZC_MSG247

%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<%=ZC_BLOG_LANGUAGE%>" lang="<%=ZC_BLOG_LANGUAGE%>">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="Content-Language" content="<%=ZC_BLOG_LANGUAGE%>" />
	<link rel="stylesheet" rev="stylesheet" href="../CSS/admin.css" type="text/css" media="screen" />
	<script language="JavaScript" src="../script/common.js" type="text/javascript"></script>
	<title><%=BlogTitle%></title>
</head>
<body>
			<div id="divMain">
<div class="Header"><%=ZC_MSG247%></div>
<div id="divMain2">
<%
	Call GetBlogHint()
%>
<form id="edit" name="edit" method="post">
<%
	Dim i,j
	Dim tmpSng

	tmpSng=LoadFromFile(BlogPath & "/c_custom.asp","utf-8")

	Dim strZC_BLOG_HOST
	Dim strZC_BLOG_TITLE
	Dim strZC_BLOG_SUBTITLE
	Dim strZC_BLOG_NAME
	Dim strZC_BLOG_SUB_NAME
	Dim strZC_BLOG_CSS
	Dim strZC_BLOG_COPYRIGHT
	Dim strZC_BLOG_MASTER

	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_HOST",strZC_BLOG_HOST)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_TITLE",strZC_BLOG_TITLE)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_SUBTITLE",strZC_BLOG_SUBTITLE)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_NAME",strZC_BLOG_NAME)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_SUB_NAME",strZC_BLOG_SUB_NAME)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_CSS",strZC_BLOG_CSS)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_COPYRIGHT",strZC_BLOG_COPYRIGHT)
	Call LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_MASTER",strZC_BLOG_MASTER)


	strZC_BLOG_HOST=TransferHTML(strZC_BLOG_HOST,"[html-format]")
	strZC_BLOG_TITLE=TransferHTML(strZC_BLOG_TITLE,"[html-format]")
	strZC_BLOG_SUBTITLE=TransferHTML(strZC_BLOG_SUBTITLE,"[html-format]")
	strZC_BLOG_NAME=TransferHTML(strZC_BLOG_NAME,"[html-format]")
	strZC_BLOG_SUB_NAME=TransferHTML(strZC_BLOG_SUB_NAME,"[html-format]")
	strZC_BLOG_CSS=TransferHTML(strZC_BLOG_CSS,"[html-format]")
	strZC_BLOG_COPYRIGHT=TransferHTML(strZC_BLOG_COPYRIGHT,"[html-format]")
	strZC_BLOG_MASTER=TransferHTML(strZC_BLOG_MASTER,"[html-format]")

	Response.Write "<br/>&nbsp;<b>"&ZC_MSG105&"</b>"
	Response.Write "<hr/>"

	Response.Write "<p>"& ZC_MSG104 &":</p><p><input id=""edtZC_BLOG_HOST"" name=""edtZC_BLOG_HOST"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_HOST & """ /></p><p></p>"
	Response.Write "<p>"& ZC_MSG091 &":</p><p><input id=""edtZC_BLOG_NAME"" name=""edtZC_BLOG_NAME"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_NAME & """ /></p><p></p>"
	Response.Write "<p>"& ZC_MSG092 &":</p><p><input id=""edtZC_BLOG_SUB_NAME"" name=""edtZC_BLOG_SUB_NAME"" style=""width:90%""  type=""text"" value=""" & strZC_BLOG_SUB_NAME & """ /></p><p></p>"
	Response.Write "<p>"& ZC_MSG093 &":</p><p><input id=""edtZC_BLOG_TITLE"" name=""edtZC_BLOG_TITLE""style=""width:90%""  type=""text"" value=""" & strZC_BLOG_TITLE &""" /></p><p></p>"
	Response.Write "<p>"& ZC_MSG094 &":</p><p><input id=""edtZC_BLOG_SUBTITLE"" name=""edtZC_BLOG_SUBTITLE"" style=""width:90%""  type=""text"" value=""" & strZC_BLOG_SUBTITLE & """ /></p><p></p>"
	Response.Write "<p>"& ZC_MSG095 &":</p><p><input id=""edtZC_BLOG_CSS"" name=""edtZC_BLOG_CSS"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_CSS & """ /></p><p></p>"
	Response.Write "<p>"& ZC_MSG096 &":</p><p><input id=""edtZC_BLOG_COPYRIGHT"" name=""edtZC_BLOG_COPYRIGHT"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_COPYRIGHT & """ /></p><p></p>"
	Response.Write "<p>"& ZC_MSG097 &":</p><p><input id=""edtZC_BLOG_MASTER"" name=""edtZC_BLOG_MASTER"" style=""width:90%""  type=""text"" value=""" & strZC_BLOG_MASTER & """ /></p><p></p>"

	Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" id=""btnPost"" onclick='document.getElementById(""edit"").action=""../cmd.asp?act=SettingSav"";' /></p>"
	Response.Write "<br/>&nbsp;<b>"&ZC_MSG173&"</b>"
	Response.Write "<hr/>"

	tmpSng=LoadFromFile(BlogPath & "/c_option.asp","utf-8")


	Dim strZC_BLOG_CLSID
	If LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_CLSID",strZC_BLOG_CLSID) Then
		strZC_BLOG_CLSID=TransferHTML(strZC_BLOG_CLSID,"[html-format]")
		Response.Write "<p>※"&ZC_MSG174&" :</p><p><input id=""edtZC_BLOG_CLSID"" name=""edtZC_BLOG_CLSID"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_CLSID & """ /></p><p></p>"
	End If

	Dim strZC_TIME_ZONE
	If LoadValueForSetting(tmpSng,True,"String","ZC_TIME_ZONE",strZC_TIME_ZONE) Then
		strZC_TIME_ZONE=TransferHTML(strZC_TIME_ZONE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG175&" :</p><p><input id=""edtZC_TIME_ZONE"" name=""edtZC_TIME_ZONE"" style=""width:90%"" type=""text"" value=""" & strZC_TIME_ZONE & """ /></p><p></p>"
	End If

	Dim strZC_BLOG_LANGUAGE
	If LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_LANGUAGE",strZC_BLOG_LANGUAGE) Then
		strZC_BLOG_LANGUAGE=TransferHTML(strZC_BLOG_LANGUAGE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG176&" :</p><p><input id=""edtZC_BLOG_LANGUAGE"" name=""edtZC_BLOG_LANGUAGE"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_LANGUAGE & """ /></p><p></p>"
	End If


	Dim strZC_UPDATE_INFO_URL
	If LoadValueForSetting(tmpSng,True,"String","ZC_UPDATE_INFO_URL",strZC_UPDATE_INFO_URL) Then
		strZC_UPDATE_INFO_URL=TransferHTML(strZC_UPDATE_INFO_URL,"[html-format]")
		Response.Write "<p>※"&ZC_MSG290&" :</p><p><input id=""edtZC_UPDATE_INFO_URL"" name=""edtZC_UPDATE_INFO_URL"" style=""width:90%"" type=""text"" value=""" & strZC_UPDATE_INFO_URL & """/></p><p></p>"
	End If

	Dim strZC_BLOG_VERSION
	If LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_VERSION",strZC_BLOG_VERSION) Then
		strZC_BLOG_VERSION=TransferHTML(strZC_BLOG_VERSION,"[html-format]")
		Response.Write "<p style='display:none;'>※"&ZC_MSG179&" :</p><p style='display:none;'><input id=""edtZC_BLOG_VERSION"" name=""edtZC_BLOG_VERSION"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_VERSION & """ readonly/></p><p></p>"
	End If

	Dim strZC_BLOG_WEBEDIT
	If LoadValueForSetting(tmpSng,True,"String","ZC_BLOG_WEBEDIT",strZC_BLOG_WEBEDIT) Then
		strZC_BLOG_WEBEDIT=TransferHTML(strZC_BLOG_WEBEDIT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG180&" :</p><p><input id=""edtZC_BLOG_WEBEDIT"" name=""edtZC_BLOG_WEBEDIT"" style=""width:90%"" type=""text"" value=""" & strZC_BLOG_WEBEDIT & """ /></p><p></p>"
	End If

	Dim strZC_UPLOAD_FILETYPE
	If LoadValueForSetting(tmpSng,True,"String","ZC_UPLOAD_FILETYPE",strZC_UPLOAD_FILETYPE) Then
		strZC_UPLOAD_FILETYPE=TransferHTML(strZC_UPLOAD_FILETYPE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG183&" :</p><p><input id=""edtZC_UPLOAD_FILETYPE"" name=""edtZC_UPLOAD_FILETYPE"" style=""width:90%"" type=""text"" value=""" & strZC_UPLOAD_FILETYPE & """ /></p><p></p>"
	End If

	Dim strZC_UPLOAD_FILESIZE
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_UPLOAD_FILESIZE",strZC_UPLOAD_FILESIZE) Then
		strZC_UPLOAD_FILESIZE=TransferHTML(strZC_UPLOAD_FILESIZE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG184&" :</p><p><input id=""edtZC_UPLOAD_FILESIZE"" name=""edtZC_UPLOAD_FILESIZE"" style=""width:90%"" type=""text"" value=""" & strZC_UPLOAD_FILESIZE & """ /></p><p></p>"
	End If

	Dim strZC_RSS_EXPORT_WHOLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_RSS_EXPORT_WHOLE",strZC_RSS_EXPORT_WHOLE) Then
		strZC_RSS_EXPORT_WHOLE=TransferHTML(strZC_RSS_EXPORT_WHOLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG226&" :</p><p><input id=""edtZC_RSS_EXPORT_WHOLE"" name=""edtZC_RSS_EXPORT_WHOLE"" style=""width:90%"" type=""text"" value=""" & strZC_RSS_EXPORT_WHOLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_GUEST_REVERT_COMMENT_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_GUEST_REVERT_COMMENT_ENABLE",strZC_GUEST_REVERT_COMMENT_ENABLE) Then
		strZC_GUEST_REVERT_COMMENT_ENABLE=TransferHTML(strZC_GUEST_REVERT_COMMENT_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG283&" :</p><p><input id=""edtZC_GUEST_REVERT_COMMENT_ENABLE"" name=""edtZC_GUEST_REVERT_COMMENT_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_GUEST_REVERT_COMMENT_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_COMMENT_TURNOFF
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_COMMENT_TURNOFF",strZC_COMMENT_TURNOFF) Then
		strZC_COMMENT_TURNOFF=TransferHTML(strZC_COMMENT_TURNOFF,"[html-format]")
		Response.Write "<p>※"&ZC_MSG262&" :</p><p><input id=""edtZC_COMMENT_TURNOFF"" name=""edtZC_COMMENT_TURNOFF"" style=""width:90%"" type=""text"" value=""" & strZC_COMMENT_TURNOFF & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_TRACKBACK_TURNOFF
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_TRACKBACK_TURNOFF",strZC_TRACKBACK_TURNOFF) Then
		strZC_TRACKBACK_TURNOFF=TransferHTML(strZC_TRACKBACK_TURNOFF,"[html-format]")
		Response.Write "<p>※"&ZC_MSG263&" :</p><p><input id=""edtZC_TRACKBACK_TURNOFF"" name=""edtZC_TRACKBACK_TURNOFF"" style=""width:90%"" type=""text"" value=""" & strZC_TRACKBACK_TURNOFF & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_GUESTBOOK_CONTENT
	If LoadValueForSetting(tmpSng,True,"String","ZC_GUESTBOOK_CONTENT",strZC_GUESTBOOK_CONTENT) Then
		strZC_GUESTBOOK_CONTENT=TransferHTML(strZC_GUESTBOOK_CONTENT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG276&" :</p><p><input id=""edtZC_GUESTBOOK_CONTENT"" name=""edtZC_GUESTBOOK_CONTENT"" style=""width:90%"" type=""text"" value=""" & strZC_GUESTBOOK_CONTENT & """ /></p><p></p>"
	End If


	Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" id=""btnPost"" onclick='document.getElementById(""edit"").action=""../cmd.asp?act=SettingSav"";' /></p>"
	Response.Write "<br/><br/>&nbsp;<b>"&ZC_MSG186&"</b>"
	Response.Write "<hr/>"


	Dim strZC_MSG_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_MSG_COUNT",strZC_MSG_COUNT) Then
		strZC_MSG_COUNT=TransferHTML(strZC_MSG_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG187&" :</p><p><input id=""edtZC_MSG_COUNT"" name=""edtZC_MSG_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_MSG_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_ARCHIVE_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_ARCHIVE_COUNT",strZC_ARCHIVE_COUNT) Then
		strZC_ARCHIVE_COUNT=TransferHTML(strZC_ARCHIVE_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG188&" :</p><p><input id=""edtZC_ARCHIVE_COUNT"" name=""edtZC_ARCHIVE_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_ARCHIVE_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_PREVIOUS_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_PREVIOUS_COUNT",strZC_PREVIOUS_COUNT) Then
		strZC_PREVIOUS_COUNT=TransferHTML(strZC_PREVIOUS_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG189&" :</p><p><input id=""edtZC_PREVIOUS_COUNT"" name=""edtZC_PREVIOUS_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_PREVIOUS_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_DISPLAY_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_DISPLAY_COUNT",strZC_DISPLAY_COUNT) Then
		strZC_DISPLAY_COUNT=TransferHTML(strZC_DISPLAY_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG190&" :</p><p><input id=""edtZC_DISPLAY_COUNT"" name=""edtZC_DISPLAY_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_DISPLAY_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_MANAGE_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_MANAGE_COUNT",strZC_MANAGE_COUNT) Then
		strZC_MANAGE_COUNT=TransferHTML(strZC_MANAGE_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG191&" :</p><p><input id=""edtZC_MANAGE_COUNT"" name=""edtZC_MANAGE_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_MANAGE_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_RSS2_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_RSS2_COUNT",strZC_RSS2_COUNT) Then
		strZC_RSS2_COUNT=TransferHTML(strZC_RSS2_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG192&" :</p><p><input id=""edtZC_RSS2_COUNT"" name=""edtZC_RSS2_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_RSS2_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_SEARCH_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_SEARCH_COUNT",strZC_SEARCH_COUNT) Then
		strZC_SEARCH_COUNT=TransferHTML(strZC_SEARCH_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG193&" :</p><p><input id=""edtZC_SEARCH_COUNT"" name=""edtZC_SEARCH_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_SEARCH_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_PAGEBAR_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_PAGEBAR_COUNT",strZC_PAGEBAR_COUNT) Then
		strZC_PAGEBAR_COUNT=TransferHTML(strZC_PAGEBAR_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG194&" :</p><p><input id=""edtZC_PAGEBAR_COUNT"" name=""edtZC_PAGEBAR_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_PAGEBAR_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_USE_NAVIGATE_ARTICLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_USE_NAVIGATE_ARTICLE",strZC_USE_NAVIGATE_ARTICLE) Then
		strZC_USE_NAVIGATE_ARTICLE=TransferHTML(strZC_USE_NAVIGATE_ARTICLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG209&" :</p><p><input id=""edtZC_USE_NAVIGATE_ARTICLE"" name=""edtZC_USE_NAVIGATE_ARTICLE"" style=""width:90%"" type=""text"" value=""" & strZC_USE_NAVIGATE_ARTICLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_MUTUALITY_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_MUTUALITY_COUNT",strZC_MUTUALITY_COUNT) Then
		strZC_MUTUALITY_COUNT=TransferHTML(strZC_MUTUALITY_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG230&" :</p><p><input id=""edtZC_MUTUALITY_COUNT"" name=""edtZC_MUTUALITY_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_MUTUALITY_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_COMMENT_REVERSE_ORDER_EXPORT
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_COMMENT_REVERSE_ORDER_EXPORT",strZC_COMMENT_REVERSE_ORDER_EXPORT) Then
		strZC_COMMENT_REVERSE_ORDER_EXPORT=TransferHTML(strZC_COMMENT_REVERSE_ORDER_EXPORT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG271&" :</p><p><input id=""edtZC_COMMENT_REVERSE_ORDER_EXPORT"" name=""edtZC_COMMENT_REVERSE_ORDER_EXPORT"" style=""width:90%"" type=""text"" value=""" & strZC_COMMENT_REVERSE_ORDER_EXPORT & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_COMMENT_VERIFY_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_COMMENT_VERIFY_ENABLE",strZC_COMMENT_VERIFY_ENABLE) Then
		strZC_COMMENT_VERIFY_ENABLE=TransferHTML(strZC_COMMENT_VERIFY_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG185&" :</p><p><input id=""edtZC_COMMENT_VERIFY_ENABLE"" name=""edtZC_COMMENT_VERIFY_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_COMMENT_VERIFY_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_IMAGE_WIDTH
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_IMAGE_WIDTH",strZC_IMAGE_WIDTH) Then
		strZC_IMAGE_WIDTH=TransferHTML(strZC_IMAGE_WIDTH,"[html-format]")
		Response.Write "<p>※"&ZC_MSG171&" :</p><p><input id=""edtZC_IMAGE_WIDTH"" name=""edtZC_IMAGE_WIDTH"" style=""width:90%"" type=""text"" value=""" & strZC_IMAGE_WIDTH & """/></p><p></p>"
	End If


	Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" id=""btnPost"" onclick='document.getElementById(""edit"").action=""../cmd.asp?act=SettingSav"";' /></p>"
	Response.Write "<br/><br/>&nbsp;<b>"&ZC_MSG281&"</b>"
	Response.Write "<hr/>"

	Dim strZC_STATIC_TYPE
	If LoadValueForSetting(tmpSng,True,"String","ZC_STATIC_TYPE",strZC_STATIC_TYPE) Then
		strZC_STATIC_TYPE=TransferHTML(strZC_STATIC_TYPE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG177&" :</p><p><input id=""edtZC_STATIC_TYPE"" name=""edtZC_STATIC_TYPE"" style=""width:90%"" type=""text"" value=""" & strZC_STATIC_TYPE & """ /></p><p></p>"
	End If

	Dim strZC_STATIC_DIRECTORY
	If LoadValueForSetting(tmpSng,True,"String","ZC_STATIC_DIRECTORY",strZC_STATIC_DIRECTORY) Then
		strZC_STATIC_DIRECTORY=TransferHTML(strZC_STATIC_DIRECTORY,"[html-format]")
		Response.Write "<p>※"&ZC_MSG178&" :</p><p><input id=""edtZC_STATIC_DIRECTORY"" name=""edtZC_STATIC_DIRECTORY"" style=""width:90%"" type=""text"" value=""" & strZC_STATIC_DIRECTORY & """ /></p><p></p>"
	End If

	Dim strZC_CUSTOM_DIRECTORY_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_CUSTOM_DIRECTORY_ENABLE",strZC_CUSTOM_DIRECTORY_ENABLE) Then
		strZC_CUSTOM_DIRECTORY_ENABLE=TransferHTML(strZC_CUSTOM_DIRECTORY_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG278&" :</p><p><input id=""edtZC_CUSTOM_DIRECTORY_ENABLE"" name=""edtZC_CUSTOM_DIRECTORY_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_CUSTOM_DIRECTORY_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_CUSTOM_DIRECTORY_REGEX
	If LoadValueForSetting(tmpSng,True,"String","ZC_CUSTOM_DIRECTORY_REGEX",strZC_CUSTOM_DIRECTORY_REGEX) Then
		strZC_CUSTOM_DIRECTORY_REGEX=TransferHTML(strZC_CUSTOM_DIRECTORY_REGEX,"[html-format]")
		Response.Write "<p>※"&ZC_MSG279&" :</p><p><input id=""edtZC_CUSTOM_DIRECTORY_REGEX"" name=""edtZC_CUSTOM_DIRECTORY_REGEX"" style=""width:90%"" type=""text"" value=""" & strZC_CUSTOM_DIRECTORY_REGEX & """ /></p><p></p>"
	End If

	Dim strZC_CUSTOM_DIRECTORY_ANONYMOUS
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_CUSTOM_DIRECTORY_ANONYMOUS",strZC_CUSTOM_DIRECTORY_ANONYMOUS) Then
		strZC_CUSTOM_DIRECTORY_ANONYMOUS=TransferHTML(strZC_CUSTOM_DIRECTORY_ANONYMOUS,"[html-format]")
		Response.Write "<p>※"&ZC_MSG280&" :</p><p><input id=""edtZC_CUSTOM_DIRECTORY_ANONYMOUS"" name=""edtZC_CUSTOM_DIRECTORY_ANONYMOUS"" style=""width:90%"" type=""text"" value=""" & strZC_CUSTOM_DIRECTORY_ANONYMOUS & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_REBUILD_FILE_COUNT
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_REBUILD_FILE_COUNT",strZC_REBUILD_FILE_COUNT) Then
		strZC_REBUILD_FILE_COUNT=TransferHTML(strZC_REBUILD_FILE_COUNT,"[html-format]")
		Response.Write "<p>※"&ZC_MSG181&" :</p><p><input id=""edtZC_REBUILD_FILE_COUNT"" name=""edtZC_REBUILD_FILE_COUNT"" style=""width:90%"" type=""text"" value=""" & strZC_REBUILD_FILE_COUNT & """ /></p><p></p>"
	End If

	Dim strZC_REBUILD_FILE_INTERVAL
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_REBUILD_FILE_INTERVAL",strZC_REBUILD_FILE_INTERVAL) Then
		strZC_REBUILD_FILE_INTERVAL=TransferHTML(strZC_REBUILD_FILE_INTERVAL,"[html-format]")
		Response.Write "<p>※"&ZC_MSG182&" :</p><p><input id=""edtZC_REBUILD_FILE_INTERVAL"" name=""edtZC_REBUILD_FILE_INTERVAL"" style=""width:90%"" type=""text"" value=""" & strZC_REBUILD_FILE_INTERVAL & """ /></p><p></p>"
	End If

	Dim strZC_MOONSOFT_PLUGIN_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_MOONSOFT_PLUGIN_ENABLE",strZC_MOONSOFT_PLUGIN_ENABLE) Then
		strZC_MOONSOFT_PLUGIN_ENABLE=TransferHTML(strZC_MOONSOFT_PLUGIN_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG270&" :</p><p><input id=""edtZC_MOONSOFT_PLUGIN_ENABLE"" name=""edtZC_MOONSOFT_PLUGIN_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_MOONSOFT_PLUGIN_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" id=""btnPost"" onclick='document.getElementById(""edit"").action=""../cmd.asp?act=SettingSav"";' /></p>"
	Response.Write "<br/><br/>&nbsp;<b>"&ZC_MSG195&"</b>"
	Response.Write "<hr/>"

	Dim strZC_UBB_LINK_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_LINK_ENABLE",strZC_UBB_LINK_ENABLE) Then
		strZC_UBB_LINK_ENABLE=TransferHTML(strZC_UBB_LINK_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG196&" :</p><p><input id=""edtZC_UBB_LINK_ENABLE"" name=""edtZC_UBB_LINK_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_LINK_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_FONT_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_FONT_ENABLE",strZC_UBB_FONT_ENABLE) Then
		strZC_UBB_FONT_ENABLE=TransferHTML(strZC_UBB_FONT_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG197&" :</p><p><input id=""edtZC_UBB_FONT_ENABLE"" name=""edtZC_UBB_FONT_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_FONT_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_CODE_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_CODE_ENABLE",strZC_UBB_CODE_ENABLE) Then
		strZC_UBB_CODE_ENABLE=TransferHTML(strZC_UBB_CODE_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG198&" :</p><p><input id=""edtZC_UBB_CODE_ENABLE"" name=""edtZC_UBB_CODE_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_CODE_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_FACE_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_FACE_ENABLE",strZC_UBB_FACE_ENABLE) Then
		strZC_UBB_FACE_ENABLE=TransferHTML(strZC_UBB_FACE_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG199&" :</p><p><input id=""edtZC_UBB_FACE_ENABLE"" name=""edtZC_UBB_FACE_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_FACE_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_IMAGE_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_IMAGE_ENABLE",strZC_UBB_IMAGE_ENABLE) Then
		strZC_UBB_IMAGE_ENABLE=TransferHTML(strZC_UBB_IMAGE_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG200&" :</p><p><input id=""edtZC_UBB_IMAGE_ENABLE"" name=""edtZC_UBB_IMAGE_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_IMAGE_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_MEDIA_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_MEDIA_ENABLE",strZC_UBB_MEDIA_ENABLE) Then
		strZC_UBB_MEDIA_ENABLE=TransferHTML(strZC_UBB_MEDIA_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG201&" :</p><p><input id=""edtZC_UBB_MEDIA_ENABLE"" name=""edtZC_UBB_MEDIA_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_MEDIA_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_FLASH_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_FLASH_ENABLE",strZC_UBB_FLASH_ENABLE) Then
		strZC_UBB_FLASH_ENABLE=TransferHTML(strZC_UBB_FLASH_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG202&" :</p><p><input id=""edtZC_UBB_FLASH_ENABLE"" name=""edtZC_UBB_FLASH_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_FLASH_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_TYPESET_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_TYPESET_ENABLE",strZC_UBB_TYPESET_ENABLE) Then
		strZC_UBB_TYPESET_ENABLE=TransferHTML(strZC_UBB_TYPESET_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG203&" :</p><p><input id=""edtZC_UBB_TYPESET_ENABLE"" name=""edtZC_UBB_TYPESET_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_TYPESET_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_UBB_AUTOLINK_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_AUTOLINK_ENABLE",strZC_UBB_AUTOLINK_ENABLE) Then
		strZC_UBB_AUTOLINK_ENABLE=TransferHTML(strZC_UBB_AUTOLINK_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG204&" :</p><p><input id=""edtZC_UBB_AUTOLINK_ENABLE"" name=""edtZC_UBB_AUTOLINK_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_AUTOLINK_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	'Dim strZC_UBB_AUTOKEY_ENABLE
	'If LoadValueForSetting(tmpSng,True,"Boolean","ZC_UBB_AUTOKEY_ENABLE",strZC_UBB_AUTOKEY_ENABLE) Then
	'	strZC_UBB_AUTOKEY_ENABLE=TransferHTML(strZC_UBB_AUTOKEY_ENABLE,"[html-format]")
	'	Response.Write "<p>※"&ZC_MSG205&" :</p><p><input id=""edtZC_UBB_AUTOKEY_ENABLE"" name=""edtZC_UBB_AUTOKEY_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_UBB_AUTOKEY_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	'End If

	Dim strZC_AUTO_NEWLINE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_AUTO_NEWLINE",strZC_AUTO_NEWLINE) Then
		strZC_AUTO_NEWLINE=TransferHTML(strZC_AUTO_NEWLINE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG206&" :</p><p><input id=""edtZC_AUTO_NEWLINE"" name=""edtZC_AUTO_NEWLINE"" style=""width:90%"" type=""text"" value=""" & strZC_AUTO_NEWLINE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_COMMENT_NOFOLLOW_ENABLE
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_COMMENT_NOFOLLOW_ENABLE",strZC_COMMENT_NOFOLLOW_ENABLE) Then
		strZC_COMMENT_NOFOLLOW_ENABLE=TransferHTML(strZC_COMMENT_NOFOLLOW_ENABLE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG207&" :</p><p><input id=""edtZC_COMMENT_NOFOLLOW_ENABLE"" name=""edtZC_COMMENT_NOFOLLOW_ENABLE"" style=""width:90%"" type=""text"" value=""" & strZC_COMMENT_NOFOLLOW_ENABLE & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_JAPAN_TO_HTML
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_JAPAN_TO_HTML",strZC_JAPAN_TO_HTML) Then
		strZC_JAPAN_TO_HTML=TransferHTML(strZC_JAPAN_TO_HTML,"[html-format]")
		Response.Write "<p>※"&ZC_MSG208&" :</p><p><input id=""edtZC_JAPAN_TO_HTML"" name=""edtZC_JAPAN_TO_HTML"" style=""width:90%"" type=""text"" value=""" & strZC_JAPAN_TO_HTML & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_EMOTICONS_FILENAME
	If LoadValueForSetting(tmpSng,True,"String","ZC_EMOTICONS_FILENAME",strZC_EMOTICONS_FILENAME) Then
		strZC_EMOTICONS_FILENAME=TransferHTML(strZC_EMOTICONS_FILENAME,"[html-format]")
		Response.Write "<p>※"&ZC_MSG235&" :</p><p><input id=""edtZC_EMOTICONS_FILENAME"" name=""edtZC_EMOTICONS_FILENAME"" style=""width:90%"" type=""text"" value=""" & strZC_EMOTICONS_FILENAME & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_EMOTICONS_FILESIZE
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_EMOTICONS_FILESIZE",strZC_EMOTICONS_FILESIZE) Then
		strZC_EMOTICONS_FILESIZE=TransferHTML(strZC_EMOTICONS_FILESIZE,"[html-format]")
		Response.Write "<p>※"&ZC_MSG234&" :</p><p><input id=""edtZC_EMOTICONS_FILESIZE"" name=""edtZC_EMOTICONS_FILESIZE"" style=""width:90%"" type=""text"" value=""" & strZC_EMOTICONS_FILESIZE & """ /></p><p></p>"
	End If


	Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" id=""btnPost"" onclick='document.getElementById(""edit"").action=""../cmd.asp?act=SettingSav"";' /></p>"
	Response.Write "<br/><br/>&nbsp;<b>"&ZC_MSG215&"</b>"
	Response.Write "<hr/>"

	Dim strZC_IE_DISPLAY_WAP
	If LoadValueForSetting(tmpSng,True,"Boolean","ZC_IE_DISPLAY_WAP",strZC_IE_DISPLAY_WAP) Then
		strZC_IE_DISPLAY_WAP=TransferHTML(strZC_IE_DISPLAY_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG216&" :</p><p><input id=""edtZC_IE_DISPLAY_WAP"" name=""edtZC_IE_DISPLAY_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_IE_DISPLAY_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_DISPLAY_COUNT_WAP
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_DISPLAY_COUNT_WAP",strZC_DISPLAY_COUNT_WAP) Then
		strZC_DISPLAY_COUNT_WAP=TransferHTML(strZC_DISPLAY_COUNT_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG217&" :</p><p><input id=""edtZC_DISPLAY_COUNT_WAP"" name=""edtZC_DISPLAY_COUNT_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_DISPLAY_COUNT_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_COMMENT_COUNT_WAP
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_COMMENT_COUNT_WAP",strZC_COMMENT_COUNT_WAP) Then
		strZC_COMMENT_COUNT_WAP=TransferHTML(strZC_COMMENT_COUNT_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG218&" :</p><p><input id=""edtZC_COMMENT_COUNT_WAP"" name=""edtZC_COMMENT_COUNT_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_COMMENT_COUNT_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_PAGEBAR_COUNT_WAP
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_PAGEBAR_COUNT_WAP",strZC_PAGEBAR_COUNT_WAP) Then
		strZC_PAGEBAR_COUNT_WAP=TransferHTML(strZC_PAGEBAR_COUNT_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG219&" :</p><p><input id=""edtZC_PAGEBAR_COUNT_WAP"" name=""edtZC_PAGEBAR_COUNT_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_PAGEBAR_COUNT_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_SINGLE_SIZE_WAP
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_SINGLE_SIZE_WAP",strZC_SINGLE_SIZE_WAP) Then
		strZC_SINGLE_SIZE_WAP=TransferHTML(strZC_SINGLE_SIZE_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG220&" :</p><p><input id=""edtZC_SINGLE_SIZE_WAP"" name=""edtZC_SINGLE_SIZE_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_SINGLE_SIZE_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_SINGLE_PAGEBAR_COUNT_WAP
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_SINGLE_PAGEBAR_COUNT_WAP",strZC_SINGLE_PAGEBAR_COUNT_WAP) Then
		strZC_SINGLE_PAGEBAR_COUNT_WAP=TransferHTML(strZC_SINGLE_PAGEBAR_COUNT_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG221&" :</p><p><input id=""edtZC_SINGLE_PAGEBAR_COUNT_WAP"" name=""edtZC_SINGLE_PAGEBAR_COUNT_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_SINGLE_PAGEBAR_COUNT_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_COMMENT_PAGEBAR_COUNT_WAP
	If LoadValueForSetting(tmpSng,True,"Numeric","ZC_COMMENT_PAGEBAR_COUNT_WAP",strZC_COMMENT_PAGEBAR_COUNT_WAP) Then
		strZC_COMMENT_PAGEBAR_COUNT_WAP=TransferHTML(strZC_COMMENT_PAGEBAR_COUNT_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG222&" :</p><p><input id=""edtZC_COMMENT_PAGEBAR_COUNT_WAP"" name=""edtZC_COMMENT_PAGEBAR_COUNT_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_COMMENT_PAGEBAR_COUNT_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Dim strZC_FILENAME_WAP
	If LoadValueForSetting(tmpSng,True,"String","ZC_FILENAME_WAP",strZC_FILENAME_WAP) Then
		strZC_FILENAME_WAP=TransferHTML(strZC_FILENAME_WAP,"[html-format]")
		Response.Write "<p>※"&ZC_MSG223&" :</p><p><input id=""edtZC_FILENAME_WAP"" name=""edtZC_FILENAME_WAP"" style=""width:90%"" type=""text"" value=""" & strZC_FILENAME_WAP & """ ONCLICK=""ChangeValue(this);""/></p><p></p>"
	End If

	Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" id=""btnPost"" onclick='document.getElementById(""edit"").action=""../cmd.asp?act=SettingSav"";' /></p>"

%>
</form>

			</div></div>
<script language="javascript">
function ChangeValue(obj){

	if (obj.value=="True")
	{
	obj.value="False";
	return true;
	}

	if (obj.value=="False")
	{
	obj.value="True";
	return true;
	}
}
</script>

</body>
</html>
<% 
Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>