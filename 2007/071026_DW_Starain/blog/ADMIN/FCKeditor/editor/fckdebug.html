<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fckdebug.html
 * 	This is the Debug window.
 * 	It automatically popups if the Debug = true in the configuration file.
 * 
 * File Authors: <AUTHORS>
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>FCKeditor Debug Window</title>
	<meta name="robots" content="noindex, nofollow" />
	<script type="text/javascript">

var oWindow ;
var oDiv ;

if ( !window.FCKMessages )
	window.FCKMessages = new Array() ;

window.onload = function()
{
	oWindow = document.getElementById('xOutput').contentWindow ;
	oWindow.document.open() ;
	oWindow.document.write( '<div id="divMsg"><\/div>' ) ;
	oWindow.document.close() ;
	oDiv	= oWindow.document.getElementById('divMsg') ;
}

function Output( message, color )
{
	if ( color )
		message = '<font color="' + color + '">' + message + '<\/font>' ;
		
	window.FCKMessages[ window.FCKMessages.length ] = message ;
	StartTimer() ;
}

function StartTimer()
{
	window.setTimeout( 'CheckMessages()', 100 ) ;
}

function CheckMessages()
{
	if ( window.FCKMessages.length > 0 )
	{
		// Get the first item in the queue
		var sMessage = window.FCKMessages[0] ;
		
		// Removes the first item from the queue
		var oTempArray = new Array() ;
		for ( i = 1 ; i < window.FCKMessages.length ; i++ )
			oTempArray[ i - 1 ] = window.FCKMessages[ i ] ;
		window.FCKMessages = oTempArray ;
		
		var d = new Date() ;
		var sTime = 
			( d.getHours() + 100 + '' ).substr( 1,2 ) + ':' + 
			( d.getMinutes() + 100 + '' ).substr( 1,2 ) + ':' + 
			( d.getSeconds() + 100 + '' ).substr( 1,2 ) + ':' + 
			( d.getMilliseconds() + 1000 + '' ).substr( 1,3 ) ;

		var oMsgDiv = oWindow.document.createElement( 'div' ) ;
		oMsgDiv.innerHTML = sTime + ': <b>' + sMessage + '<\/b>' ;
		oDiv.appendChild( oMsgDiv ) ;
		oMsgDiv.scrollIntoView() ;
	}
}

function Clear()
{
	oDiv.innerHTML = '' ;
}
	</script>
</head>
<body style="margin: 10px">
	<table style="height: 100%" cellspacing="5" cellpadding="0" width="100%" border="0">
		<tr>
			<td>
				<table cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font-weight: bold; font-size: 1.2em;">
							FCKeditor Debug Window</td>
						<td align="right">
							<input type="button" value="Clear" onclick="Clear();" /></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr style="height: 100%">
			<td style="border: #696969 1px solid">
				<iframe id="xOutput" width="100%" height="100%" scrolling="auto" src="fckblank.html"
					frameborder="0"></iframe>
			</td>
		</tr>
	</table>
</body>
</html>
