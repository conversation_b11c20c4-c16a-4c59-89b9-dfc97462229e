<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_placeholder.html
 * 	Placeholder Plugin.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Placeholder Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script language="javascript">

var oEditor = window.parent.InnerDialogLoaded() ;
var FCKLang = oEditor.FCKLang ;
var FCKPlaceholders = oEditor.FCKPlaceholders ;

window.onload = function ()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage( document ) ;
	
	LoadSelected() ;
	
	// Show the "Ok" button.
	window.parent.SetOkButton( true ) ;	
}

var eSelected = oEditor.FCKSelection.GetSelectedElement() ;

function LoadSelected()
{
	if ( !eSelected )
		return ;

	if ( eSelected.tagName == 'SPAN' && eSelected._fckplaceholder )
		document.getElementById('txtName').value = eSelected._fckplaceholder ;
	else
		eSelected == null ;
}

function Ok()
{
	var sValue = document.getElementById('txtName').value ;
	
	if ( eSelected && eSelected._fckplaceholder == sValue )
		return true ;

	if ( sValue.length == 0 )
	{
		alert( FCKLang.PlaceholderErrNoName ) ;
		return false ;
	}
	
	if ( FCKPlaceholders.Exist( sValue ) )
	{
		alert( FCKLang.PlaceholderErrNameInUse ) ;
		return false ;
	}

	FCKPlaceholders.Add( sValue ) ;
	return true ;
}

		</script>
	</head>
	<body scroll="no" style="OVERFLOW: hidden">
		<table height="100%" cellSpacing="0" cellPadding="0" width="100%" border="0">
			<tr>
				<td>
					<table cellSpacing="0" cellPadding="0" align="center" border="0">
						<tr>
							<td>
								<span fckLang="PlaceholderDlgName">Placeholder Name</span><br>
								<input id="txtName" type="text">
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>