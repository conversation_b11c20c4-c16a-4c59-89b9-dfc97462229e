/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_editorarea.css
 * 	This is the default CSS file used by the editor area. It defines the
 * 	initial font of the editor and background color.
 * 
 * 	A user can configure the editor to use another CSS file. Just change
 * 	the value of the FCKConfig.EditorAreaCSS key in the configuration
 * 	file.
 * 
 * File Authors: <AUTHORS>
 */

/*
    The "body" styles should match your editor web site, mainly regarding
    background color and font family and size.
*/

body
{
	background-color: #ffffff;
	padding: 5px 5px 5px 5px;
	margin: 0px;
}

body, td
{
	font-family: Arial, Verdana, Sans-Serif;
	font-size: 12px;
}

a
{
	color: #0000FF !important;	/* For Firefox... mark as important, otherwise it becomes black */
}

/* 
	Just uncomment the following block if you want to avoid spaces between 
	paragraphs. Remember to apply the same style in your output front end page.
*/

/*
P, UL, LI
{
	margin-top: 0px;
	margin-bottom: 0px;
}
*/

/*
    The following are some sample styles used in the "Styles" toolbar command.
    You should instead remove them, and include the styles used by the site
    you are using the editor in.
*/

.Bold
{
	font-weight: bold;
}

.Title
{
	font-weight: bold;
	font-size: 18px;
	color: #cc3300;
}

.Code
{
	border: #8b4513 1px solid;
	padding-right: 5px;
	padding-left: 5px;
	color: #000066;
	font-family: 'Courier New' , Monospace;
	background-color: #ff9933;
}