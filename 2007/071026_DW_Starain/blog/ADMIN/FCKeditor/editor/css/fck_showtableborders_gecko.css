/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_showtableborders_gecko.css
 * 	This CSS Style Sheet defines the rules to show table borders on Gecko.
 * 
 * File Authors: <AUTHORS>
 */

/* For tables with the "border" attribute set to "0" */
table[border="0"], 
table[border="0"] > tr > td, table[border="0"] > tr > th, 
table[border="0"] > tbody > tr > td, table[border="0"] > tbody > tr > th, 
table[border="0"] > thead > tr > td, table[border="0"] > thead > tr > th, 
table[border="0"] > tfoot > tr > td, table[border="0"] > tfoot > tr > th
{
	border: #d3d3d3 1px dotted ;
}

/* For tables with no "border" attribute set */
table:not([border]), 
table:not([border]) > tr > td, table:not([border]) > tr > th,
table:not([border]) > tbody > tr > td, table:not([border]) > tbody > tr > th,
table:not([border]) > thead > tr > td, table:not([border]) > thead > tr > th,
table:not([border]) > tfoot > tr > td, table:not([border]) > tfoot > tr > th
{
	border: #d3d3d3 1px dotted ;
}
