<public:component lightweight="true">

<public:attach event="oncontentready" onevent="ShowField()" />
<public:attach event="ondoubleclick" onevent="EditField()" />

<script language="javascript">

var bIsHiddenField = this.type == 'hidden' ;

function ShowField()
{
	if ( bIsHiddenField ) 
	{
		this.runtimeStyle.width				= '20px' ;
		this.runtimeStyle.height			= '20px' ;
		this.runtimeStyle.border			= '1px dotted #FF0000' ;
		this.runtimeStyle.backgroundImage	= 'url(css/behaviors/hiddenfield.gif)' ;
		this.runtimeStyle.fontSize			= '99px' ;
	}
}

function EditField()
{
	if ( bIsHiddenField ) 
		alert( this.outerHTML ) ;
}

</script>

</public:component>
