/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_dialog.css
 * 	Styles used by the dialog boxes.
 * 
 * File Authors: <AUTHORS>
 */

body
{
	margin: 0px;
	padding: 10px;
	background-color: #f7f7f7;
}

body, td, input, select, textarea
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

body, .BackColor
{
	background-color: #f7f7f7;
}

.PopupBody
{
	margin: 0px;
	padding: 0px;
}

.PopupTitle
{
	padding-right: 10px;
	padding-left: 10px;
	font-weight: bold;
	font-size: 14pt;
	padding-bottom: 3px;
	color: #504845;
	padding-top: 3px;
	background-color: #dedede;
}

.Popup<PERSON>uttons
{
	border-top: #cec6b5 1px solid;
	background-color: #DEDEDE;
	padding: 7px 10px 7px 10px;
}

.Button
{
	border: #7a7261 1px solid;
	color: #504845;
	background-color: #cec6b5;
}

.DarkBackground
{
	background-color: #d7d79f;
}

.LightBackground
{
	background-color: #ffffbe;
}

.PopupTitleBorder
{
	border-bottom: #cec6b5 1px solid;
}

.PopupTabArea
{
	color: #504845;
	background-color: #DEDEDE;
}

.PopupTabEmptyArea
{
	padding-left: 10px ;
	border-bottom: #cec6b5 1px solid;
}

.PopupTab, .PopupTabSelected
{
	border-right: #cec6b5 1px solid;
	border-top: #cec6b5 1px solid;
	border-left: #cec6b5 1px solid;
	padding-right: 5px;
	padding-left: 5px;
	padding-bottom: 3px;
	padding-top: 3px;
	color: #504845;
}

.PopupTab
{
	margin-top: 1px;
	border-bottom: #cec6b5 1px solid;
	cursor: pointer;
	cursor: hand;
}

.PopupTabSelected
{
	font-weight:bold;
	cursor: default;
	padding-top: 4px;
	border-bottom: #f1f1e3 1px solid;
	background-color: #f7f7f7;
}

.PopupSelectionBox
{
	border: #a9a9a9 1px solid;
	background-color: #dcdcdc;
	cursor: pointer;
	cursor: hand;
}