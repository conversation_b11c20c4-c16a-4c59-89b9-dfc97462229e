/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_dialog.css
 * 	Styles used by the dialog boxes.
 * 
 * File Authors: <AUTHORS>
 */

body
{
	margin: 0px;
	padding: 10px;
}

body, td, input, select, textarea
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

body, .BackColor
{
	background-color: #f1f1e3;
}

.PopupBody
{
	margin: 0px;
	padding: 0px;
}

.PopupTitle
{
	font-weight: bold;
	font-size: 14pt;
	color: #737357;
	background-color: #e3e3c7;
	padding: 3px 10px 3px 10px;
}

.PopupButtons
{
	border-top: #d5d59d 1px solid;
	background-color: #e3e3c7;
	padding: 7px 10px 7px 10px;
}

.<PERSON><PERSON>
{
	border-right: #737357 1px solid;
	border-top: #737357 1px solid;
	border-left: #737357 1px solid;
	color: #3b3b1f;
	border-bottom: #737357 1px solid;
	background-color: #c7c78f;
}

.DarkBackground
{
	background-color: #d7d79f;
}

.LightBackground
{
	background-color: #ffffbe;
}

.PopupTitleBorder
{
	border-bottom: #d5d59d 1px solid;
}

.PopupTabArea
{
	color: #737357;
	background-color: #e3e3c7;
}

.PopupTabEmptyArea
{
	padding-left: 10px ;
	border-bottom: #d5d59d 1px solid;
}

.PopupTab, .PopupTabSelected
{
	border-right: #d5d59d 1px solid;
	border-top: #d5d59d 1px solid;
	border-left: #d5d59d 1px solid;
	padding-right: 5px;
	padding-left: 5px;
	padding-bottom: 3px;
	padding-top: 3px;
	color: #737357;
}

.PopupTab
{
	margin-top: 1px;
	border-bottom: #d5d59d 1px solid;
	cursor: pointer;
	cursor: hand;
}

.PopupTabSelected
{
	font-weight:bold;
	cursor: default;
	padding-top: 4px;
	border-bottom: #f1f1e3 1px solid;
	background-color: #f1f1e3;
}

.PopupSelectionBox
{
	border: #ff9933 1px solid;
	background-color: #fffacd;
	cursor: pointer;
	cursor: hand;
}