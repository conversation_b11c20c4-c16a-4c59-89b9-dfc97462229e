/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_editor.css
 * 	Styles used by the editor IFRAME and Toolbar.
 * 
 * File Authors: <AUTHORS>
 */

/*
	### Basic Editor IFRAME Styles.
*/

body
{
    padding: 1px 1px 1px 1px;
    margin: 0px 0px 0px 0px;
}

#xEditingArea
{
    border: #696969 1px solid;
}

.SourceField
{
    padding: 5px;
    margin: 0px;
    font-family: Monospace;
}

/*
	Toolbar
*/

.TB_ToolbarSet, .TB_Expand, .TB_Collapse
{
    cursor: default;
    background-color: #efefde;
}

.TB_ToolbarSet
{
    border-top: #efefde 1px outset;
    border-bottom: #efefde 1px outset;
}

.TB_ToolbarSet TD
{
    font-size: 11px;
    font-family: 'Microsoft Sans Serif' , <PERSON><PERSON><PERSON>, <PERSON>l, Verdana, Sans-Serif;
}

.TB_Toolbar
{
    display: inline;
    /* display: inline-table;	/* Opera "jumping buttons" bug */
}

.TB_Separator
{
    width: 1px;
    height: 16px;
    margin: 2px;
    background-color: #999966;
}

.TB_Start
{
    background-image: url(images/toolbar.start.gif);
    margin: 2px;
    width: 3px;
    background-repeat: no-repeat;
    height: 16px;
}

.TB_End
{
    display: none;
}

.TB_ExpandImg
{
    background-image: url(images/toolbar.expand.gif);
    background-repeat: no-repeat;
}

.TB_CollapseImg
{
    background-image: url(images/toolbar.collapse.gif);
    background-repeat: no-repeat;
}

.TB_SideBorder
{
    background-color: #696969;
}

.TB_Expand, .TB_Collapse
{
    padding: 2px 2px 2px 2px;
    border: #efefde 1px outset;
}

.TB_Collapse
{
    width: 5px;
}

.TB_Break
{
    height: 24px; /* IE needs the height to be set, otherwise no break */
}

/*
	Toolbar Button
*/

.TB_Button_On, .TB_Button_Off, .TB_Button_On_Over, .TB_Button_Off_Over, .TB_Button_Disabled
{
    border: #efefde 1px solid; /* This is the default border */
    height: 22px; /* The height is necessary, otherwise IE will not apply the alpha */
}

.TB_Button_On
{
    border: #316ac5 1px solid;
    background-color: #c1d2ee;
}

.TB_Button_On_Over, .TB_Button_Off_Over
{
    border: #316ac5 1px solid;
    background-color: #dff1ff;
}

.TB_Button_Off
{
    filter: alpha(opacity=70); /* IE */
    opacity: 0.70; /* Safari, Opera and Mozilla */
}

.TB_Button_Disabled
{
    filter: gray() alpha(opacity=30); /* IE */
    opacity: 0.30; /* Safari, Opera and Mozilla */
}

.TB_Button_Padding
{
    visibility: hidden;
    width: 3px;
    height: 22px;
}

.TB_Button_Image
{
    overflow: hidden;
    width: 16px;
    height: 16px;
    margin: 3px;
    background-repeat: no-repeat;
}

.TB_Button_Image img
{
    position: relative;
}

.TB_Button_Off .TB_Button_Text
{
   	background-color: #efefde;  /* Needed because of a bug on Clear Type */
}

.TB_ConnectionLine
{
    background-color: #ffffff;
    height: 1px;
    margin-left: 1px;   /* ltr */
    margin-right: 1px;  /* rtl */
}

.TB_Text
{
	height: 22px;
}

.TB_Button_Off .TB_Text
{
   	background-color: #efefde ;  /* Needed because of a bug on ClearType */
}

.TB_Button_On_Over .TB_Text
{
   	background-color: #dff1ff ;  /* Needed because of a bug on ClearType */
}

/*
	Menu
*/

.MN_Menu
{
    border: 1px solid #8f8f73;
    padding: 2px;
    background-color: #ffffff;
    cursor: default;
}

.MN_Menu, .MN_Menu .MN_Label
{
    font-size: 11px;
    font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
}

.MN_Item_Padding
{
    visibility: hidden;
    width: 3px;
    height: 20px;
}

.MN_Icon
{
    background-color: #e3e3c7;
    text-align: center;
    height: 20px;
}

.MN_Label
{
    padding-left: 3px;
    padding-right: 3px;
}

.MN_Separator
{
    height: 3px;
}

.MN_Separator_Line
{
    border-top: #b9b99d 1px solid;
}

.MN_Item .MN_Icon IMG
{
    filter: alpha(opacity=70);
    opacity: 0.70;
}

.MN_Item_Over
{
    color: #ffffff;
    background-color: #8f8f73;
}

.MN_Item_Over .MN_Icon
{
    background-color: #737357;
}

.MN_Item_Disabled IMG
{
    filter: gray() alpha(opacity=30); /* IE */
    opacity: 0.30; /* Safari, Opera and Mozilla */
}

.MN_Item_Disabled .MN_Label
{
    color: #b7b7b7;
}

.MN_Arrow
{
    padding-right: 3px;
    padding-left: 3px;
}

.MN_ConnectionLine
{
    background-color: #ffffff;
}

.Menu .TB_Button_On, .Menu .TB_Button_On_Over
{
    border: #8f8f73 1px solid;
    background-color: #ffffff;
}

/*
	### Panel Styles
*/

.FCK_Panel
{
    border: #8f8f73 1px solid;
    padding: 2px;
    background-color: #ffffff;
}

.FCK_Panel, .FCK_Panel TD
{
    font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
    font-size: 11px;
}

/*
	### Special Combos
*/

.SC_Panel
{
    overflow: auto;
    white-space: nowrap;
    cursor: default;
    border: 1px solid #8f8f73;
    padding-left: 2px;
    padding-right: 2px;
    background-color: #ffffff;
}

.SC_Panel, .SC_Panel TD
{
    font-size: 11px;
    font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
}

.SC_Item, .SC_ItemSelected
{
    margin-top: 2px;
    margin-bottom: 2px;
    background-position: left center;
    padding-left: 11px;
    padding-right: 3px;
    padding-top: 2px;
    padding-bottom: 2px;
    text-overflow: ellipsis;
    overflow: hidden;
    background-repeat: no-repeat;
    border: #dddddd 1px solid;
}

.SC_Item *, .SC_ItemSelected *
{
    margin-top: 0px;
    margin-bottom: 0px;
}

.SC_ItemSelected
{
    border: #9a9afb 1px solid;
    background-image: url(images/toolbar.arrowright.gif);
}

.SC_ItemOver
{
    border: #316ac5 1px solid;
}

.SC_Field
{
    border: #b7b7a6 1px solid;
    cursor: default;
}

.SC_FieldCaption
{
    overflow: visible;
    padding-right: 5px;
    padding-left: 5px;
    opacity: 0.75; /* Safari, Opera and Mozilla */
    filter: alpha(opacity=70); /* IE */ /* -moz-opacity: 0.75; Mozilla (Old) */
    height: 23px;
    background-color: #efefde;
}

.SC_FieldLabel
{
    white-space: nowrap;
    padding: 2px;
    width: 100%;
    cursor: default;
    background-color: #ffffff;
    text-overflow: ellipsis;
    overflow: hidden;
}

.SC_FieldButton
{
    background-position: center center;
    background-image: url(images/toolbar.buttonarrow.gif);
    border-left: #b7b7a6 1px solid;
    width: 14px;
    background-repeat: no-repeat;
}

.SC_FieldDisabled .SC_FieldButton, .SC_FieldDisabled .SC_FieldCaption
{
    opacity: 0.30; /* Safari, Opera and Mozilla */
    filter: gray() alpha(opacity=30); /* IE */ /* -moz-opacity: 0.30; Mozilla (Old) */
}

.SC_FieldOver
{
    border: #316ac5 1px solid;
}

.SC_FieldOver .SC_FieldButton
{
    border-left: #316ac5 1px solid;
}

/*
	### Color Selector Panel
*/

.ColorBoxBorder
{
    border: #808080 1px solid;
    position: static;
}

.ColorBox
{
    font-size: 1px;
    width: 10px;
    position: static;
    height: 10px;
}

.ColorDeselected, .ColorSelected
{
    cursor: default;
}

.ColorDeselected
{
    border: #ffffff 1px solid;
    padding: 2px;
    float: left;
}

.ColorSelected
{
    border: #330066 1px solid;
    padding: 2px;
    float: left;
    background-color: #c4cdd6;
}
