<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_form.html
 * 	Checkbox dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Checkbox Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = oEditor.FCKSelection.MoveToAncestorNode( 'FORM' ) ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl ) 
	{
		GetE('txtName').value	= oActiveEl.name ;
		GetE('txtAction').value	= oActiveEl.action ;
		GetE('txtMethod').value	= oActiveEl.method ;
	}
	else
		oActiveEl = null ;

	window.parent.SetOkButton( true ) ;
}

function Ok()
{
	if ( !oActiveEl )
	{
		oActiveEl = oEditor.FCK.EditorDocument.createElement( 'FORM' ) ;
		oActiveEl = oEditor.FCK.InsertElementAndGetIt( oActiveEl ) ;
		oActiveEl.innerHTML = '&nbsp;' ;
	}
	
	oActiveEl.name = GetE('txtName').value ;
	SetAttribute( oActiveEl, 'action'	, GetE('txtAction').value ) ;
	oActiveEl.method = GetE('txtMethod').value ;

	return true ;
}

		</script>
	</head>
	<body style="OVERFLOW: hidden" scroll="no">
		<table height="100%" width="100%">
			<tr>
				<td>
					<table cellspacing="0" cellpadding="0" width="80%" border="0" valign="top" align="center">
						<tr>
							<td>
								<span fckLang="DlgFormName">Name</span><br>
								<input style="WIDTH: 100%" type="text" id="txtName">
							</td>
						</tr>
						<tr>
							<td>
								<span fckLang="DlgFormAction">Action</span><br>
								<input style="WIDTH: 100%" type="text" id="txtAction">
							</td>
						</tr>
						<tr>
							<td>
								<span fckLang="DlgFormMethod">Method</span><br>
								<select id="txtMethod">
									<option value="get" selected>GET</option>
									<option value="post">POST</option>
								</select>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>
