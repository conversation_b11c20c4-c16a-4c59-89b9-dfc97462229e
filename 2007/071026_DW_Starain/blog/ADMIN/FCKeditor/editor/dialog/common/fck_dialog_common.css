/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_dialog_common.css
 * 	This is the CSS file used for interface details in some dialog
 * 	windows.
 * 
 * File Authors: <AUTHORS>
 */

.ImagePreviewArea
{
	border: #000000 1px solid;
	overflow: auto;
	width: 100%;
	height: 170px;
	background-color: #ffffff;
}

.FlashPreviewArea
{
	border: #000000 1px solid;
	padding: 5px;
	overflow: auto;
	width: 100%;
	height: 170px;
	background-color: #ffffff;
}

.BtnReset
{
	float: left;
	background-position: center center;
	background-image: url(images/reset.gif);
	width: 16px;
	height: 16px;
	background-repeat: no-repeat;
	border: 1px none;
	font-size: 1px ;
}

.BtnLocked, .BtnUnlocked
{
	float: left;
	background-position: center center;
	background-image: url(images/locked.gif);
	width: 16px;
	height: 16px;
	background-repeat: no-repeat;
	border: 1px none;
	font-size: 1px ;
}

.BtnUnlocked
{
	background-image: url(images/unlocked.gif);
}

.BtnOver
{
	border: 1px outset;
	cursor: pointer;
	cursor: hand;
}

.FCK__FieldNumeric
{
	behavior: url(common/fcknumericfield.htc) ;
}