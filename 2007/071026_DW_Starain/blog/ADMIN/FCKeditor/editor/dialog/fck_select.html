<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_select.html
 * 	Select dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Select Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript" src="fck_select/fck_select.js"></script>
		<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = oEditor.FCKSelection.GetSelectedElement() ;

var oListText ;
var oListValue ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	oListText	= document.getElementById( 'cmbText' ) ;
	oListValue	= document.getElementById( 'cmbValue' ) ;

	if ( oActiveEl && oActiveEl.tagName == 'SELECT' )
	{
		GetE('txtName').value		= oActiveEl.name ;
		GetE('txtSelValue').value	= oActiveEl.value ;
		GetE('txtLines').value		= GetAttribute( oActiveEl, 'size' ) ;
		GetE('chkMultiple').checked	= oActiveEl.multiple ;

		// Load the actual options
		for ( var i = 0 ; i < oActiveEl.options.length ; i++ )
		{
			var sText	= oActiveEl.options[i].innerHTML ;
			var sValue	= oActiveEl.options[i].value ;

			AddComboOption( oListText, sText, sText ) ;
			AddComboOption( oListValue, sValue, sValue ) ;
		}
	}
	else
		oActiveEl = null ;

	window.parent.SetOkButton( true ) ;
}

function Ok()
{
	var sSize = GetE('txtLines').value ;
	if ( sSize == null || isNaN( sSize ) || sSize <= 1 )
		sSize = '' ;

	if ( !oActiveEl )
	{
		oActiveEl = oEditor.FCK.EditorDocument.createElement( 'SELECT' ) ;
		oActiveEl = oEditor.FCK.InsertElementAndGetIt( oActiveEl ) ;
	}

	SetAttribute( oActiveEl, 'name'	, GetE('txtName').value ) ;
	SetAttribute( oActiveEl, 'size'	, sSize ) ;
	oActiveEl.multiple = ( sSize.length > 0 && GetE('chkMultiple').checked ) ;

	// Remove all options.
	while ( oActiveEl.options.length > 0 )
		oActiveEl.remove(0) ;

	// Add all available options.
	for ( var i = 0 ; i < oListText.options.length ; i++ )
	{
		var sText	= oListText.options[i].value ;
		var sValue	= oListValue.options[i].value ;
		if ( sValue.length == 0 ) sValue = sText ;

		var oOption = AddComboOption( oActiveEl, sText, sValue, oDOM ) ;

		if ( sValue == GetE('txtSelValue').value )
		{
			SetAttribute( oOption, 'selected', 'selected' ) ;
			oOption.selected = true ;
		}
	}

	return true ;
}

		</script>
	</head>
	<body style='OVERFLOW: hidden' scroll='no'>
		<table width="100%" height="100%">
			<tr>
				<td>
					<table width="100%">
						<tr>
							<td nowrap><span fckLang="DlgSelectName">Name</span>&nbsp;</td>
							<td width="100%" colSpan="2"><input id="txtName" style="WIDTH: 100%" type="text"></td>
						</tr>
						<tr>
							<td nowrap><span fckLang="DlgSelectValue">Value</span>&nbsp;</td>
							<td width="100%" colSpan="2"><input id="txtSelValue" style="WIDTH: 100%; BACKGROUND-COLOR: buttonface" type="text" readonly></td>
						</tr>
						<tr>
							<td nowrap><span fckLang="DlgSelectSize">Size</span>&nbsp;</td>
							<td nowrap><input id="txtLines" type="text" size="2" value="">&nbsp;<span fckLang="DlgSelectLines">lines</span></td>
							<td nowrap align="right"><input id="chkMultiple" name="chkMultiple" type="checkbox"><label for="chkMultiple" fckLang="DlgSelectChkMulti">Allow
									multiple selections</label></td>
						</tr>
					</table>
					<br>
					<hr style="POSITION: absolute">
					<span style="LEFT: 10px; POSITION: relative; TOP: -7px" class="BackColor">&nbsp;<span fckLang="DlgSelectOpAvail">Available
							Options</span>&nbsp;</span>
					<table width="100%">
						<tr>
							<td width="50%"><span fckLang="DlgSelectOpText">Text</span><br>
								<input id="txtText" style="WIDTH: 100%" type="text" name="txtText">
							</td>
							<td width="50%"><span fckLang="DlgSelectOpValue">Value</span><br>
								<input id="txtValue" style="WIDTH: 100%" type="text" name="txtValue">
							</td>
							<td vAlign="bottom"><input onclick="Add();" type="button" fckLang="DlgSelectBtnAdd" value="Add"></td>
							<td vAlign="bottom"><input onclick="Modify();" type="button" fckLang="DlgSelectBtnModify" value="Modify"></td>
						</tr>
						<tr>
							<td rowSpan="2"><select id="cmbText" style="WIDTH: 100%" onchange="GetE('cmbValue').selectedIndex = this.selectedIndex;Select(this);"
									size="5" name="cmbText"></select>
							</td>
							<td rowSpan="2"><select id="cmbValue" style="WIDTH: 100%" onchange="GetE('cmbText').selectedIndex = this.selectedIndex;Select(this);"
									size="5" name="cmbValue"></select>
							</td>
							<td vAlign="top" colSpan="2">
							</td>
						</tr>
						<tr>
							<td vAlign="bottom" colSpan="2"><input style="WIDTH: 100%" onclick="Move(-1);" type="button" fckLang="DlgSelectBtnUp" value="Up">
								<br>
								<input style="WIDTH: 100%" onclick="Move(1);" type="button" fckLang="DlgSelectBtnDown"
									value="Down">
							</td>
						</tr>
						<TR>
							<TD vAlign="bottom" colSpan="4"><INPUT onclick="SetSelectedValue();" type="button" fckLang="DlgSelectBtnSetValue" value="Set as selected value">&nbsp;&nbsp;
								<input onclick="Delete();" type="button" fckLang="DlgSelectBtnDelete" value="Delete"></TD>
						</TR>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>
