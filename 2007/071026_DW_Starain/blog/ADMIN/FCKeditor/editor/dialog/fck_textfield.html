<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_textfield.html
 * 	Text field dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Text Field Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = oEditor.FCKSelection.GetSelectedElement() ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl && oActiveEl.tagName == 'INPUT' && ( oActiveEl.type == 'text' || oActiveEl.type == 'password' ) )
	{
		GetE('txtName').value	= oActiveEl.name ;
		GetE('txtValue').value	= oActiveEl.value ;
		GetE('txtSize').value	= GetAttribute( oActiveEl, 'size' ) ;
		GetE('txtMax').value	= GetAttribute( oActiveEl, 'maxLength' ) ;
		GetE('txtType').value	= oActiveEl.type ;

		GetE('txtType').disabled = true ;
	}
	else
		oActiveEl = null ;

	window.parent.SetOkButton( true ) ;
}

function Ok()
{
	if ( isNaN( GetE('txtMax').value ) || GetE('txtMax').value < 0 )
	{
		alert( "Maximum characters must be a positive number." ) ;
		GetE('txtMax').focus() ;
		return false ;
	}
	else if( isNaN( GetE('txtSize').value ) || GetE('txtSize').value < 0 )
	{
		alert( "Width must be a positive number." ) ;
		GetE('txtSize').focus() ;
		return false ;
	}

	if ( !oActiveEl )
	{
		oActiveEl = oEditor.FCK.EditorDocument.createElement( 'INPUT' ) ;
		oActiveEl.type = GetE('txtType').value ;
		oActiveEl = oEditor.FCK.InsertElementAndGetIt( oActiveEl ) ;
	}

	oActiveEl.name = GetE('txtName').value ;
	SetAttribute( oActiveEl, 'value'	, GetE('txtValue').value ) ;
	SetAttribute( oActiveEl, 'size'		, GetE('txtSize').value ) ;
	SetAttribute( oActiveEl, 'maxlength', GetE('txtMax').value ) ;

	return true ;
}

		</script>
	</head>
	<body style="OVERFLOW: hidden" scroll="no">
		<table height="100%" width="100%">
			<tr>
				<td align="center">
					<table cellSpacing="0" cellPadding="0" border="0">
						<tr>
							<td>
								<span fckLang="DlgTextName">Name</span><br>
								<input id="txtName" type="text" size="20">
							</td>
							<td></td>
							<td>
								<span fckLang="DlgTextValue">Value</span><br>
								<input id="txtValue" type="text" size="25">
							</td>
						</tr>
						<tr>
							<td>
								<span fckLang="DlgTextCharWidth">Character Width</span><br>
								<input id="txtSize" type="text" size="5">
							</td>
							<td></td>
							<td>
								<span fckLang="DlgTextMaxChars">Maximum Characters</span><br>
								<input id="txtMax" type="text" size="5">
							</td>
						</tr>
						<tr>
							<td>
								<span fckLang="DlgTextType">Type</span><br>
								<select id="txtType">
									<option value="text" selected fckLang="DlgTextTypeText">Text</option>
									<option value="password" fckLang="DlgTextTypePass">Password</option>
								</select>
							</td>
							<td>&nbsp;</td>
							<td></td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>
