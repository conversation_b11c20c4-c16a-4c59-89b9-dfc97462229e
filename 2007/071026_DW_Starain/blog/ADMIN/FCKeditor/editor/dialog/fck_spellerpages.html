<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_spellerpages.html
 * 	Spell Check dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Spell Check</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="fck_spellerpages/spellerpages/spellChecker.js"></script>
		<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;
var FCKLang = oEditor.FCKLang ;

window.onload = function()
{
	document.getElementById('txtHtml').value = oEditor.FCK.EditorDocument.body.innerHTML ;

	var oSpeller = new spellChecker( document.getElementById('txtHtml') ) ;
	oSpeller.OnFinished = oSpeller_OnFinished ;
	oSpeller.openChecker() ;
}

function OnSpellerControlsLoad( controlsWindow )
{
	// Translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage( controlsWindow.document ) ;
}

function oSpeller_OnFinished( numberOCorrections )
{
	if ( numberOCorrections > 0 )
		oEditor.FCK.SetHTML( document.getElementById('txtHtml').value ) ;
	window.parent.Cancel() ;
}

		</script>
	</head>
	<body style="OVERFLOW: hidden" scroll="no" style="padding:0px;">
		<input type="hidden" id="txtHtml" value="">
		<iframe id="frmSpell" src="../fckblank.html" name="spellchecker" width="100%" height="100%" frameborder="0"></iframe>
	</body>
</html>
