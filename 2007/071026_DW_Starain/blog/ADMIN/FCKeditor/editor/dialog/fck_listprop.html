<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_listprop.html
 * 	Bulleted List dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Bulleted List Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = oEditor.FCKSelection.MoveToAncestorNode( 'UL' ) ;
var oActiveSel ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl )
		oActiveSel = GetE('selBulleted') ;
	else
	{
		oActiveEl = oEditor.FCKSelection.MoveToAncestorNode( 'OL' ) ;
		if ( oActiveEl )
			oActiveSel = GetE('selNumbered') ;
	}

	oActiveSel.style.display = '' ;

	if ( oActiveEl )
	{
		if ( oActiveEl.getAttribute('type') )
			oActiveSel.value = oActiveEl.getAttribute('type').toLowerCase() ;
	}

	window.parent.SetOkButton( true ) ;
}

function Ok()
{
	if ( oActiveEl )
		SetAttribute( oActiveEl, 'type'	, oActiveSel.value ) ;

	return true ;
}

		</script>
	</head>
	<body style="OVERFLOW: hidden" scroll="no">
		<table width="100%" height="100%">
			<tr>
				<td>
					<table cellspacing="0" cellpadding="0" border="0" align="center">
						<tr>
							<td>
								<span fckLang="DlgLstType">List Type</span><br>
								<select id="selBulleted" style="DISPLAY: none">
									<option value="" selected></option>
									<option value="circle" fckLang="DlgLstTypeCircle">Circle</option>
									<option value="disc" fckLang="DlgLstTypeDisc">Disc</option>
									<option value="square" fckLang="DlgLstTypeSquare">Square</option>
								</select>
								<select id="selNumbered" style="DISPLAY: none">
									<option value="" selected></option>
									<option value="1" fckLang="DlgLstTypeNumbers">Numbers (1, 2, 3)</option>
									<option value="a" fckLang="DlgLstTypeLCase">Lowercase Letters (a, b, c)</option>
									<option value="A" fckLang="DlgLstTypeUCase">Uppercase Letters (A, B, C)</option>
									<option value="i" fckLang="DlgLstTypeSRoman">Small Roman Numerals (i, ii, iii)</option>
									<option value="I" fckLang="DlgLstTypeLRoman">Large Roman Numerals (I, II, III)</option>
								</select>
								&nbsp;
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>
