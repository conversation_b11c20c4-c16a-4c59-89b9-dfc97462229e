<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_about.html
 * 	"About" dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="robots" content="noindex, nofollow">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script language="javascript">

var oEditor = window.parent.InnerDialogLoaded() ;
var FCKLang	= oEditor.FCKLang ;

window.parent.AddTab( 'About', FCKLang.DlgAboutAboutTab ) ;
window.parent.AddTab( 'License', FCKLang.DlgAboutLicenseTab ) ;
window.parent.AddTab( 'BrowserInfo', FCKLang.DlgAboutBrowserInfoTab ) ;

// Function called when a dialog tag is selected.
function OnDialogTabChange( tabCode )
{
	ShowE('divAbout', ( tabCode == 'About' ) ) ;
	ShowE('divLicense', ( tabCode == 'License' ) ) ;
	ShowE('divInfo'	, ( tabCode == 'BrowserInfo' ) ) ;
}

function SendEMail()
{
	var eMail = 'mailto:' ;
	eMail += 'fredck' ;
	eMail += '@' ;
	eMail += 'fckeditor' ;
	eMail += '.' ;
	eMail += 'net' ;

	window.location = eMail ;
}

window.onload = function()
{
	// Translate the dialog box texts.
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	window.parent.SetAutoSize( true ) ;
}

		</script>
	</head>
	<body scroll="no" style="OVERFLOW: hidden">
		<div id="divAbout">
			<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%">
				<tr>
					<td>
						<img alt="" src="fck_about/logo_fckeditor.gif" width="236" height="41" align="left">
						<table width="80" border="0" cellspacing="0" cellpadding="5" bgcolor="#ffffff" align="right">
							<tr>
								<td align="center" nowrap style="BORDER-RIGHT: #000000 1px solid; BORDER-TOP: #000000 1px solid; BORDER-LEFT: #000000 1px solid; BORDER-BOTTOM: #000000 1px solid">
									<span fckLang="DlgAboutVersion">version</span>
									<br>
									<b>2.3</b><br />
									Build 1054</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr height="100%">
					<td align="center">
						&nbsp;<br>
						<span style="FONT-SIZE: 14px" dir="ltr">Support <b>Open Source</b> software.<br>
							<b><a href="http://www.fckeditor.net/donate/?about" target="_blank" title="Click to go to the donation page">
									What about a donation today?</a></b> </span>
						<br><br><br>
						<span fckLang="DlgAboutInfo">For further information go to</span> <a href="http://www.fckeditor.net/?About" target="_blank">
							http://www.fckeditor.net/</a>.
						<br>
						Copyright &copy; 2003-2006 <a href="#" onclick="SendEMail();">Frederico Caldeira 
							Knabben</a>
					</td>
				</tr>
				<tr>
					<td align="center">
						<img alt="" src="fck_about/logo_fredck.gif" width="87" height="36">
					</td>
				</tr>
			</table>
		</div>
		<div id="divLicense" style="DISPLAY: none">
			<table height="100%" width="100%">
				<tr>
					<td>
						<span fckLang="DlgAboutLicense">Licensed under the terms of the GNU Lesser General 
							Public License</span>
						<br>
						<a href="http://www.opensource.org/licenses/lgpl-license.php" target="_blank">http://www.opensource.org/licenses/lgpl-license.php</a>
						<br>
					</td>
				</tr>
				<tr>
					<td height="100%">
						<iframe height="100%" width="100%" src="fck_about/lgpl.html"></iframe>
					</td>
				</tr>
			</table>
		</div>
		<div id="divInfo" style="DISPLAY: none" dir="ltr">
			<table align="center" width="80%" border="0">
				<tr>
					<td>
						<script language="javascript">
<!--
document.write( '<b>User Agent<\/b><br>' + window.navigator.userAgent + '<br><br>' ) ;
document.write( '<b>Browser<\/b><br>' + window.navigator.appName + ' ' + window.navigator.appVersion + '<br><br>' ) ;
document.write( '<b>Platform<\/b><br>' + window.navigator.platform + '<br><br>' ) ;

var sUserLang = '?' ;

if ( window.navigator.language )
	sUserLang = window.navigator.language.toLowerCase() ;
else if ( window.navigator.userLanguage )
	sUserLang = window.navigator.userLanguage.toLowerCase() ;

document.write( '<b>User Language<\/b><br>' + sUserLang ) ;
//-->
						</script>
					</td>
				</tr>
			</table>
		</div>
	</body>
</html>
