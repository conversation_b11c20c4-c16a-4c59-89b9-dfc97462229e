<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_source.html
 * 	Source editor dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<html>
	<head>
		<title>Source</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="robots" content="noindex, nofollow">
		<link href="common/fck_dialog_common.css" rel="stylesheet" type="text/css" />
		<script language="javascript">
		
var oEditor		= window.parent.InnerDialogLoaded() ;
var FCK			= oEditor.FCK ;
var FCKConfig	= oEditor.FCKConfig ;

window.onload = function()
{
	// EnableXHTML and EnableSourceXHTML has been deprecated
//	document.getElementById('txtSource').value = ( FCKConfig.EnableXHTML && FCKConfig.EnableSourceXHTML ? FCK.GetXHTML( FCKConfig.FormatSource ) : FCK.GetHTML( FCKConfig.FormatSource ) ) ;
	document.getElementById('txtSource').value = FCK.GetXHTML( FCKConfig.FormatSource ) ;

	// Activate the "OK" button.
	window.parent.SetOkButton( true ) ;
}

//#### The OK button was hit.
function Ok()
{
	if ( oEditor.FCKBrowserInfo.IsIE )
		oEditor.FCKUndo.SaveUndoStep() ;
			
	FCK.SetHTML( document.getElementById('txtSource').value, false ) ;
	
	return true ;
}
		</script>
	</head>
	<body scroll="no" style="OVERFLOW: hidden">
		<table width="100%" height="100%">
			<tr>
				<td height="100%"><textarea id="txtSource" dir="ltr" style="PADDING-RIGHT: 5px; PADDING-LEFT: 5px; FONT-SIZE: 14px; PADDING-BOTTOM: 5px; WIDTH: 100%; PADDING-TOP: 5px; FONT-FAMILY: Monospace; HEIGHT: 100%">Loading. Please wait...</textarea></td>
			</tr>
		</table>
	</body>
</html>
