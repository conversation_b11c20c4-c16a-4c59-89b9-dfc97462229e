<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2006 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_textarea.html
 * 	Text Area dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<html>
	<head>
		<title>Text Area Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = oEditor.FCKSelection.GetSelectedElement() ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl && oActiveEl.tagName == 'TEXTAREA' )
	{
		GetE('txtName').value		= oActiveEl.name ;
		GetE('txtCols').value		= GetAttribute( oActiveEl, 'cols' ) ;
		GetE('txtRows').value		= GetAttribute( oActiveEl, 'rows' ) ;
	}
	else
		oActiveEl = null ;

	window.parent.SetOkButton( true ) ;
}

function Ok()
{
	if ( !oActiveEl )
	{
		oActiveEl = oEditor.FCK.EditorDocument.createElement( 'TEXTAREA' ) ;
		oActiveEl = oEditor.FCK.InsertElementAndGetIt( oActiveEl ) ;
	}

	oActiveEl.name = GetE('txtName').value ;
	SetAttribute( oActiveEl, 'cols', GetE('txtCols').value ) ;
	SetAttribute( oActiveEl, 'rows', GetE('txtRows').value ) ;

	return true ;
}

		</script>
	</head>
	<body style='OVERFLOW: hidden' scroll='no'>
		<table height="100%" width="100%">
			<tr>
				<td align="center">
					<table border="0" cellpadding="0" cellspacing="0" width="80%">
						<tr>
							<td>
								<span fckLang="DlgTextareaName">Name</span><br>
								<input type="text" id="txtName" style="WIDTH: 100%">
								<span fckLang="DlgTextareaCols">Collumns</span><br>
								<input id="txtCols" type="text" size="5">
								<br>
								<span fckLang="DlgTextareaRows">Rows</span><br>
								<input id="txtRows" type="text" size="5">
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>
