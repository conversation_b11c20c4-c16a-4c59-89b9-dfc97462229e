<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    admin.asp
'// 开始时间:    2004.07.30
'// 最后修改:    
'// 备    注:    管理页
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="../c_option.asp" -->
<!-- #include file="../function/c_function.asp" -->
<!-- #include file="../function/c_system_lib.asp" -->
<!-- #include file="../function/c_system_base.asp" -->
<!-- #include file="../function/c_system_manage.asp" -->
<%

Call System_Initialize()

Call CheckReference("")

Dim strAct
strAct=Request.QueryString("act")

'检查权限
If Not CheckRights(strAct) Then Call ShowError(6)

BlogTitle=ZC_BLOG_TITLE & ZC_MSG044 & ZC_MSG046

%><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<%=ZC_BLOG_LANGUAGE%>" lang="<%=ZC_BLOG_LANGUAGE%>">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="Content-Language" content="<%=ZC_BLOG_LANGUAGE%>" />
	<link rel="stylesheet" rev="stylesheet" href="../CSS/admin.css" type="text/css" media="screen" />
	<script language="JavaScript" src="../script/common.js" type="text/javascript"></script>
	<title><%=BlogTitle%></title>
</head>
<body>
			<div id="divMain">
<%

Select Case Request.QueryString("act")

	Case "ArticleMng" Call ExportArticleList(Request.QueryString("page"),Request("cate"),Request("level"),Escape(Request("title")))
	Case "CategoryMng" Call ExportCategoryList(Request.QueryString("page"))
	Case "CommentMng" Call ExportCommentList(Request.QueryString("page"),Request("intContent"))
	Case "TrackBackMng" Call ExportTrackBackList(Request.QueryString("page"))
	Case "UserMng" Call ExportUserList(Request.QueryString("page"))
	Case "FileMng" Call ExportFileList(Request.QueryString("page"))
	Case "BlogMng" Call ExportManageList()
	Case "KeyWordMng" Call ExportKeyWordList(Request.QueryString("page"))
	Case "TagMng" Call ExportTagList(Request.QueryString("page"))
	Case "PlugInMng" Call ExportPluginList()
	Case "SiteInfo" Call ExportSiteInfo()
	Case "SiteFileMng" Call ExportSiteFileList(Request.QueryString("path"),Request.QueryString("opath"))
	Case "SiteFileEdt" Call ExportSiteFileEdit(Request.QueryString("path"))
	Case "AskFileReBuild" Call ExportFileReBuildAsk()
End Select

%>
			</div>
<script>

	//斑马线
	var tables=document.getElementsByTagName("table");
	var b=false;
	for (var j = 0; j < tables.length; j++){

		var cells = tables[j].getElementsByTagName("tr");

		cells[0].style.background="#DAE2E8";
		for (var i = 1; i < cells.length; i++){
			if(b){
				cells[i].style.background="#F1F4F7";
				b=false;
			}
			else{
				cells[i].style.background="#FFFFFF";
				b=true;
			};
		};
	}

document.close();

</script>
</body>
</html>
<%
Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>