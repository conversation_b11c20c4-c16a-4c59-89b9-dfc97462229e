﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<#ZC_BLOG_LANGUAGE#>" lang="<#ZC_BLOG_LANGUAGE#>">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="Content-Language" content="<#ZC_BLOG_LANGUAGE#>" />
	<link rel="stylesheet" rev="stylesheet" href="<#ZC_BLOG_HOST#>style/<#ZC_BLOG_CSS#>.css" type="text/css" media="screen" />
	<link rel="stylesheet" rev="stylesheet" href="<#ZC_BLOG_HOST#>css/print.css" type="text/css" media="print" />
	<link rel="alternate" type="application/rss+xml" href="<#ZC_BLOG_HOST#>rss.xml" title="<#ZC_BLOG_TITLE#>" />
	<link rel="alternate" type="application/atom+xml" href="<#ZC_BLOG_HOST#>atom.xml" title="<#ZC_BLOG_TITLE#>" />
	<script language="JavaScript" src="<#ZC_BLOG_HOST#>script/common.js" type="text/javascript"></script>
	<title><#ZC_BLOG_TITLE#><#ZC_MSG044#><#ZC_BLOG_SUBTITLE#></title>
</head>
<body class="multi default">
<script language="JavaScript" type="text/javascript">
	var str00="<#ZC_BLOG_HOST#>";
	var str01="<#ZC_MSG033#>";
	var str02="<#ZC_MSG034#>";
	var str03="<#ZC_MSG035#>";
	var str06="<#ZC_MSG057#>";
	var intMaxLen="<#ZC_CONTENT_MAX#>";
	var strBatchView="";
	var strBatchInculde="";
	var strBatchCount="";
</script>
<div id="divAll">
	<div id="divPage">
	<div id="divMiddle">
		<div id="divTop">
			<h1 id="BlogTitle"><#ZC_BLOG_NAME#></h1>
			<h2 id="BlogSubTitle"><#ZC_BLOG_SUB_NAME#></h2>
		</div>
		<div id="divNavBar">
<h3><#ZC_MSG052#></h3>
<ul>
<#CACHE_INCLUDE_NAVBAR#>
</ul>
		</div>
		<div id="divMain">
<#template:article-multi#>
<div class="post pagebar"><#ZC_MSG042#>:<#template:pagebar#></div>
		</div>
		<div id="divSidebar">

<div class="function" id="divCalendar">
<h3><#ZC_MSG050#></h3>
<div><#CACHE_INCLUDE_CALENDAR#></div>
</div>

<div class="function" id="divContorPanel">
<h3><#ZC_MSG025#></h3>
<ul>
<li><a href="cmd.asp?act=login">[<#ZC_MSG009#>]</a>&nbsp;&nbsp;<a href="cmd.asp?act=vrs">[<#ZC_MSG021#>]</a></li>
</ul>
</div>

<div class="function" id="divCatalog">
<h3><#ZC_MSG026#></h3>
<ul>
<#CACHE_INCLUDE_CATALOG#>
</ul>
</div>

<div class="function" id="divComments">
<h3><#ZC_MSG027#></h3>
<ul>
<#CACHE_INCLUDE_COMMENTS#>
</ul>
</div>

<div class="function" id="divGuestComments">
<h3><#ZC_MSG274#></h3>
<ul>
<#CACHE_INCLUDE_GUESTCOMMENTS#>
</ul>
</div>

<div class="function" id="divTrackbacks">
<h3><#ZC_MSG154#></h3>
<ul>
<#CACHE_INCLUDE_TRACKBACKS#>
</ul>
</div>

<div class="function" id="divArchives">
<h3><#ZC_MSG028#></h3>
<ul>
<#CACHE_INCLUDE_ARCHIVES#>
</ul>
</div>

<div class="function" id="divSearchPanel">
<h3><#ZC_MSG085#></h3>
<ul>
<li>
<form method="post" action="<#ZC_BLOG_HOST#>cmd.asp?act=Search">
<input type="text" name="edtSearch" id="edtSearch" size="12" />
<input type="submit" value="<#ZC_MSG087#>" name="btnPost" id="btnPost" />
</form>
</li>
</ul>
</div>

<div class="function" id="divStatistics">
<h3><#ZC_MSG029#></h3>
<ul>
<#CACHE_INCLUDE_STATISTICS#>
</ul>
</div>


<div class="function" id="divFavorites">
<h3><#ZC_MSG030#></h3>
<ul>
<#CACHE_INCLUDE_FAVORITE#>
</ul>
</div>


<div class="function" id="divLinkage">
<h3><#ZC_MSG031#></h3>
<ul>
<#CACHE_INCLUDE_LINK#>
</ul>
</div>

<div class="function" id="divMisc">
<h3><#ZC_MSG039#></h3>
<ul>
<#CACHE_INCLUDE_MISC#>
</ul>
</div>

		</div>
		<div id="divBottom">
			<h3 id="BlogPowerBy"><#ZC_BLOG_VERSION#></h3>
			<h2 id="BlogCopyRight"><#ZC_BLOG_COPYRIGHT#></h2>
		</div>
	</div>
	</div>
</div>
<script language="JavaScript" type="text/javascript">
try{
	var elScript = document.createElement("script");
	elScript.setAttribute("language", "JavaScript");
	elScript.setAttribute("src", "<#ZC_BLOG_HOST#>function/c_html_js.asp?act=batch"+unescape("%26")+"view=" + escape(strBatchView)+unescape("%26")+"inculde=" + escape(strBatchInculde)+unescape("%26")+"count=" + escape(strBatchCount));
	document.getElementsByTagName("body")[0].appendChild(elScript);
	}
catch(e){};
</script>
</body>
</html>