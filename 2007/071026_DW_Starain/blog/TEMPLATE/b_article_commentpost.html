﻿<div class="post" id="divCommentPost">
	<p class="posttop"><a name="comment"><#ZC_MSG024#>:</a></p>
	<form id="frmSumbit" target="_self" method="post" action="<#article/commentposturl#>" >
	<input type="hidden" name="inpId" id="inpId" value="<#article/id#>" />
	<input type="hidden" name="inpArticle" id="inpArticle" value="" />
	<input type="hidden" name="inpLocation" id="inpLocation" value="" />
	<p><input type="text" name="inpName" id="inpName" class="text" value="" size="28" tabindex="1" /> <label for="inpName"><#ZC_MSG001#>(*)</label></p>
	<p><input type="text" name="inpEmail" id="inpEmail" class="text" value="" size="28" tabindex="2" /> <label for="inpEmail"><#ZC_MSG053#></label></p>
	<p><input type="text" name="inpHomePage" id="inpHomePage" class="text" value="" size="28" tabindex="3" /> <label for="inpHomePage"><#ZC_MSG054#></label></p>
	<#template:article_commentpost-verify#>
	<p><label for="txaArticle"><#ZC_MSG055#>(*)(<#ZC_MSG056#>:<#ZC_CONTENT_MAX#>)</label></p>
	<p><textarea name="txaArticle" id="txaArticle" onchange="GetActiveText(this.id);" onclick="GetActiveText(this.id);" onfocus="GetActiveText(this.id);" class="text" cols="50" rows="4" tabindex="5" ></textarea></p>
	<p><input name="btnSumbit" type="submit" tabindex="6" value="<#ZC_MSG087#>" onclick="JavaScript:return VerifyMessage()" class="button" /> <input type="checkbox" name="chkRemember" value="1" id="chkRemember" /> <label for="chkRemember"><#ZC_MSG049#></label></p>
	<script language="JavaScript" type="text/javascript">objActive="txaArticle";ExportUbbFrame();</script>
	</form>
	<p class="postbottom"><#ZC_MSG040#></p>
	<script language="JavaScript" type="text/javascript">LoadRememberInfo();</script>
</div>