<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    
'// 开始时间:    
'// 最后修改:    
'// 备    注:    
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="c_option.asp" -->
<!-- #include file="function/c_function.asp" -->
<!-- #include file="function/c_system_lib.asp" -->
<!-- #include file="function/c_system_base.asp" -->
<!-- #include file="function/c_system_event.asp" -->
<%
Call System_Initialize()

LoadGlobeCache

Dim ArtList
Set ArtList=New TArticleList

ArtList.LoadCache

ArtList.template="TAGS"

ArtList.Title="TagCloud"

ArtList.Build

Dim Tag
Dim strTagCloud
For Each Tag in Tags

	If IsObject(Tag) Then

			strTagCloud=strTagCloud & "<span style='font-family:verdana,sans-serif;line-height:150%;font-size:"& 12 + (Tag.Count/2) &"px;margin:10px;'><a title='" & Tag.Count & "' alt='"&  Tag.Count  &"' href='" & Tag.Url &"'>" & Tag.name & "</a></span>"

	End If
Next

ArtList.SetVar "CUSTOM_TAGS",strTagCloud

Response.Write ArtList.html


%><!-- <%=RunTime()%> --><%
Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>