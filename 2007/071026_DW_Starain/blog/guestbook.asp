<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd) & Sipo
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    guestbook.asp
'// 开始时间:    2007-01-03
'// 最后修改:    
'// 备    注:    GuestBook
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="c_option.asp" -->
<!-- #include file="function/c_function.asp" -->
<!-- #include file="function/c_function_md5.asp" -->
<!-- #include file="function/c_system_lib.asp" -->
<!-- #include file="function/c_system_base.asp" -->
<!-- #include file="function/c_system_event.asp" -->
<%
Call System_Initialize()

Dim GuestBook
Set GuestBook=New TGuestBook

GuestBook.template="SINGLE"

If GuestBook.Export(Request.QueryString("page")) Then

	Response.Write GuestBook.html

End If

%><!-- <%=RunTime()%> --><%
Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>