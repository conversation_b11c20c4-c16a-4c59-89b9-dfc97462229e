﻿/*--------------------------全--局-------------------------------*/
body{
	margin:0;
	padding:0;
	color:#000000;
	font-size:12px;
	background:#FFFFFF;
}
a{
	text-decoration: none;
}
a:link {
	color: #095C83;
}
a:visited {
	color: #095C83;
}
a:hover {
	color: #DC143C;
	text-decoration: underline;
}
a:active {
	color: #DC143C;
	text-decoration: underline;
}
ul{
	list-style-type:none;
	list-style-position : outside;
	margin:0 0 0 0;
	padding:0 0 0 0;
	word-break:break-all;
}
li{
	margin:0 0 0 0;
	padding:0 0 0 0;
}
p{
	margin:0 0 0 0;
	padding:0 0 0 0;
	word-break:break-all;
}
img{
	border:0;
}

/*---------------------------------------------------------------*/
#divAll{
	width:790px;
	margin:0 auto;
	padding:0
}

#divPage{
	width:778px;
	margin:0;
	padding:0 6px 0 6px;
	text-align:left;
	float:left;
	background:url("default/bg.gif") repeat-y center;
}

#divMiddle{
	width:778px;
	margin:0;
	padding:0;
	text-align:left;
	float:left;
}

#divTop{
	width:778px;
	margin:0 auto;
	padding:0;
	text-align:left;
	float:left;
	height:70px;
	background:#1D99D3 url("default/topbacking.gif") repeat-x 0 0;
	border-bottom:1px solid #808080;
}

#divSidebar{
	width:210px;
	margin:0 0 0 0;
	padding:0 0 10px 0;
	text-align:center;
	float:left;
}

#divMain{
	width:568px;
	margin:0 0 0 0;
	padding: 0;
	text-align:left;
	float:left;
}

#divBottom{
	width:778px;
	margin:0 auto;
	padding:0;
	text-align:center;
	float:left;
	height:80px;
	background:url("default/bottom.png") repeat-x 0 0;
	border-top:1px solid #D0D0D0;
}

/*---------------------------------------------------------------*/
div.function {
	float:left;
	width:190px;
	margin:10px 0 0 0;
	padding:0 0 0 10px;
	text-align:left;
}
div.function h3{
	font-size:12px;
	height:15px;
	margin:0 0 0 0;
	padding:5px 0 0 5px;
	color:black;
}
div.function ul{
	text-align:left;
	width:190px;
	margin:2px 0 0 0;
	padding:5px 0 0 0;
	list-style-position:outside;
	list-style-type:none;
	border-top:1px solid #d0d0d0;
}
div.function li{
	margin:0 0 0 0;
	padding:2px 0 1px 15px;
	background:url("default/pointblue.gif") no-repeat 3px 4px;
}

#divCatalog li{
	background:none;
	padding:2px 0 1px 4px;
}
/*#divCatalog li span.feed-icon{
	display:none;
}*/

#divMisc{
	padding:10px 0 5px 10px;
}
#divMisc h3{
	display: none;
}
#divMisc ul{
	border:none;
}
#divMisc li{
	padding:2px 0 2px 15px;
	background-image : none;
}

div.function li span.feed-icon a{
	background:url("default/feed.png") no-repeat 0 3px;
	margin:0 0 0 0;
	padding:0 0 0 0;
	font-size:14px;
	height:12px;
	width:12px;
}
div.function li span.feed-icon img{
	height:12px;
	width:9px;
	visibility:hidden;
	margin:0;
	padding:0;
}

#divContorPanel li{
	background-image : none;
}

#divSearchPanel li{
	background-image : none;
}

/*---------------------------------------------------------------*/


div.post {
	width:548px;
	margin:10px 0 10px 10px;
	padding:0 0 0 0;
	text-align:left;
}
div.post-nav {
	width:100%;
	clear:both;
	float:left;
}
div.post-nav a.l{
	float:left;
	padding-right:10px;
	padding-bottom:5px;
}
div.post-nav a.r{
	float:right;
	padding-left:10px;
	padding-bottom:5px;
}

div.post .post-title{
	width:519px;
	padding:5px 0 2px 28px;
	margin:2px 0 2px 0;
	font-size:18px;
	color: #284259;
	border-top:1px dashed #D0D0D0;
	border-bottom:1px dashed #D0D0D0;
	background:#fafafa url("default/title-bg.gif") no-repeat 3px center;
}

div.post .post-date{
	width:538px;
	margin:0 0 0 0;
	padding:5px 5px 2px 0;
	font-size:12px;
	color:gray;
}

div.post .post-footer{
	width:546px;
	margin:10px 0 30px 0;
	padding:2px 0 0 0;
	font-size:12px;
	color:gray;
	height:20px;
	text-align:right;
}


div.post .post-tags{
	width:541px;
	margin:2px 0 2px 5px;
	padding:2px 0 0 0;
	font-size:12px;
	color:gray;
	height:20px;
	text-align:left;
}

body.multi div.post .post-tags{
	/*display:none;*/
}

div.post div.post-body{
	width:520px;
	margin:0 0 0 0;
	padding:0 0 0 5px;
	font-size:13px;
	word-break:break-all;
	text-align:left;
	line-height:150%;
}

div.post-body div.media a{
	padding-left:16px;
	background:#FBFBFB url("default/media.gif") no-repeat 0 center;
}

div.post-body img{
	padding:4px 4px 4px 4px;
	border:0;
	clear : both;
	float : none;
}

div.post-body p{
	margin:10px 0 15px 0;
	padding:0;
}
div.post-body p.img{
	margin:0;
	padding:0;
	text-align:center;
}
div.post-body p.inscript{
	margin:0;
	padding:0;
	padding-right:20px;
	text-align:right;
}
div.post-body p.code{
	padding:5px;
	border:1px dotted black;
}
div.post-body div.code{
	padding:5px;
	border:1px dotted black;
}

div.post-body h1{
	color: #000000;
	margin:0;
	padding:0;
	font-size:16px;
}
div.post-body h2{
	color: #000000;
	margin:0;
	padding:0;
	font-size:14px;
}
div.post-body h3{
	color: #000000;
	margin:0;
	padding:0;
	font-size:13px;
}
div.post-body h4{
	color: #000000;
	margin:0;
	padding:0;
	font-size:12px;
}
div.post-body h5{
	color: #000000;
	margin:0;
	padding:0;
	font-size:11px;
}
div.post-body h6{
	color: #000000;
	margin:0;
	padding:0;
	font-size:10px;
}

/*---------------------------------------------------------------*/
#BlogTitle{
	font-size:32px;
	margin:10px 0 0 20px;
	padding:0;
	font-weight:bold;
	color:#F0FFF0;
}
#BlogTitle a{
	color:#F0FFF0;
}
#BlogTitle a:hover {
	color: #DC143C;
	text-decoration: none;
}
#BlogTitle a:active {
	color: #DC143C;
	text-decoration: none;
}
#BlogSubTitle{
	color: #F0FFF0;
	font-size:12px;
	margin:2px 0 0 10px;
	padding:0;
	font-weight:normal;
}
#BlogPowerBy{
	font-size:12px;
	height:15px;
	margin:0;
	padding:10px 10px 0 10px;
	text-align:right;
	font-weight:normal;
}
#BlogCopyRight{
	font-size:12px;
	height:20px;
	margin:0;
	padding:0 10px 0 10px;
	text-align:right;
	float:none;
	clear:both;
	font-weight:normal;
}


/*---------------------------------------------------------------*/
p.posttop {
	margin:10px 0 0 0;
	padding:3px 0 2px 12px;
	background: url("default/pointblue.gif") no-repeat 0 center;
}
p.postbottom {
	margin:0 0 10px 0;
	padding:5px 0 0 0;
}

#frmSumbit{
	padding:0;
	margin:0;
}

#frmSumbit p{
	padding:5px 0 5px 0;
}


input.button{
	background: white;
	border: 1px double #284259;
	color: #333;
	padding: 0.05em 0.25em 0.05em 0.25em;
}

input.text {
	padding: 0.15em 0.25em 0.20em 0.25em;
	border: 1px double #284259;
	width: 200px;
	background: white;
}

textarea.text {
	padding: 0.15em 0.25em 0.20em 0.25em;
	border: 1px double #284259;
	width: 80%;
	height: 80px;
	background: white;
	width: 520px;
	height: 120px;
}


/*---------------------------------------------------------------*/
#divCalendar{
	padding:0 0 0 10px;
	text-align:center;
}

#divCalendar h3{
	display:none;
}

#divCalendar div{
	margin:0 0 0 0;
	padding:0 0 5px 4px;
	float:left;
}
#divCalendar div.month1{
	background:url("default/month1.gif") no-repeat center 10px;
}
#divCalendar div.month2{
	background:url("default/month2.gif") no-repeat center 10px;
}
#divCalendar div.month3{
	background:url("default/month3.gif") no-repeat center 10px;
}
#divCalendar div.month4{
	background:url("default/month4.gif") no-repeat center 10px;
}
#divCalendar div.month5{
	background:url("default/month5.gif") no-repeat center 10px;;
}
#divCalendar div.month6{
	background:url("default/month6.gif") no-repeat center 10px;
}
#divCalendar div.month7{
	background:url("default/month7.gif") no-repeat center 10px;
}
#divCalendar div.month8{
	background:url("default/month8.gif") no-repeat center 10px;
}
#divCalendar div.month9{
	background:url("default/month9.gif") no-repeat center 10px;
}
#divCalendar div.month10{
	background:url("default/month10.gif") no-repeat center 10px;
}
#divCalendar div.month11{
	background:url("default/month11.gif") no-repeat center 10px;
}
#divCalendar div.month12{
	background:url("default/month12.gif") no-repeat center 10px;
}

p.w{
	float:left;
	width:25px;
	height:16px;
	margin:4px 0 0 0;
	padding:2px 0 1px 0;
	text-align:center;
	vertical-align :middle;
	font-size:9px;
	text-decoration : underline; 
}

p.y{
	text-align:center;
	height:14px;
	margin:2px 0 0 0;
	padding:0 0 0 0;
	float:left;
	width:175px;
	font-size:12px;
	font-weight:bold;
}
p.d{
	float:left;
	width:25px;
	height:18px;
	margin:0 0 0 0;
	padding:2px 0 0 0;
	text-align:center;
	vertical-align :middle;
}
p.yd{
	float:left;
	width:25px;
	height:18px;
	margin:0 0 0 0;
	padding:2px 0 0 0;
	text-align:center;
	vertical-align :middle;
	font-weight:bolder;
	color:#6090c0;
	background: url("default/links_r.gif") no-repeat 1px 1px;
}
p.nd{
	float:left;
	width:25px;
	height:18px;
	margin:0 0 0 0;
	padding:2px 0 0 0;
	text-align:center;
	vertical-align :middle;
}
p.cd{
	float:left;
	width:25px;
	height:18px;
	margin:0 0 0 0;
	padding:2px 0 0 0;
	text-align:center;
	vertical-align :middle;
	font-weight:bold;
}


/*---------------------------------------------------------------*/
ul.msg{
	width:548px;
	margin:10px 0 40px 10px;
	padding:0 0 0 0;
	text-align:left;
	list-style-position : outside;
}
li.msgname{
	padding:2px 0 2px 5px;
	margin:0px;
	border-bottom:1px dashed #D0D0D0;
}
li.tbname{
	padding:0 0 0 5px;
	margin:0px;
	border-bottom:1px dashed #D0D0D0;
}
li.msgurl{
	text-align:right;
	padding:2px 10px 2px 10px;
	margin:0;
	border-left:1px dashed #D0D0D0;
	border-right:1px dashed #D0D0D0;
}
li.msgarticle{
	list-style-position : outside;
	padding:15px 10px 15px 10px;
	margin:0;
	line-height:150%;
	border-left:1px dashed #D0D0D0;
	border-right:1px dashed #D0D0D0;
}
li.msgtime{
	text-align:right;
	padding:2px 5px 0 10px;
	margin:0 0 0 0;
	border-bottom:1px dashed #D0D0D0;
	border-left:1px dashed #D0D0D0;
	border-right:1px dashed #D0D0D0;
}
ul.trackback li.tbname{
	border-bottom:none;
	background: url("default/pointblue.gif") no-repeat 0 center;
	padding:3px 0 2px 12px;
	margin:0px;
}
ul.trackback{
	margin:5px 0 10px 10px;
}
ul.mutuality{
	margin:5px 0 10px 10px;
	border-bottom:1px dashed #D0D0D0;
}
ul.mutuality li.tbname{
	background: url("default/pointblue.gif") no-repeat 0 center;
	padding:3px 0 2px 12px;
	margin:0px;
	border:none;
}
ul.mutuality li.msgarticle{
	background:#ffffff;
	padding:0 10px 10px 16px;
	border:none;
}


li.msgarticle div.quote{
	padding:10px;
	background:#f0f0f0;
}
li.msgarticle div.quote-title{
	margin:2px;
	padding:3px;
	font-size:120%;
	border-bottom:1px #f8f8f8 solid;
}

/*---------------------------------------------------------------*/
#divNavBar{
	float:left;
	width:778px;
	border-bottom:1px solid #C0C0C0;
	background:url("default/navbg.gif") repeat-x 0 0;
	height:24px;
}

#divNavBar h3{
	display:none;
}

#divNavBar ul{
	margin:0;
	padding:0;
}

#divNavBar li{
	float:left;
	padding:0 0 0 0;
	margin:0 0 0 0;
	font-family:"宋体","黑体";
	background:url("default/navbg2.gif") no-repeat right 0;
}

#divNavBar a{
	float:left;
	margin:0;
	padding:6px 10px 4px 10px;
	height:14px;
	text-decoration: none;
}

#divNavBar a:link {
	color: black;
}
#divNavBar a:visited {
	color: black;
}
#divNavBar a:hover {
	color: white;
	background:#DC143C;
}
#divNavBar a:active {
	color: white;
	background:#DC143C;
}