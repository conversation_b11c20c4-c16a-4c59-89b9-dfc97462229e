<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    c_system_event.asp
'// 开始时间:    2005.02.11
'// 最后修改:    
'// 备    注:    
'///////////////////////////////////////////////////////////////////////////////



'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    评论发表 公共调用部份
'*********************************************************
Function PostComment_Core(objComment)

	Set PostComment_Core=objComment

End Function
'*********************************************************




'*********************************************************
' 目的：    引用发表 公共调用部份
'*********************************************************
Function PostTrackBack_Core(objTrackBack)

	Set PostTrackBack_Core=objTrackBack

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    用户登陆
'*********************************************************
Public Function Login()

	If CheckVerifyNumber(Request.Form("edtCheckOut"))=False Then Call ShowError(38)

	If BlogUser.Verify=False Then
		Call ShowError(8)
	Else
		Response.Redirect "cmd.asp?act=admin"
	End If
End Function
'*********************************************************




'*********************************************************
' 目的：    用户退出
'*********************************************************
Public Function Logout()
	Response.Cookies("username")=""
	Response.Cookies("password")=""
	Response.Write "<script language=""JavaScript"" src=""script/common.js"" type=""text/javascript""></script>"
	Response.Write "<script language=""JavaScript"" type=""text/javascript"">"
	Response.Write "SetCookie(""username"","""","""");"
	Response.Write "SetCookie(""password"","""","""");"
	Response.Write "window.location=""" & ZC_BLOG_HOST & """;"
	Response.Write "</script>"
End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    文件上抟
'*********************************************************
Function UploadFile(bolAutoName,bolReload)

	Dim objUpLoadFile
	Set objUpLoadFile=New TUpLoadFile

	objUpLoadFile.AuthorID=BlogUser.ID

	If objUpLoadFile.UpLoad(bolAutoName) Then

		UploadFile=True

		If bolReload=False Then Exit Function

		Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8""/><meta http-equiv=""Content-Language"" content=""zh-cn"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /></head><body>"

		Response.Write "<form border=""1"" name=""edit"" id=""edit"" method=""post"" enctype=""multipart/form-data"" action="""& ZC_BLOG_HOST &"cmd.asp?act=FileSnd"">"
		Response.Write "<p>"& ZC_MSG236 &":</p>"
		Response.Write "<p>"& objUpLoadFile.FileName &"</p>"
		Response.Write "<p><input type=""submit"" class=""button"" value="""& ZC_MSG237 &""" name=""B1"" /></p></form>"


		Dim strFileType
		Dim strFileName

		strFileType=LCase(objUpLoadFile.FileName)

		If (CheckRegExp(strFileType,"\.(jpeg|jpg|gif|png|bmp)$")=True) Then
			strFileName="[IMG]upload/"&objUpLoadFile.FileName&"[/IMG]"
		ElseIf (CheckRegExp(strFileType,"\.(swf)$")=True) Then 
			strFileName="[FLASH=400,300,True]upload/"&objUpLoadFile.FileName&"[/FLASH]"
		ElseIf (CheckRegExp(strFileType,"\.(wmv|avi|asf)$")=True) Then 
			strFileName="[WMV=400,300,True]upload/"&objUpLoadFile.FileName&"[/WMV]"
		ElseIf (CheckRegExp(strFileType,"\.(qt|mov)$")=True) Then 
			strFileName="[QT=400,300,True]upload/"&objUpLoadFile.FileName&"[/QT]"
		ElseIf (CheckRegExp(strFileType,"\.(rm|rmvb|mpg|mpeg)$")=True) Then 
			strFileName="[RM=400,300,True]upload/"&objUpLoadFile.FileName&"[/RM]"
		ElseIf (CheckRegExp(strFileType,"\.(wma)$")=True) Then 
			strFileName="[WMA=True]upload/"&objUpLoadFile.FileName&"[/WMA]"
		ElseIf (CheckRegExp(strFileType,"\.(rm)$")=True) Then 
			strFileName="[RA=True]upload/"&objUpLoadFile.FileName&"[/RA]"
		Else
			strFileName="[URL=upload/"& objUpLoadFile.FileName &"]"& objUpLoadFile.FileName &"[/URL]"
		End If

		'edit
		Response.Write "<script language=""Javascript"">try{parent.document.edit.txaContent.currPos.text+='"&strFileName&"';}catch(e){try{parent.document.edit.txaContent.value+='"&strFileName&"'}catch(e){}}</script>"
		'edit_widgeditor
		Response.Write "<script language=""Javascript"">try{parent.document.getElementById('txaContentWidgIframe').contentWindow.document.getElementsByTagName('body')[0].innerHTML+='"&strFileName&"'}catch(e){}</script>"
		'edit_fckeditor
		Response.Write "<script language=""Javascript"">try{parent.document.getElementById('MyEditor___Frame').contentWindow.frames[0].document.getElementsByTagName('body')[0].innerHTML+='"&strFileName&"'}catch(e){}</script>"
		'edit_htmlarea
		Response.Write "<script language=""Javascript"">try{parent.document.getElementById('ta').parentNode.getElementsByTagName('iframe')[0].contentWindow.document.getElementsByTagName('body')[0].innerHTML+='"&strFileName&"'}catch(e){}</script>"
		'edit_tinymce
		Response.Write "<script language=""Javascript"">try{parent.document.getElementById('mce_editor_0').contentWindow.document.getElementsByTagName('body')[0].innerHTML+='"&strFileName&"'}catch(e){}</script>"
		'edit_ewebeditor
		Response.Write "<script language=""Javascript"">try{parent.document.getElementById('eWebEditor1').contentWindow.document.getElementsByTagName('body')[0].innerHTML+='"&strFileName&"'}catch(e){}</script>"

		Response.Write "</body></html>"

		If bolReload=True Then Response.End

	Else

		If bolReload=True Then Response.Redirect "admin/admin.asp?act=FileSnd"

	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    Form of Send File
'*********************************************************
Function SendFile()

	Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8""/><meta http-equiv=""Content-Language"" content=""zh-cn"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /></head><body>"

	Response.Write "<form border=""1"" name=""edit"" id=""edit"" method=""post"" enctype=""multipart/form-data"" action="""& ZC_BLOG_HOST &"cmd.asp?act=FileUpload&reload=1"">"
	Response.Write "<p>"& ZC_MSG108 &": </p>"
	Response.Write "<p><input type=""checkbox"" onclick='if(this.checked==true){document.getElementById(""edit"").action=document.getElementById(""edit"").action+""&autoname=1"";}else{document.getElementById(""edit"").action="""& ZC_BLOG_HOST &"cmd.asp?act=FileUpload&reload=1"";}' id=""chkAutoName"" id=""chkAutoName""/><label for=""chkAutoName"">"& ZC_MSG131 &"</label></p>"
	Response.Write "<p><input type=""file"" id=""edtFileLoad"" name=""edtFileLoad"" size=""20"">  <input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" name=""B1"" onclick='document.getElementById(""edit"").action=document.getElementById(""edit"").action+""&filename=""+escape(edtFileLoad.value)' /> <input class=""button"" type=""reset"" value="""& ZC_MSG088 &""" name=""B2"" /></p></form>"

	Response.Write "</body></html>"

End Function
'*********************************************************






'*********************************************************
' 目的：     文件删除
'*********************************************************
Function DelFile(intID)

	Dim objUpLoadFile
	Set objUpLoadFile=New TUpLoadFile

	If objUpLoadFile.LoadInfoByID(intID) Then

		If (objUpLoadFile.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True) Then
			If objUpLoadFile.Del Then DelFile=True
		End If

	Else
		Exit Function
	End If

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Post Article
'*********************************************************
Function PostArticle()

	Dim s

	If Request.Form("edtID")<>"0" Then
		Dim objTestArticle
		Set objTestArticle=New TArticle
		If objTestArticle.LoadInfobyID(Request.Form("edtID")) Then
			If Not((objTestArticle.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
			objTestArticle.DelFile
		Else
			Call ShowError(9)
		End If
	End If

	Dim objArticle
	Set objArticle=New TArticle
	objArticle.ID=Request.Form("edtID")
	objArticle.CateID=Request.Form("edtCateID")
	objArticle.AuthorID=Request.Form("edtAuthorID")
	objArticle.Level=Request.Form("edtLevel")
	objArticle.PostTime=Request.Form("edtYear") & "-" & Request.Form("edtMonth") & "-" & Request.Form("edtDay") & " " &  Request.Form("edtTime")
	objArticle.Title=Request.Form("edtTitle")
	objArticle.Tag=ParseTag(Request.Form("edtTag"))
	objArticle.Alias=Request.Form("edtAlias")
	objArticle.Istop=Request.Form("edtIstop")

	objArticle.Intro=Request.Form("txaIntro")

	Select Case LCase(Request.QueryString("type"))
	Case ""
		objArticle.Content=Request.Form("txaContent")

		'If objArticle.Intro="" Then
		'	s=objArticle.Content
		'	s=TransferHTML(s,"[nohtml]")
		'	s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
		'	objArticle.Intro=s
		'End If
	Case "htmlarea"
		objArticle.Content=Request.Form("ta")

		If objArticle.Intro="" Then
			s=objArticle.Content
			s=TransferHTML(s,"[nohtml]")
			s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
			objArticle.Intro=s
		End If
	Case "tinymce"
		objArticle.Content=Request.Form("txaContent")
		objArticle.Content=Replace(objArticle.Content,vbCrLf,"")
		objArticle.Content=Replace(objArticle.Content,vbLf,"")
		If objArticle.Intro="" Then
			s=objArticle.Content
			s=TransferHTML(s,"[nohtml]")
			s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
			objArticle.Intro=s
		End If
	Case "fckeditor"
		objArticle.Content=Request.Form("txaContent")
		objArticle.Content=Replace(objArticle.Content,vbCrLf,"")
		objArticle.Content=Replace(objArticle.Content,vbLf,"")
		If objArticle.Intro="" Then
			s=objArticle.Content
			s=TransferHTML(s,"[nohtml]")
			s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
			objArticle.Intro=s
		End If
	Case "ewebeditor"
		objArticle.Content=Request.Form("txaContent")
		objArticle.Content=Replace(objArticle.Content,vbCrLf,"")
		objArticle.Content=Replace(objArticle.Content,vbLf,"")
		If objArticle.Intro="" Then
			s=objArticle.Content
			s=TransferHTML(s,"[nohtml]")
			s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
			objArticle.Intro=s
		End If
	Case "widgeditor"
		objArticle.Content=Request.Form("txaContent")
		objArticle.Content=Replace(objArticle.Content,vbCrLf,"")
		objArticle.Content=Replace(objArticle.Content,vbLf,"")
		If objArticle.Intro="" Then
			s=objArticle.Content
			s=TransferHTML(s,"[nohtml]")
			s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
			objArticle.Intro=s
		End If
	Case Else
		objArticle.Content=Request.Form("txaContent")

		If objArticle.Intro="" Then
			s=objArticle.Content
			s=TransferHTML(s,"[nohtml]")
			s=Left(s,ZC_TB_EXCERPT_MAX) & "..."
			objArticle.Intro=s
		End If
	End Select

	If objArticle.Post Then
		Call BuildArticle(objArticle.ID,True,True)
		PostArticle=True
	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    Del Article
'*********************************************************
Function DelArticle(intID)

	If intID<>"" Then
		Dim objTestArticle
		Set objTestArticle=New TArticle
		If objTestArticle.LoadInfobyID(intID) Then
			If Not((objTestArticle.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
		Else
			Call ShowError(9)
		End If
		Set objTestArticle=Nothing
	End If

	Dim objArticle
	Set objArticle=New TArticle

	If objArticle.LoadInfoByID(intID) Then

		If objArticle.Del Then DelArticle=True

		If ZC_MOONSOFT_PLUGIN_ENABLE=True Then
			Call BuildCategory(Empty,Categorys(objArticle.CateID).ID,Empty,Empty,Empty,ZC_DISPLAY_MODE_ALL,Categorys(objArticle.CateID).Directory,Categorys(objArticle.CateID).FileName)
			Call BuildCategory(Empty,Empty,Empty,Year(objArticle.PostTime) & "-" & Month(objArticle.PostTime),Empty,ZC_DISPLAY_MODE_ALL,ZC_STATIC_DIRECTORY,Year(objArticle.PostTime) & "_" & Month(objArticle.PostTime) & "." & ZC_STATIC_TYPE)
		End If

		Call BlogReBuild_Comments

		Dim objNavArticle
		Dim objRS
		Set objRS=objConn.Execute("SELECT TOP 1 [log_ID] FROM [blog_Article] WHERE ([log_Level]>2) AND ([log_PostTime]<#" & objArticle.PostTime & "#) ORDER BY [log_PostTime] DESC")
		If (Not objRS.bof) And (Not objRS.eof) Then
			Call BuildArticle(objRS("log_ID"),False,False)
		End If
		Set objRS=Nothing
		Set objRS=objConn.Execute("SELECT TOP 1 [log_ID] FROM [blog_Article] WHERE ([log_Level]>2) AND ([log_PostTime]>#" & objArticle.PostTime & "#) ORDER BY [log_PostTime] ASC")
		If (Not objRS.bof) And (Not objRS.eof) Then
			Call BuildArticle(objRS("log_ID"),False,False)
		End If
		Set objRS=Nothing

	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    Build Article
'*********************************************************
Function BuildArticle(intID,bolBuildNavigate,bolBuildCategory)

	Dim objArticle
	Set objArticle=New TArticle

	If objArticle.LoadInfoByID(intID) Then
		objArticle.Statistic
		objArticle.template="SINGLE"
		If objArticle.Export(ZC_DISPLAY_MODE_ALL) Then
			objArticle.SaveCache
			objArticle.Build
			objArticle.Save

			If (bolBuildCategory=True) And (ZC_MOONSOFT_PLUGIN_ENABLE=True) Then
				Call BuildCategory(Empty,Categorys(objArticle.CateID).ID,Empty,Empty,Empty,ZC_DISPLAY_MODE_ALL,Categorys(objArticle.CateID).Directory,Categorys(objArticle.CateID).FileName)
				Call BuildCategory(Empty,Empty,Empty,Year(objArticle.PostTime) & "-" & Month(objArticle.PostTime),Empty,ZC_DISPLAY_MODE_ALL,ZC_STATIC_DIRECTORY,Year(objArticle.PostTime) & "_" & Month(objArticle.PostTime) & "." & ZC_STATIC_TYPE)
			End If

		End If

		If (bolBuildNavigate=True) And (ZC_USE_NAVIGATE_ARTICLE=True) Then

			Dim objRS
			Set objRS=objConn.Execute("SELECT TOP 1 [log_ID] FROM [blog_Article] WHERE ([log_Level]>2) AND ([log_PostTime]<#" & objArticle.PostTime & "#) ORDER BY [log_PostTime] DESC")
			If (Not objRS.bof) And (Not objRS.eof) Then
				Call BuildArticle(objRS("log_ID"),False,False)
			End If
			Set objRS=Nothing
			Set objRS=objConn.Execute("SELECT TOP 1 [log_ID] FROM [blog_Article] WHERE ([log_Level]>2) AND ([log_PostTime]>#" & objArticle.PostTime & "#) ORDER BY [log_PostTime] ASC")
			If (Not objRS.bof) And (Not objRS.eof) Then
				Call BuildArticle(objRS("log_ID"),False,False)
			End If
			Set objRS=Nothing

		End If

		BuildArticle=True

	End If

	Set objArticle=Nothing

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Post Category
'*********************************************************
Function PostCategory()

	If Request.Form("edtID")<>"0" Then
		Dim objTestCategory
		Set objTestCategory=New TCategory
		If objTestCategory.LoadInfobyID(Request.Form("edtID")) Then
			objTestCategory.DelFile
		End If
	End If

	Dim objCategory
	Set objCategory=New TCategory
	objCategory.ID=Request.Form("edtID")
	objCategory.Name=Request.Form("edtName")
	objCategory.Order=Request.Form("edtOrder")
	objCategory.Alias=Request.Form("edtAlias")
	If objCategory.Post Then

		Call BuildCategory(Empty,objCategory.ID,Empty,Empty,Empty,ZC_DISPLAY_MODE_ALL,objCategory.Directory,objCategory.FileName)

		PostCategory=True

	End If
	Set objCategory=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Del Category
'*********************************************************
Function DelCategory(intID)

	Dim objCategory
	Set objCategory=New TCategory

	If objCategory.LoadInfobyID(intID) Then
		If objCategory.Del Then DelCategory=True
	End If

	Set objCategory=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Build Category
'*********************************************************
Function BuildCategory(intPage,intCateId,intAuthorId,dtmYearMonth,strTagsName,intType,strDirectory,strFileName)

	Dim ArtList
	Set ArtList=New TArticleList
	ArtList.LoadCache
	ArtList.template="CATALOG"

	If ArtList.ExportByMixed(intPage,intCateId,intAuthorId,dtmYearMonth,strTagsName,intType) Then
		ArtList.FileName=strFileName
		ArtList.Directory=strDirectory
		ArtList.Build
		ArtList.Save
	End If
	Set ArtList=Nothing

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Post Comment
'*********************************************************
Function PostComment(strKey)

	If IsEmpty(Request.Form("inpAjax"))=False Then
		ShowError_Custom="Call RespondError(id,ZVA_ErrorMsg(id)):Response.End"
	End If

	If ZC_COMMENT_TURNOFF Then
		Call ShowError(40)
	End If

	If ZC_COMMENT_VERIFY_ENABLE Then
		If CheckVerifyNumber(Request.Form("inpVerify"))=False Then Call ShowError(38)
	End If

	Dim objComment
	Dim objArticle

	Set objComment=New TComment
	objComment.log_ID=Request.Form("inpID")
	objComment.AuthorID=BlogUser.ID
	objComment.Author=Request.Form("inpName")
	objComment.Content=Request.Form("inpArticle")
	objComment.Email=Request.Form("inpEmail")
	objComment.HomePage=Request.Form("inpHomePage")

	If objComment.AuthorID>0 Then
		objComment.Author=Users(objComment.AuthorID).Name
	End If

	If objComment.log_ID>0 Then
		Set objArticle=New TArticle
		If objArticle.LoadInfoByID(objComment.log_ID) Then
			If Not (strKey=objArticle.CommentKey) Then Call ShowError(43)
			If objArticle.Level<4 Then Call ShowError(44)
		End If
		Set objArticle=Nothing
	Else
		If Not (strKey=Left(MD5(ZC_BLOG_HOST & ZC_BLOG_CLSID & CStr(0) & CStr(Day(Now))),8)) Then Call ShowError(43)
	End If

	Dim objUser
	For Each objUser in Users
		If IsObject(objUser) Then
			If (UCase(objUser.Name)=UCase(objComment.Author)) And (objUser.ID<>objComment.AuthorID) Then Call ShowError(31)
		End If
	Next

	Set objComment=PostComment_Core(objComment)

	If objComment.Post Then
		If objComment.log_ID>0 Then
			Call BuildArticle(objComment.log_ID,False,True)
			BlogReBuild_Comments
		Else
			BlogReBuild_GuestComments
		End If
		PostComment=True
	End if

	If IsEmpty(Request.Form("inpAjax"))=False Then
		Call ReturnAjaxComment(objComment)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.End
	End If

	Set objComment=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Del Comment
'*********************************************************
Function DelComment(intID,intLog_ID)

	Dim objComment
	Dim objArticle

	Set objComment=New TComment
	Set objArticle=New TArticle

	If objComment.LoadInfobyID(intID) Then

		If objComment.log_ID>0 Then
			Dim objTestArticle
			Set objTestArticle=New TArticle
			If objTestArticle.LoadInfobyID(objComment.log_ID) Then
				If Not((objComment.AuthorID=BlogUser.ID) Or (objTestArticle.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
			Else
				Call ShowError(9)
			End If
			Set objTestArticle=Nothing
		Else
			If Not ((objComment.log_ID=0) And (CheckRights("GuestBookMng")=True)) Then Exit Function
		End If

		If objComment.Del Then
			If objComment.log_ID>0 Then
				Call BuildArticle(objComment.log_ID,False,True)
				BlogReBuild_Comments
			Else
				BlogReBuild_GuestComments
			End If
			DelComment=True
		End If

	End If

	Set objComment=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Revert Comment
'*********************************************************
Function RevertComment(strKey,intRevertCommentID)

	If IsEmpty(Request.Form("inpAjax"))=False Then
		ShowError_Custom="Call RespondError(id,ZVA_ErrorMsg(id)):Response.End"
	End If

	Call CheckParameter(intRevertCommentID,"int",0)

	If ZC_COMMENT_TURNOFF Then
		Call ShowError(40)
	End If

	If ZC_COMMENT_VERIFY_ENABLE Then
		If CheckVerifyNumber(Request.Form("inpVerify"))=False Then Call ShowError(38)
	End If

	Dim objComment
	Dim objArticle

	Set objComment=New TComment
	If objComment.LoadInfoByID(intRevertCommentID)=True Then

		Dim s
		s=""

		If BlogUser.ID=0 Then
			If ZC_GUEST_REVERT_COMMENT_ENABLE=False Then Call ShowError(47)
			s=Request.Form("inpName")
			Dim objUser
			For Each objUser in Users
				If IsObject(objUser) Then
					If (UCase(objUser.Name)=UCase(s)) Then Call ShowError(31)
				End If
			Next
		Else
			s=BlogUser.Name
		End If

		objComment.Content=objComment.Content & "[REVERT="& Replace(Replace(ZC_MSG264,"%s",s,1,1),"%s",Now(),1,1) &"]"&Request.Form("inpArticle")&"[/REVERT]"

	End If

	If objComment.log_ID>0 Then
		Set objArticle=New TArticle
		If objArticle.LoadInfoByID(objComment.log_ID) Then
			If Not (strKey=objArticle.CommentKey) Then Call ShowError(43)
			If objArticle.Level<4 Then Call ShowError(44)
		Else
			Call ShowError(9)
		End If
		Set objArticle=Nothing
		objComment.PostTime=Now()
	Else
		If BlogUser.ID=0 Then Call ShowError(45)
		If Not (strKey=Left(MD5(ZC_BLOG_HOST & ZC_BLOG_CLSID & CStr(0) & CStr(Day(Now))),8)) Then Call ShowError(43)
	End If

	Set objComment=PostComment_Core(objComment)

	If objComment.Post Then
		If objComment.log_ID>0 Then
			Call BuildArticle(objComment.log_ID,False,False)
			BlogReBuild_Comments
		Else
			BlogReBuild_GuestComments
		End If

		RevertComment=True
	End if

	If IsEmpty(Request.Form("inpAjax"))=False Then
		Call ReturnAjaxComment(objComment)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.End
	End If

	Set objComment=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Save Comment
'*********************************************************
Function SaveComment(intID,intLog_ID)

	Dim objComment
	Dim objArticle

	Set objComment=New TComment
	If objComment.LoadInfoByID(intID)=True Then

		objComment.log_ID=intLog_ID
		objComment.Author=Request.Form("inpName")
		objComment.Email=Request.Form("inpEmail")
		objComment.HomePage=Request.Form("inpHomePage")
		objComment.Content=Request.Form("txaArticle") & vbCrlf  & Replace(Replace(ZC_MSG273,"%s",BlogUser.Name,1,1),"%s",Now(),1,1)

	End If

	If objComment.log_ID>0 Then
		Set objArticle=New TArticle
		If objArticle.LoadInfoByID(objComment.log_ID) Then
			If Not ((objArticle.AuthorID=BlogUser.ID) Or (objComment.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
		End If
		Set objArticle=Nothing
	Else
		If Not ((objComment.log_ID=0) And (CheckRights("GuestBookMng")=True)) Then Exit Function
	End If

	'Set objComment=PostComment_Core(objComment)

	If objComment.Post Then
		If objComment.log_ID>0 Then
			Call BuildArticle(objComment.log_ID,False,False)
			BlogReBuild_Comments
		Else
			BlogReBuild_GuestComments
		End If

		SaveComment=True
	End if

	Set objComment=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Return Ajax Comment
'*********************************************************
Function ReturnAjaxComment(objComment)

	Dim i,j
	i=0
	Dim objArticle

	If objComment.log_ID>0 Then
		Set objArticle=New TArticle
		If objArticle.LoadInfoByID(objComment.log_ID) Then
			i=objArticle.CommNums
		End If
	Else
		Dim objRS
		Set objRS=Server.CreateObject("ADODB.Recordset")
		objRS.CursorType = adOpenKeyset
		objRS.LockType = adLockReadOnly
		objRS.ActiveConnection=objConn
		objRS.Source=""
		objRS.Open("SELECT COUNT([comm_ID])AS allComment FROM [blog_Comment] WHERE [blog_Comment].[log_ID]=0")
		If (Not objRS.bof) And (Not objRS.eof) Then
			i=objRS("allComment")
		End If
		objRS.Close
		Set objRS=Nothing
	End If

	Dim strC
	Application.Lock
	strC=Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_COMMENT")
	Application.UnLock

	objComment.Count=i
	strC=objComment.MakeTemplate(strC)

	strC=Replace(strC,"<#ZC_BLOG_HOST#>",ZC_BLOG_HOST)

	Dim aryTemplateTagsName2
	Dim aryTemplateTagsValue2

	Application.Lock
	aryTemplateTagsName2=Application(ZC_BLOG_CLSID & "TemplateTagsName")
	aryTemplateTagsValue2=Application(ZC_BLOG_CLSID & "TemplateTagsValue")
	Application.UnLock

	j=UBound(aryTemplateTagsName2)

	For i=1 to j
		strC=Replace(strC,"<#" & aryTemplateTagsName2(i) & "#>",aryTemplateTagsValue2(i))
	Next

	strC= Replace(strC,vbCrLf,"")
	strC= Replace(strC,vbLf,"")
	strC= Replace(strC,vbTab,"")

	Response.Write strC

	ReturnAjaxComment=True

End Function
'*********************************************************


'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Post TrackBack
'*********************************************************
Function PostTrackBack(intID,strKey)

	Dim objTrackBack
	Dim objArticle

	Dim keys
	Dim i,j,k,b

	If ZC_TRACKBACK_TURNOFF Then
		Call RespondError(41,ZVA_ErrorMsg(41))
	End If

	If Len(strKey)=5 Then

		If CheckVerifyNumber(strKey)=False Then Call ShowError(43)

	ElseIf Len(strKey)=8 Then

		Set objArticle=New TArticle
		If objArticle.LoadInfoByID(intID) Then
			If Not (strKey=objArticle.TrackBackKey) Then Call RespondError(43)
			If objArticle.Level<4 Then Call RespondError(44)
		End If
		Set objArticle=Nothing

	Else
		Exit Function
	End If

	Set objTrackBack=New TTrackBack
	Set objArticle=New TArticle

	objTrackBack.log_ID=intID
	objTrackBack.URL=Request.Form("url")
	objTrackBack.Title=Request.Form("title")
	objTrackBack.Blog=Request.Form("blog_name")
	objTrackBack.Excerpt=Request.Form("excerpt")

	Set objTrackBack=PostTrackBack_Core(objTrackBack)

	If objTrackBack.Post Then
		Call BuildArticle(objTrackBack.log_ID,False,True)
		BlogReBuild_TrackBacks
		PostTrackBack=True
	End If

	Set objTrackBack=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Del TrackBack
'*********************************************************
Function DelTrackBack(intID,intLog_ID)

	Dim objTrackBack
	Dim objArticle

	Set objTrackBack=New TTrackBack
	Set objArticle=New TArticle

	If objTrackBack.LoadInfobyID(intID) Then

		Dim objTestArticle
		Set objTestArticle=New TArticle
		If objTestArticle.LoadInfobyID(objTrackBack.log_ID) Then
			If Not((objTestArticle.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
		Else
			Call ShowError(9)
		End If
		Set objTestArticle=Nothing

		If objTrackBack.Del Then
			Call BuildArticle(objTrackBack.log_ID,False,True)
			BlogReBuild_TrackBacks
			DelTrackBack=True
		End If

	End If

	Set objTrackBack=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Send TrackBack
'*********************************************************
Function SendTrackBack()

	Dim objTrackBack
	Dim objArticle

	Set objTrackBack=New TTrackBack
	Set objArticle=New TArticle

	If objArticle.LoadInfobyID(Request.Form("edtID")) Then
		objTrackBack.URL=objArticle.Url
		objTrackBack.Title=objArticle.Title
		objTrackBack.Blog=ZC_BLOG_NAME
		objTrackBack.Excerpt=Left(objArticle.HtmlContent,250)
	Else
		Call ShowError(9)
	End If

	If objTrackBack.Send(Request.Form("edtTrackBack")) Then SendTrackBack=True
	Set objTrackBack=Nothing

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Edit User
'*********************************************************
Function EditUser()

	Dim objUser
	Set objUser=New TUser
	objUser.ID=Request.Form("edtID")
	objUser.Level=Request.Form("edtLevel")
	objUser.Name=Request.Form("edtName")
	objUser.PassWord=Request.Form("edtPassWord")
	objUser.Email=Request.Form("edtEmail")
	objUser.HomePage=Request.Form("edtHomePage")
	objUser.Alias=Request.Form("edtAlias")

	If Not((CInt(objUser.ID)=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function

	If objUser.Edit(BlogUser) Then EditUser=True

	Set objUser=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Del User
'*********************************************************
Function DelUser(intID)

	Dim objRS
	Dim objUser
	Dim objUpLoadFile

	Set objUser=New TUser
	objUser.ID=intID
	If objUser.Del(BlogUser) Then DelUser=True
	Set objUser=Nothing

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Archives()

	Dim i
	Dim j
	Dim l
	Dim n
	Dim objRS
	Dim objStream

	Dim ArtList

	'Archives
	Dim strArchives
	Set objRS=objConn.Execute("SELECT * FROM [blog_Article] WHERE ([log_Level]>1) ORDER BY [log_PostTime] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		Dim dtmYM()
		i=0
		j=0
		ReDim Preserve dtmYM(0)
		Do While Not objRS.eof
			j=UBound(dtmYM)
			i=Year(objRS("log_PostTime")) & "-" & Month(objRS("log_PostTime"))
			If i<>dtmYM(j) Then
				ReDim Preserve dtmYM(j+1)
				dtmYM(j+1)=i
			End If
			objRS.MoveNext
		Loop
	End If
	objRS.Close
	Set objRS=Nothing

	If Not IsEmpty(dtmYM) Then
		For i=1 to UBound(dtmYM)

			l=Year(dtmYM(i))
			n=Month(dtmYM(i))+1
			IF n>12 Then l=l+1:n=1

			Set objRS=objConn.Execute("SELECT COUNT([log_ID]) FROM [blog_Article] WHERE ([log_Level]>1) AND [log_PostTime] BETWEEN #"& Year(dtmYM(i)) &"-"& Month(dtmYM(i)) &"-1# AND #"& l &"-"& n &"-1#")

			If (Not objRS.bof) And (Not objRS.eof) Then

				If ZC_MOONSOFT_PLUGIN_ENABLE=True Then
					strArchives=strArchives & "<li><a href="""& ZC_BLOG_HOST & ZC_STATIC_DIRECTORY & "/" & Year(dtmYM(i)) & "_" & Month(dtmYM(i)) & "." & ZC_STATIC_TYPE & """>" & Year(dtmYM(i)) & " " & ZVA_Month(Month(dtmYM(i))) & " (" & objRS(0) & ")" +"</a></li>"
					Call BuildCategory(Empty,Empty,Empty,Year(dtmYM(i)) & "-" & Month(dtmYM(i)),Empty,ZC_DISPLAY_MODE_ALL,ZC_STATIC_DIRECTORY,Year(dtmYM(i)) & "_" & Month(dtmYM(i))& "." & ZC_STATIC_TYPE)
				Else
					strArchives=strArchives & "<li><a href="""& ZC_BLOG_HOST &"catalog.asp?date=" & Year(dtmYM(i)) & "-" & Month(dtmYM(i)) & """>" & Year(dtmYM(i)) & " " & ZVA_Month(Month(dtmYM(i))) & "<span class=""article-nums""> (" & objRS(0) & ")</span>" +"</a></li>"
				End If

				If ZC_ARCHIVE_COUNT>0 Then
					If i=ZC_ARCHIVE_COUNT Then Exit For
				End If
			End If

			objRS.Close
			Set objRS=Nothing
		Next
	End If

	strArchives=TransferHTML(strArchives,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/archives.asp",strArchives,"utf-8",True)

	BlogReBuild_Archives=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Catalogs()

	Dim objRS
	Dim objStream

	Dim ArtList

	'Catalogs
	Dim strCatalog
	Set objRS=objConn.Execute("SELECT * FROM [blog_Category] ORDER BY [cate_Order] ASC,[cate_Count] DESC,[cate_ID] ASC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		Do While Not objRS.eof

			strCatalog=strCatalog & "<li><span class=""feed-icon""><a href="""& Categorys(objRS("cate_ID")).RssUrl &""" target=""_blank""><img title=""rss"" width=""20"" height=""12"" src="""&ZC_BLOG_HOST&"IMAGE/LOGO/rss.png"" border=""0"" alt=""rss"" /></a>&nbsp;</span><a href="""& Categorys(objRS("cate_ID")).Url & """>"+Categorys(objRS("cate_ID")).Name + "<span class=""article-nums""> (" & Categorys(objRS("cate_ID")).Count & ")</span>" +"</a></li>"


			If ZC_MOONSOFT_PLUGIN_ENABLE=True Then
				Call BuildCategory(Empty,Categorys(objRS("cate_ID")).ID,Empty,Empty,Empty,ZC_DISPLAY_MODE_ALL,Categorys(objRS("cate_ID")).Directory,Categorys(objRS("cate_ID")).FileName)
			End If

			objRS.MoveNext

		Loop
	End If
	objRS.Close
	Set objRS=Nothing


	strCatalog=TransferHTML(strCatalog,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/catalog.asp",strCatalog,"utf-8",True)

	BlogReBuild_Catalogs=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Categorys()

	Dim objRS
	Dim objStream
	Dim objArticle
	Dim i

	'Categorys
	Dim strCategory

	Dim Category
	For Each Category in Categorys

		If IsObject(Category) Then

			Set objRS=objConn.Execute("SELECT [log_ID] FROM [blog_Article] WHERE ([log_ID]>0) AND ([log_Level]>1) AND ([log_CateID]="&Category.ID&") ORDER BY [log_PostTime] DESC")

			If (Not objRS.bof) And (Not objRS.eof) Then
				For i=1 to ZC_PREVIOUS_COUNT
					Set objArticle=New TArticle
					If objArticle.LoadInfoByID(objRS("log_ID")) Then
						strCategory=strCategory & "<li><a href="""& objArticle.Url & """>" & objArticle.Title & "</a></li>"
					End If
					Set objArticle=Nothing
					objRS.MoveNext
					If objRS.eof Then Exit For
				Next
			End If
			objRS.close

			strCategory=TransferHTML(strCategory,"[no-asp]")

			Call SaveToFile(BlogPath & "/include/category_"&Category.ID&".asp",strCategory,"utf-8",True)

			strCategory=""

		End If
	Next

	BlogReBuild_Categorys=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Authors()

	Dim objRS
	Dim objStream

	'Authors
	Dim strAuthor
	Dim User
	For Each User in Users
		If IsObject(User) Then
				strAuthor=strAuthor & "<li><a href="""& User.Url & """>"+User.Name + " (" & User.Count & ")" +"</a></li>"
		End If
	Next

	strAuthor=TransferHTML(strAuthor,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/authors.asp",strAuthor,"utf-8",True)

	BlogReBuild_Authors=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Tags()

	Dim objRS
	Dim objStream

	'Authors
	Dim strTag

	Set objRS=objConn.Execute("SELECT * FROM [blog_Tag] ORDER BY [tag_Order] DESC,[tag_Count] DESC,[tag_ID] ASC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		Do While Not objRS.eof
				strTag=strTag & "<li><a href="""& Tags(objRS("tag_ID")).Url & """>"+Tags(objRS("tag_ID")).Name + " (" & Tags(objRS("tag_ID")).Count & ")" +"</a></li>"
			objRS.MoveNext
		Loop
	End If
	objRS.Close
	Set objRS=Nothing

	strTag=TransferHTML(strTag,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/tags.asp",strTag,"utf-8",True)

	BlogReBuild_Tags=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Previous()

	Dim i
	Dim objRS
	Dim objStream
	Dim objArticle

	'Previous
	Dim strPrevious
	Set objRS=objConn.Execute("SELECT [log_ID] FROM [blog_Article] WHERE ([log_ID]>0) AND ([log_Level]>1) ORDER BY [log_PostTime] DESC")

	If (Not objRS.bof) And (Not objRS.eof) Then
		For i=1 to ZC_PREVIOUS_COUNT
			Set objArticle=New TArticle
			If objArticle.LoadInfoByID(objRS("log_ID")) Then
				strPrevious=strPrevious & "<li><a href="""& objArticle.Url & """><span class=""article-date"">["& Right("0" & Month(objArticle.PostTime),2) & "/" & Right("0" & Day(objArticle.PostTime),2) &"]</span>" & objArticle.Title & "</a></li>"
			End If
			Set objArticle=Nothing
			objRS.MoveNext
			If objRS.eof Then Exit For
		Next
	End If
	objRS.close

	strPrevious=TransferHTML(strPrevious,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/previous.asp",strPrevious,"utf-8",True)

	BlogReBuild_Previous=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Comments()

	Dim objRS
	Dim objStream
	Dim objArticle

	'Comments
	Dim strComments

	Dim s
	Dim i
	Set objRS=objConn.Execute("SELECT [log_ID],[comm_ID],[comm_Content],[comm_PostTime],[comm_Author] FROM [blog_Comment] WHERE [log_ID]>0 ORDER BY [comm_PostTime] DESC,[comm_ID] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		For i=1 to ZC_MSG_COUNT
			s=objRS("comm_Content")
			s=Replace(s,vbCrlf,"")
			If len(s)>ZC_RECENT_COMMENT_WORD_MAX Then s=Left(s,ZC_RECENT_COMMENT_WORD_MAX-4)&"..."
			Set objArticle=New TArticle
			If objArticle.LoadInfoByID(objRS("log_ID")) Then
				strComments=strComments & "<li><a href="""& objArticle.Url & "#cmt" & objRS("comm_ID") & """ title=""" & objRS("comm_PostTime") & " post by " & objRS("comm_Author") & """>"+s+"</a></li>"
			End If
			Set objArticle=Nothing
			objRS.MoveNext
			If objRS.eof Then Exit For
		Next
	End If
	objRS.close
	Set objRS=Nothing

	strComments=TransferHTML(strComments,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/comments.asp",strComments,"utf-8",True)

	BlogReBuild_Comments=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_GuestComments()

	Dim objRS
	Dim objStream
	Dim objArticle

	'Comments
	Dim strComments

	Dim s
	Dim i
	Set objRS=objConn.Execute("SELECT [log_ID],[comm_ID],[comm_Content],[comm_PostTime],[comm_Author] FROM [blog_Comment] WHERE [log_ID]=0 ORDER BY [comm_ID] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		For i=1 to ZC_MSG_COUNT
			s=objRS("comm_Content")
			s=Replace(s,vbCrlf,"")
			If len(s)>ZC_RECENT_COMMENT_WORD_MAX Then s=Left(s,ZC_RECENT_COMMENT_WORD_MAX-4)&"..."

			strComments=strComments & "<li><a href="""& ZC_BLOG_HOST & "guestbook.asp" & "#cmt" & objRS("comm_ID") & """ title=""" & objRS("comm_PostTime") & " post by " & objRS("comm_Author") & """>"+s+"</a></li>"

			objRS.MoveNext
			If objRS.eof Then Exit For
		Next
	End If
	objRS.close
	Set objRS=Nothing

	strComments=TransferHTML(strComments,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/guestcomments.asp",strComments,"utf-8",True)

	BlogReBuild_GuestComments=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_TrackBacks()

	Dim objRS
	Dim objStream
	Dim objArticle

	'TrackBacks
	Dim strTrackBacks

	Dim s
	Dim i
	Set objRS=objConn.Execute("SELECT * FROM [blog_TrackBack] ORDER BY [tb_ID] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		For i=1 to ZC_MSG_COUNT
			s=objRS("tb_Title")
			s=Replace(s,vbCrlf,"")
			If len(s)>ZC_RECENT_COMMENT_WORD_MAX Then s=Left(s,ZC_RECENT_COMMENT_WORD_MAX-5)&"..."
			Set objArticle=New TArticle
			If objArticle.LoadInfoByID(objRS("log_ID")) Then
				strTrackBacks=strTrackBacks & "<li><a href="""& objArticle.Url & "#tb" & objRS("tb_ID") & """ title=""" & objRS("tb_PostTime") & " post by " & Replace(objRS("tb_Blog"),"""","") & """>"+s+"</a></li>"
			End If
			Set objArticle=Nothing
			objRS.MoveNext
			If objRS.eof Then Exit For
		Next
	End If
	objRS.close
	Set objRS=Nothing

	strTrackBacks=TransferHTML(strTrackBacks,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/trackbacks.asp",strTrackBacks,"utf-8",True)

	BlogReBuild_TrackBacks=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Statistics()

	Dim i
	Dim objRS
	Dim objStream

	'重新统计分类及用户的文章数、评论数
	Dim Category
	For Each Category in Categorys
		If IsObject(Category) Then
			Set objRS=objConn.Execute("SELECT COUNT([log_ID]) FROM [blog_Article] WHERE [log_Level]>1 AND [log_CateID]=" & Category.ID )
			i=objRS(0)
			objConn.Execute("UPDATE [blog_Category] SET [cate_Count]="&i&" WHERE [cate_ID] =" & Category.ID)
			Set objRS=Nothing
		End If
	Next
	Dim User
	For Each User in Users
		If IsObject(User) Then
			Set objRS=objConn.Execute("SELECT COUNT([log_ID]) FROM [blog_Article] WHERE [log_Level]>1 AND [log_AuthorID]=" & User.ID )
			i=objRS(0)
			objConn.Execute("UPDATE [blog_Member] SET [mem_PostLogs]="&i&" WHERE [mem_ID] =" & User.ID)
			Set objRS=Nothing

			Set objRS=objConn.Execute("SELECT COUNT([comm_ID]) FROM [blog_Comment] WHERE [comm_AuthorID]=" & User.ID )
			i=objRS(0)
			objConn.Execute("UPDATE [blog_Member] SET [mem_PostComms]="&i&" WHERE [mem_ID] =" & User.ID)
			Set objRS=Nothing
		End If
	Next
	Dim Tag
	For Each Tag in Tags
		If IsObject(Tag) Then
			Set objRS=objConn.Execute("SELECT COUNT([log_ID]) FROM [blog_Article] WHERE [log_Level]>1 AND [log_Tag] LIKE '%{" & Tag.ID & "}%'")
			i=objRS(0)
			objConn.Execute("UPDATE [blog_Tag] SET [tag_Count]="&i&" WHERE [tag_ID] =" & Tag.ID)
			Set objRS=Nothing
		End If
	Next

	'Statistics
	Dim strStatistics
	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""


	objRS.Open("SELECT COUNT([log_ID])AS allArticle,SUM([log_CommNums]) AS allCommNums,SUM([log_ViewNums]) AS allViewNums,SUM([log_TrackBackNums]) AS allTrackBackNums FROM [blog_Article]")
	If (Not objRS.bof) And (Not objRS.eof) Then
		strStatistics=strStatistics & "<li>"& ZC_MSG082 &":" & objRS("allArticle") & "</li>"
		strStatistics=strStatistics & "<li>"& ZC_MSG124 &":" & objRS("allCommNums") & "</li>"
		strStatistics=strStatistics & "<li>"& ZC_MSG125 &":" & objRS("allTrackBackNums") & "</li>"
		strStatistics=strStatistics & "<li>"& ZC_MSG129 &":" & objRS("allViewNums") & "</li>"
	End If
	objRS.Close

	objRS.Open("SELECT COUNT([comm_ID])AS allComment FROM [blog_Comment] WHERE [log_ID]=0")
	If (Not objRS.bof) And (Not objRS.eof) Then
		strStatistics=strStatistics & "<li>"& ZC_MSG284 &":" & objRS("allComment") & "</li>"
	End If
	objRS.Close

	strStatistics=strStatistics & "<li>"& ZC_MSG083 &":" & ZC_BLOG_CSS & "</li>"
	strStatistics=strStatistics & "<li>"& ZC_MSG084 &":" & ZC_BLOG_LANGUAGE & "</li>"

	Set objRS=Nothing

	strStatistics=TransferHTML(strStatistics,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/statistics.asp",strStatistics,"utf-8",False)


	Call GetCategory()
	Call GetUser()
	Call GetTags()
	Call GetKeyWords()

	BlogReBuild_Statistics=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Blog ReBuild
'*********************************************************
Function MakeBlogReBuild()

	BlogReBuild_Statistics

	BlogReBuild_Archives

	BlogReBuild_Previous

	BlogReBuild_Comments

	BlogReBuild_GuestComments

	BlogReBuild_TrackBacks

	BlogReBuild_Catalogs

	BlogReBuild_Calendar

	BlogReBuild_Authors

	BlogReBuild_Tags

	'BlogReBuild_Categorys

	BuildAllCache

	ExportRSS

	ExportATOM

	Call SetBlogHint(True,False,Empty)

	Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /></head><body>"

	Response.Write "<div id=""divMain""><div class=""Header"">" & ZC_MSG072 & "</div>"
	Response.Write "<div id=""divMain2"">"
	Call GetBlogHint()
	Response.Write "<form  name=""edit"" id=""edit"">"

	Response.Write "<p>" & ZC_MSG225 &"</p>"
	Response.Write "<p>" & Replace(ZC_MSG169,"%n",RunTime/1000)&"</p>"

	Response.Write "</form></div></div>"
	Response.Write "</body></html>"

	MakeBlogReBuild=True

End Function
'*********************************************************




'*********************************************************
' 目的：    All Files ReBuild
'*********************************************************
Function MakeFileReBuild()

	Dim intPage

	intPage=CInt(Request.QueryString("page"))

	If intPage=0 Then intPage=1
	If intPage=1 Then
		Application.Lock
		Application(ZC_BLOG_CLSID & "FileRebuildStep")=""
		Application(ZC_BLOG_CLSID & "FileRebuildTime")=0
		Application.UnLock
	End If

	Dim i,j

	Dim objRS
	Dim objArticle

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source="SELECT [log_ID] FROM [blog_Article] WHERE [log_Level]>1"
	objRS.Open()

	If (Not objRS.bof) And (Not objRS.eof) Then

		objRS.PageSize = ZC_REBUILD_FILE_COUNT

		If intPage>objRS.PageCount Then

			MakeFileReBuild=True

			Call SetBlogHint(True,Empty,False)

			Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /></head><body>"

			Response.Write "<div id=""divMain""><div class=""Header"">" & ZC_MSG073 & "</div>"
			Response.Write "<div id=""divMain2"">"
			Call GetBlogHint()
			Response.Write "<form  name=""edit"" id=""edit"">"

			Response.Write "<p>" & ZC_MSG225 &"</p>"
			Response.Write "<p>" & Replace(ZC_MSG169,"%n",Application(ZC_BLOG_CLSID & "FileRebuildTime")/1000)&"</p>"

			Response.Write "</form></div></div>"
			Response.Write "</body></html>"

			Response.End
			Exit Function

		End If

		objRS.AbsolutePage = intPage

		For i = 1 To ZC_REBUILD_FILE_COUNT

			Call BuildArticle(objRS("log_ID"),False,False)

			objRS.MoveNext
			If objRS.eof Then Exit For
		Next

		Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8""/><meta http-equiv=""Content-Language"" content=""zh-cn"" /><meta http-equiv=""refresh"" content="""&ZC_REBUILD_FILE_INTERVAL&";URL="&ZC_BLOG_HOST&"cmd.asp?act=FileReBuild&page="&intPage+1&"""/><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /><title>"&ZC_MSG073&"</title></head><body>"

		Response.Write "<div id=""divMain""><div class=""Header"">" & ZC_MSG073 & "</div>"
		Response.Write "<div id=""divMain2"">"
		Response.Write "<form  name=""edit"" id=""edit"">"

		Application.Lock
		Application(ZC_BLOG_CLSID & "FileRebuildStep")=Application(ZC_BLOG_CLSID & "FileRebuildStep") & "<p>" &Replace(ZC_MSG227,"%n",intPage)&"</p>"
		Application(ZC_BLOG_CLSID & "FileRebuildTime")=CLng(Application(ZC_BLOG_CLSID & "FileRebuildTime"))+RunTime
		Application.UnLock

		Response.Write Application(ZC_BLOG_CLSID & "FileRebuildStep")
		Response.Write "<p>" &Replace(ZC_MSG152,"%n",ZC_REBUILD_FILE_INTERVAL)&"</p>"
		Response.Write "</form></div></div>"
		Response.Write "</body></html>"

	Else

		Response.Redirect "admin/admin.asp?act=AskFileReBuild"
		Response.End

	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    全新的部份索引程序
'*********************************************************
Function BuildAllCache()

	Dim strList

	Dim ArticleList
	Dim AuthList
	Dim CateList
	Dim TagsList

	Dim aryAllList()

	Dim objRS
	Dim i
	Dim j
	Dim n
	Dim l
	Dim k

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	objRS.Open("SELECT [log_ID] FROM [blog_Article] WHERE ([log_Level]>1) AND ([log_Istop]=False) ORDER BY [log_PostTime] DESC")

	If (Not objRS.bof) And (Not objRS.eof) Then

		objRS.PageSize = ZC_DISPLAY_COUNT
		ReDim aryAllList(objRS.PageCount+1)

		For i=1 to objRS.PageCount
			objRS.AbsolutePage=i
			For j = 1 To objRS.PageSize
				If j=1 Then aryAllList(i)="AllPage" & i & "["

				If i=1 Then
					aryAllList(i)=aryAllList(i) & objRS("log_ID") & ";"
				End If

				If j=objRS.PageSize Then aryAllList(i)=aryAllList(i) & "]"
				objRS.MoveNext
				If objRS.EOF Then aryAllList(i)=aryAllList(i) & "]":Exit For
			Next
		Next

	End If
	objRS.Close
	strList=strList & Join(aryAllList)
	Erase aryAllList



	objRS.Open("SELECT [log_ID] FROM [blog_Article] WHERE ([log_Level]>1) AND ([log_Istop]=True) ORDER BY [log_PostTime] DESC")

	If (Not objRS.bof) And (Not objRS.eof) Then

		objRS.PageSize = ZC_DISPLAY_COUNT
		ReDim aryAllList(objRS.PageCount+1)

		For i=1 to objRS.PageCount
			objRS.AbsolutePage=i
			For j = 1 To objRS.PageSize
				If j=1 Then aryAllList(i)="IstopPage" & i & "["
				aryAllList(i)=aryAllList(i) & objRS("log_ID") & ";"
				If j=objRS.PageSize Then aryAllList(i)=aryAllList(i) & "]"
				objRS.MoveNext
				If objRS.EOF Then aryAllList(i)=aryAllList(i) & "]":Exit For
			Next
		Next

	End If
	objRS.Close
	strList=strList & Join(aryAllList)
	Erase aryAllList

	Call SaveToFile(BlogPath & "/CACHE/cache_list_"&ZC_BLOG_CLSID&".html",strList,"utf-8",False)

	BuildAllCache=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function BlogReBuild_Calendar()

	Dim objStream
	Dim strCalendar
	Dim i,j
	Dim objRS
	Dim k,l,m,n

	'Calendar
	strCalendar=MakeCalendar("")

	strCalendar=TransferHTML(strCalendar,"[no-asp]")

	Call SaveToFile(BlogPath & "/include/calendar.asp",strCalendar,"utf-8",True)

	BlogReBuild_Calendar=True

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    List User Rights
'*********************************************************
Function ListUser_Rights()

	Dim s
	Dim i
	Dim strAction
	Dim aryAction

	strAction="login|verify|logout|admin|cmt|tb|vrs|BlogReBuild|FileReBuild|ArticleMng|ArticleEdt|ArticlePst|ArticleDel|CategoryMng|CategoryPst|CategoryDel|CommentMng|CommentDel|CommentRev|TrackBackMng|TrackBackDel|TrackBackSnd|UserMng|UserEdt|UserCrt|UserDel|FileMng|FileUpload|FileDel|Search|TagMng|TagEdt|TagPst|TagDel|SettingMng|SettingSav|PlugInMng|rss|SiteFileMng|SiteFileEdt|SiteFilePst|SiteFileDel|Root"

	aryAction=Split(strAction, "|")

	s=ZC_MSG019

	Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8""/><meta http-equiv=""Content-Language"" content=""zh-cn"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /><title>"&ZC_MSG021&"</title></head><body>"

	Response.Write "<div id=""divMain""><div class=""Header"">" & ZC_MSG021 & "</div>"
	Response.Write "<div id=""divMain2""><form  name=""edit"" id=""edit""><P>"

	Response.Write ZC_MSG001 & ":" & BLogUser.Name & "<br/><br/>"
	Response.Write ZC_MSG249 & ":" & ZVA_User_Level_Name(BLogUser.Level) & "<br/><br/>"

	For i=LBound(aryAction) To UBound(aryAction)

		If Not CheckRights(aryAction(i)) Then s=Replace(s,"%s",":<font color=Red>fail</font>"&"<br/><br/>",1,1) Else s=Replace(s,"%s",":<font color=green>ok</font>"&"<br/><br/>",1,1)

	Next

	Response.Write s

	Response.Write "</p></form></div></div>"
	Response.Write "</body></html>"

	ListUser_Rights=True

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Export RSS 2.0
'*********************************************************
Function ExportRSS()

	Dim Rss2Export
	Dim objArticle

	Set Rss2Export = New TRss2Export

	With Rss2Export

		.TimeZone=ZC_TIME_ZONE

		.AddChannelAttribute "title",TransferHTML(ZC_BLOG_TITLE,"[html-format]")
		.AddChannelAttribute "link",TransferHTML(ZC_BLOG_HOST,"[html-format]")
		.AddChannelAttribute "description",TransferHTML(ZC_BLOG_SUBTITLE,"[html-format]")
		.AddChannelAttribute "generator","RainbowSoft Studio Z-Blog " & ZC_BLOG_VERSION
		.AddChannelAttribute "language",ZC_BLOG_LANGUAGE
		.AddChannelAttribute "copyright",TransferHTML(ZC_BLOG_COPYRIGHT,"[html-format]")
		.AddChannelAttribute "pubDate",Now

			Dim i
			Dim objRS
			Set objRS=objConn.Execute("SELECT [log_ID],[log_Tag],[log_CateID],[log_Title],[log_Intro],[log_Content],[log_Level],[log_AuthorID],[log_PostTime],[log_CommNums],[log_ViewNums],[log_TrackBackNums],[log_Url],[log_Istop] FROM [blog_Article] WHERE ([log_ID]>0) AND ([log_Level]>2) ORDER BY [log_PostTime] DESC")

			If (Not objRS.bof) And (Not objRS.eof) Then
				For i=1 to ZC_RSS2_COUNT
					Set objArticle=New TArticle
					If objArticle.LoadInfoByArray(Array(objRS("log_ID"),objRS("log_Tag"),objRS("log_CateID"),objRS("log_Title"),objRS("log_Intro"),objRS("log_Content"),objRS("log_Level"),objRS("log_AuthorID"),objRS("log_PostTime"),objRS("log_CommNums"),objRS("log_ViewNums"),objRS("log_TrackBackNums"),objRS("log_Url"),objRS("log_Istop"))) Then

					If ZC_RSS_EXPORT_WHOLE Then
					.AddItem objArticle.HtmlTitle,Users(objArticle.AuthorID).Email & " (" & Users(objArticle.AuthorID).Name & ")",objArticle.HtmlUrl,objArticle.PostTime,objArticle.HtmlUrl,objArticle.HtmlContent,Categorys(objArticle.CateID).HtmlName,objArticle.CommentUrl,objArticle.WfwComment,objArticle.WfwCommentRss,objArticle.TrackBackUrl
					Else
					.AddItem objArticle.HtmlTitle,Users(objArticle.AuthorID).Email & " (" & Users(objArticle.AuthorID).Name & ")",objArticle.HtmlUrl,objArticle.PostTime,objArticle.HtmlUrl,objArticle.HtmlIntro,Categorys(objArticle.CateID).HtmlName,objArticle.CommentUrl,objArticle.WfwComment,objArticle.WfwCommentRss,objArticle.TrackBackUrl
					End If

					End If
					objRS.MoveNext
					If objRS.eof Then Exit For
					Set objArticle=Nothing
				Next
			End If

	End With

	Rss2Export.Execute

	Rss2Export.SaveToFile(BlogPath & "/rss.xml")

	Set Rss2Export = Nothing

	objRS.close
	Set objRS=Nothing
	ExportRSS=True

	Response.ContentType = "text/html"
	Response.Clear

End Function
'*********************************************************




'*********************************************************
' 目的：    Export ATOM 1.0
'*********************************************************
Function ExportATOM()

	Dim objArticle

	Dim Atom10Export
	Set Atom10Export = New TAtom10Export

	Atom10Export.TimeZone=ZC_TIME_ZONE

	Dim AtomEntry

	Dim AtomFeed
	Set AtomFeed = New TAtomFeed

	With AtomFeed

		.atomTitle=TransferHTML(ZC_BLOG_TITLE,"[html-format]")
		.atomSubtitle=TransferHTML(ZC_BLOG_SUBTITLE,"[html-format]")
		.atomID=ZC_BLOG_HOST
		.atomLink "alternate","text/html",ZC_BLOG_HOST
		.atomLink "self","application/atom+xml",ZC_BLOG_HOST & "atom.xml"
		'.atomPerson "author",BlogUser.Name,BlogUser.Email,BlogUser.HomePage
		.atomGenerator "RainbowSoft Studio Z-Blog","http://www.rainbowsoft.org/",ZC_BLOG_VERSION
		.atomUpdated=Now

	End With

	Atom10Export.GetFeed(AtomFeed.Node)


	Dim i
	Dim objRS
	Set objRS=objConn.Execute("SELECT [log_ID],[log_Tag],[log_CateID],[log_Title],[log_Intro],NULL,[log_Level],[log_AuthorID],[log_PostTime],[log_CommNums],[log_ViewNums],[log_TrackBackNums],[log_Url],[log_Istop] FROM [blog_Article] WHERE ([log_ID]>0) AND ([log_Level]>2) ORDER BY [log_PostTime] DESC")

	If (Not objRS.bof) And (Not objRS.eof) Then
		For i=1 to ZC_RSS2_COUNT

			Set objArticle=New TArticle
			Set AtomEntry = New TAtomEntry

			With AtomEntry
				If objArticle.LoadInfoByArray(Array(objRS("log_ID"),objRS("log_Tag"),objRS("log_CateID"),objRS("log_Title"),objRS("log_Intro"),,objRS("log_Level"),objRS("log_AuthorID"),objRS("log_PostTime"),objRS("log_CommNums"),objRS("log_ViewNums"),objRS("log_TrackBackNums"),objRS("log_Url"),objRS("log_Istop"))) Then

					.atomTitle=objArticle.HtmlTitle
					.atomPerson "author",Users(objArticle.AuthorID).Name,Users(objArticle.AuthorID).Email,Users(objArticle.AuthorID).HomePage
					.atomCategory "",Categorys(objArticle.CateID).Url,Categorys(objArticle.CateID).HtmlName
					.atomUpdated=objArticle.PostTime
					.atomPublished=objArticle.PostTime
					.atomSummary=objArticle.HtmlIntro
					.atomLink "alternate","text/html",objArticle.Url
					.atomID=objArticle.Url

				End If
			End With

			Atom10Export.GetEntry(AtomEntry.Node)
			Set AtomEntry = Nothing
			Set objArticle=Nothing

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next
	End If

	objRS.close
	Set objRS=Nothing


	Atom10Export.Execute

	Atom10Export.SaveToFile(BlogPath & "/atom.xml")

	Set Atom10Export = Nothing

	ExportATOM=True

	Response.ContentType = "text/html"
	Response.Clear

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Save Blog Setting
'*********************************************************
Function SaveSetting()

	Dim i,j
	Dim s,t
	Dim strContent

	strContent=LoadFromFile(BlogPath & "/c_custom.asp","utf-8")

	Dim strZC_BLOG_HOST
	Dim strZC_BLOG_TITLE
	Dim strZC_BLOG_SUBTITLE
	Dim strZC_BLOG_NAME
	Dim strZC_BLOG_SUB_NAME
	Dim strZC_BLOG_CSS
	Dim strZC_BLOG_COPYRIGHT
	Dim strZC_BLOG_MASTER

	strZC_BLOG_HOST=Request.Form("edtZC_BLOG_HOST")
	If Right(strZC_BLOG_HOST,1)<>"/" Then strZC_BLOG_HOST=strZC_BLOG_HOST & "/"

	strZC_BLOG_TITLE=Request.Form("edtZC_BLOG_TITLE")

	strZC_BLOG_SUBTITLE=Request.Form("edtZC_BLOG_SUBTITLE")

	strZC_BLOG_NAME=Request.Form("edtZC_BLOG_NAME")

	strZC_BLOG_SUB_NAME=Request.Form("edtZC_BLOG_SUB_NAME")

	strZC_BLOG_CSS=Request.Form("edtZC_BLOG_CSS")

	strZC_BLOG_COPYRIGHT=Request.Form("edtZC_BLOG_COPYRIGHT")

	strZC_BLOG_MASTER=Request.Form("edtZC_BLOG_MASTER")

	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_HOST",strZC_BLOG_HOST)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_TITLE",strZC_BLOG_TITLE)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_SUBTITLE",strZC_BLOG_SUBTITLE)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_NAME",strZC_BLOG_NAME)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_SUB_NAME",strZC_BLOG_SUB_NAME)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_CSS",strZC_BLOG_CSS)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_COPYRIGHT",strZC_BLOG_COPYRIGHT)
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_MASTER",strZC_BLOG_MASTER)

	If UCase(strZC_BLOG_HOST)<>UCase("""" & CStr(ZC_BLOG_HOST) & """") Then Call SetBlogHint(Empty,Empty,True)
	If UCase(strZC_BLOG_TITLE)<>UCase("""" & CStr(ZC_BLOG_TITLE) & """") Then Call SetBlogHint(Empty,Empty,True)
	If UCase(strZC_BLOG_SUBTITLE)<>UCase("""" & CStr(ZC_BLOG_SUBTITLE) & """") Then Call SetBlogHint(Empty,Empty,Empty)
	If UCase(strZC_BLOG_NAME)<>UCase("""" & CStr(ZC_BLOG_NAME) & """") Then Call SetBlogHint(Empty,Empty,True)
	If UCase(strZC_BLOG_SUB_NAME)<>UCase("""" & CStr(ZC_BLOG_SUB_NAME) & """") Then Call SetBlogHint(Empty,Empty,True)
	If UCase(strZC_BLOG_CSS)<>UCase("""" & CStr(ZC_BLOG_CSS) & """") Then Call SetBlogHint(Empty,Empty,True)
	If UCase(strZC_BLOG_COPYRIGHT)<>UCase("""" & CStr(ZC_BLOG_COPYRIGHT) & """") Then Call SetBlogHint(Empty,Empty,True)
	If UCase(strZC_BLOG_MASTER)<>UCase("""" & CStr(ZC_BLOG_MASTER) & """") Then Call SetBlogHint(Empty,True,Empty)

	Call SaveToFile(BlogPath & "/c_custom.asp",strContent,"utf-8",False)

	strContent=LoadFromFile(BlogPath & "/c_option.asp","utf-8")

	Dim strZC_BLOG_CLSID
	strZC_BLOG_CLSID=Request.Form("edtZC_BLOG_CLSID")
	If CheckRegExp(strZC_BLOG_CLSID,"[guid]") Then
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_CLSID",strZC_BLOG_CLSID)
	If UCase(strZC_BLOG_CLSID)<>UCase("""" & CStr(ZC_BLOG_CLSID) & """") Then Call SetBlogHintWithCLSID(True,True,True,Replace(strZC_BLOG_CLSID,"""",""))
	End If

	Dim strZC_TIME_ZONE
	strZC_TIME_ZONE=Request.Form("edtZC_TIME_ZONE")
	Call SaveValueForSetting(strContent,True,"String","ZC_TIME_ZONE",strZC_TIME_ZONE)
	If UCase(strZC_TIME_ZONE)<>UCase("""" & CStr(ZC_TIME_ZONE) & """") Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_BLOG_LANGUAGE
	strZC_BLOG_LANGUAGE=Request.Form("edtZC_BLOG_LANGUAGE")
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_LANGUAGE",strZC_BLOG_LANGUAGE)
	If UCase(strZC_BLOG_LANGUAGE)<>UCase("""" & CStr(ZC_BLOG_LANGUAGE) & """") Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UPDATE_INFO_URL
	strZC_UPDATE_INFO_URL=Request.Form("edtZC_UPDATE_INFO_URL")
	If (Not CheckRegExp(strZC_UPDATE_INFO_URL,"[homepage]")) And (strZC_UPDATE_INFO_URL<>"") Then strZC_UPDATE_INFO_URL="http://update.rainbowsoft.org/info/"
	Call SaveValueForSetting(strContent,True,"String","ZC_UPDATE_INFO_URL",strZC_UPDATE_INFO_URL)
	If UCase(strZC_UPDATE_INFO_URL)<>UCase("""" & CStr(ZC_UPDATE_INFO_URL) & """") Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_STATIC_TYPE
	strZC_STATIC_TYPE=Request.Form("edtZC_STATIC_TYPE")
	Call SaveValueForSetting(strContent,True,"String","ZC_STATIC_TYPE",strZC_STATIC_TYPE)
	If UCase(strZC_STATIC_TYPE)<>UCase("""" & CStr(ZC_STATIC_TYPE) & """") Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_STATIC_DIRECTORY
	strZC_STATIC_DIRECTORY=Request.Form("edtZC_STATIC_DIRECTORY")
	Call SaveValueForSetting(strContent,True,"String","ZC_STATIC_DIRECTORY",strZC_STATIC_DIRECTORY)
	If UCase(strZC_STATIC_DIRECTORY)<>UCase("""" & CStr(ZC_STATIC_DIRECTORY) & """") Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_BLOG_VERSION
	strZC_BLOG_VERSION=Request.Form("edtZC_BLOG_VERSION")
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_VERSION",strZC_BLOG_VERSION)
	If UCase(strZC_BLOG_VERSION)<>UCase("""" & CStr(ZC_BLOG_VERSION) & """") Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_BLOG_WEBEDIT
	strZC_BLOG_WEBEDIT=Request.Form("edtZC_BLOG_WEBEDIT")
	Call SaveValueForSetting(strContent,True,"String","ZC_BLOG_WEBEDIT",strZC_BLOG_WEBEDIT)
	If UCase(strZC_BLOG_WEBEDIT)<>UCase("""" & CStr(ZC_BLOG_WEBEDIT) & """") Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_REBUILD_FILE_COUNT
	strZC_REBUILD_FILE_COUNT=Request.Form("edtZC_REBUILD_FILE_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_REBUILD_FILE_COUNT",strZC_REBUILD_FILE_COUNT)
	If UCase(strZC_REBUILD_FILE_COUNT)<>UCase(CStr(ZC_REBUILD_FILE_COUNT)) Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_REBUILD_FILE_INTERVAL
	strZC_REBUILD_FILE_INTERVAL=Request.Form("edtZC_REBUILD_FILE_INTERVAL")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_REBUILD_FILE_INTERVAL",strZC_REBUILD_FILE_INTERVAL)
	If UCase(strZC_REBUILD_FILE_INTERVAL)<>UCase(CStr(ZC_REBUILD_FILE_INTERVAL)) Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_UPLOAD_FILETYPE
	strZC_UPLOAD_FILETYPE=Request.Form("edtZC_UPLOAD_FILETYPE")
	Call SaveValueForSetting(strContent,True,"String","ZC_UPLOAD_FILETYPE",strZC_UPLOAD_FILETYPE)
	If UCase(strZC_UPLOAD_FILETYPE)<>UCase("""" & CStr(ZC_UPLOAD_FILETYPE) & """") Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_UPLOAD_FILESIZE
	strZC_UPLOAD_FILESIZE=Request.Form("edtZC_UPLOAD_FILESIZE")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_UPLOAD_FILESIZE",strZC_UPLOAD_FILESIZE)
	If UCase(strZC_UPLOAD_FILESIZE)<>UCase(CStr(ZC_UPLOAD_FILESIZE)) Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_COMMENT_VERIFY_ENABLE
	strZC_COMMENT_VERIFY_ENABLE=Request.Form("edtZC_COMMENT_VERIFY_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_COMMENT_VERIFY_ENABLE",strZC_COMMENT_VERIFY_ENABLE)
	If UCase(strZC_COMMENT_VERIFY_ENABLE)<>UCase(CStr(ZC_COMMENT_VERIFY_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)



	Dim strZC_MSG_COUNT
	strZC_MSG_COUNT=Request.Form("edtZC_MSG_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_MSG_COUNT",strZC_MSG_COUNT)
	If UCase(strZC_MSG_COUNT)<>UCase(CStr(ZC_MSG_COUNT)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_ARCHIVE_COUNT
	strZC_ARCHIVE_COUNT=Request.Form("edtZC_ARCHIVE_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_ARCHIVE_COUNT",strZC_ARCHIVE_COUNT)
	If UCase(strZC_ARCHIVE_COUNT)<>UCase(CStr(ZC_ARCHIVE_COUNT)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_PREVIOUS_COUNT
	strZC_PREVIOUS_COUNT=Request.Form("edtZC_PREVIOUS_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_PREVIOUS_COUNT",strZC_PREVIOUS_COUNT)
	If UCase(strZC_PREVIOUS_COUNT)<>UCase(CStr(ZC_PREVIOUS_COUNT)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_DISPLAY_COUNT
	strZC_DISPLAY_COUNT=Request.Form("edtZC_DISPLAY_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_DISPLAY_COUNT",strZC_DISPLAY_COUNT)
	If UCase(strZC_DISPLAY_COUNT)<>UCase(CStr(ZC_DISPLAY_COUNT)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_MANAGE_COUNT
	strZC_MANAGE_COUNT=Request.Form("edtZC_MANAGE_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_MANAGE_COUNT",strZC_MANAGE_COUNT)
	If UCase(strZC_MANAGE_COUNT)<>UCase(CStr(ZC_MANAGE_COUNT)) Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_RSS2_COUNT
	strZC_RSS2_COUNT=Request.Form("edtZC_RSS2_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_RSS2_COUNT",strZC_RSS2_COUNT)
	If UCase(strZC_RSS2_COUNT)<>UCase(CStr(ZC_RSS2_COUNT)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_SEARCH_COUNT
	strZC_SEARCH_COUNT=Request.Form("edtZC_SEARCH_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_SEARCH_COUNT",strZC_SEARCH_COUNT)
	If UCase(strZC_SEARCH_COUNT)<>UCase(CStr(ZC_SEARCH_COUNT)) Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_PAGEBAR_COUNT
	strZC_PAGEBAR_COUNT=Request.Form("edtZC_PAGEBAR_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_PAGEBAR_COUNT",strZC_PAGEBAR_COUNT)
	If UCase(strZC_PAGEBAR_COUNT)<>UCase(CStr(ZC_PAGEBAR_COUNT)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_USE_NAVIGATE_ARTICLE
	strZC_USE_NAVIGATE_ARTICLE=Request.Form("edtZC_USE_NAVIGATE_ARTICLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_USE_NAVIGATE_ARTICLE",strZC_USE_NAVIGATE_ARTICLE)
	If UCase(strZC_USE_NAVIGATE_ARTICLE)<>UCase(CStr(ZC_USE_NAVIGATE_ARTICLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_MUTUALITY_COUNT
	strZC_MUTUALITY_COUNT=Request.Form("edtZC_MUTUALITY_COUNT")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_MUTUALITY_COUNT",strZC_MUTUALITY_COUNT)
	If UCase(strZC_MUTUALITY_COUNT)<>UCase(CStr(ZC_MUTUALITY_COUNT)) Then Call SetBlogHint(Empty,Empty,True)


	Dim strZC_UBB_LINK_ENABLE
	strZC_UBB_LINK_ENABLE=Request.Form("edtZC_UBB_LINK_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_LINK_ENABLE",strZC_UBB_LINK_ENABLE)
	If UCase(strZC_UBB_LINK_ENABLE)<>UCase(CStr(ZC_UBB_LINK_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_FONT_ENABLE
	strZC_UBB_FONT_ENABLE=Request.Form("edtZC_UBB_FONT_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_FONT_ENABLE",strZC_UBB_FONT_ENABLE)
	If UCase(strZC_UBB_FONT_ENABLE)<>UCase(CStr(ZC_UBB_FONT_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_CODE_ENABLE
	strZC_UBB_CODE_ENABLE=Request.Form("edtZC_UBB_CODE_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_CODE_ENABLE",strZC_UBB_CODE_ENABLE)
	If UCase(strZC_UBB_CODE_ENABLE)<>UCase(CStr(ZC_UBB_CODE_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_FACE_ENABLE
	strZC_UBB_FACE_ENABLE=Request.Form("edtZC_UBB_FACE_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_FACE_ENABLE",strZC_UBB_FACE_ENABLE)
	If UCase(strZC_UBB_FACE_ENABLE)<>UCase(CStr(ZC_UBB_FACE_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_IMAGE_ENABLE
	strZC_UBB_IMAGE_ENABLE=Request.Form("edtZC_UBB_IMAGE_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_IMAGE_ENABLE",strZC_UBB_IMAGE_ENABLE)
	If UCase(strZC_UBB_IMAGE_ENABLE)<>UCase(CStr(ZC_UBB_IMAGE_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_MEDIA_ENABLE
	strZC_UBB_MEDIA_ENABLE=Request.Form("edtZC_UBB_MEDIA_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_MEDIA_ENABLE",strZC_UBB_MEDIA_ENABLE)
	If UCase(strZC_UBB_MEDIA_ENABLE)<>UCase(CStr(ZC_UBB_MEDIA_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_FLASH_ENABLE
	strZC_UBB_FLASH_ENABLE=Request.Form("edtZC_UBB_FLASH_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_FLASH_ENABLE",strZC_UBB_FLASH_ENABLE)
	If UCase(strZC_UBB_FLASH_ENABLE)<>UCase(CStr(ZC_UBB_FLASH_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_TYPESET_ENABLE
	strZC_UBB_TYPESET_ENABLE=Request.Form("edtZC_UBB_TYPESET_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_TYPESET_ENABLE",strZC_UBB_TYPESET_ENABLE)
	If UCase(strZC_UBB_TYPESET_ENABLE)<>UCase(CStr(ZC_UBB_TYPESET_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_UBB_AUTOLINK_ENABLE
	strZC_UBB_AUTOLINK_ENABLE=Request.Form("edtZC_UBB_AUTOLINK_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_UBB_AUTOLINK_ENABLE",strZC_UBB_AUTOLINK_ENABLE)
	If UCase(strZC_UBB_AUTOLINK_ENABLE)<>UCase(CStr(ZC_UBB_AUTOLINK_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_AUTO_NEWLINE
	strZC_AUTO_NEWLINE=Request.Form("edtZC_AUTO_NEWLINE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_AUTO_NEWLINE",strZC_AUTO_NEWLINE)
	If UCase(strZC_AUTO_NEWLINE)<>UCase(CStr(ZC_AUTO_NEWLINE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_COMMENT_NOFOLLOW_ENABLE
	strZC_COMMENT_NOFOLLOW_ENABLE=Request.Form("edtZC_COMMENT_NOFOLLOW_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_COMMENT_NOFOLLOW_ENABLE",strZC_COMMENT_NOFOLLOW_ENABLE)
	If UCase(strZC_COMMENT_NOFOLLOW_ENABLE)<>UCase(CStr(ZC_COMMENT_NOFOLLOW_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_JAPAN_TO_HTML
	strZC_JAPAN_TO_HTML=Request.Form("edtZC_JAPAN_TO_HTML")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_JAPAN_TO_HTML",strZC_JAPAN_TO_HTML)
	If UCase(strZC_JAPAN_TO_HTML)<>UCase(CStr(ZC_JAPAN_TO_HTML)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_EMOTICONS_FILENAME
	strZC_EMOTICONS_FILENAME=Request.Form("edtZC_EMOTICONS_FILENAME")
	Call SaveValueForSetting(strContent,True,"String","ZC_EMOTICONS_FILENAME",strZC_EMOTICONS_FILENAME) 
	If UCase(strZC_EMOTICONS_FILENAME)<>UCase("""" & CStr(ZC_EMOTICONS_FILENAME) & """") Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_EMOTICONS_FILESIZE
	strZC_EMOTICONS_FILESIZE=Request.Form("edtZC_EMOTICONS_FILESIZE")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_EMOTICONS_FILESIZE",strZC_EMOTICONS_FILESIZE)
	If UCase(strZC_EMOTICONS_FILESIZE)<>UCase(CStr(ZC_EMOTICONS_FILESIZE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_MOONSOFT_PLUGIN_ENABLE
	strZC_MOONSOFT_PLUGIN_ENABLE=Request.Form("edtZC_MOONSOFT_PLUGIN_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_MOONSOFT_PLUGIN_ENABLE",strZC_MOONSOFT_PLUGIN_ENABLE)
	If UCase(strZC_MOONSOFT_PLUGIN_ENABLE)<>UCase(CStr(ZC_MOONSOFT_PLUGIN_ENABLE)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_COMMENT_REVERSE_ORDER_EXPORT
	strZC_COMMENT_REVERSE_ORDER_EXPORT=Request.Form("edtZC_COMMENT_REVERSE_ORDER_EXPORT")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_COMMENT_REVERSE_ORDER_EXPORT",strZC_COMMENT_REVERSE_ORDER_EXPORT)
	If UCase(strZC_COMMENT_REVERSE_ORDER_EXPORT)<>UCase(CStr(ZC_COMMENT_REVERSE_ORDER_EXPORT)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_GUESTBOOK_CONTENT
	strZC_GUESTBOOK_CONTENT=Request.Form("edtZC_GUESTBOOK_CONTENT")
	Call SaveValueForSetting(strContent,True,"String","ZC_GUESTBOOK_CONTENT",strZC_GUESTBOOK_CONTENT) 
	If UCase(strZC_GUESTBOOK_CONTENT)<>UCase("""" & CStr(ZC_GUESTBOOK_CONTENT) & """") Then Call SetBlogHint(Empty,Empty,Empty)

	Dim strZC_CUSTOM_DIRECTORY_ENABLE
	strZC_CUSTOM_DIRECTORY_ENABLE=Request.Form("edtZC_CUSTOM_DIRECTORY_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_CUSTOM_DIRECTORY_ENABLE",strZC_CUSTOM_DIRECTORY_ENABLE)
	If UCase(strZC_CUSTOM_DIRECTORY_ENABLE)<>UCase(CStr(ZC_CUSTOM_DIRECTORY_ENABLE)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_CUSTOM_DIRECTORY_ANONYMOUS
	strZC_CUSTOM_DIRECTORY_ANONYMOUS=Request.Form("edtZC_CUSTOM_DIRECTORY_ANONYMOUS")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_CUSTOM_DIRECTORY_ANONYMOUS",strZC_CUSTOM_DIRECTORY_ANONYMOUS)
	If UCase(strZC_CUSTOM_DIRECTORY_ANONYMOUS)<>UCase(CStr(ZC_CUSTOM_DIRECTORY_ANONYMOUS)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_CUSTOM_DIRECTORY_REGEX
	strZC_CUSTOM_DIRECTORY_REGEX=Request.Form("edtZC_CUSTOM_DIRECTORY_REGEX")
	Call SaveValueForSetting(strContent,True,"String","ZC_CUSTOM_DIRECTORY_REGEX",strZC_CUSTOM_DIRECTORY_REGEX) 
	If UCase(strZC_CUSTOM_DIRECTORY_REGEX)<>UCase("""" & CStr(ZC_CUSTOM_DIRECTORY_REGEX) & """") Then Call SetBlogHint(Empty,Empty,True)


	Dim strZC_IE_DISPLAY_WAP
	strZC_IE_DISPLAY_WAP=Request.Form("edtZC_IE_DISPLAY_WAP")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_IE_DISPLAY_WAP",strZC_IE_DISPLAY_WAP)

	Dim strZC_DISPLAY_COUNT_WAP
	strZC_DISPLAY_COUNT_WAP=Request.Form("edtZC_DISPLAY_COUNT_WAP")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_DISPLAY_COUNT_WAP",strZC_DISPLAY_COUNT_WAP)

	Dim strZC_COMMENT_COUNT_WAP
	strZC_COMMENT_COUNT_WAP=Request.Form("edtZC_COMMENT_COUNT_WAP")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_COMMENT_COUNT_WAP",strZC_COMMENT_COUNT_WAP)

	Dim strZC_PAGEBAR_COUNT_WAP
	strZC_PAGEBAR_COUNT_WAP=Request.Form("edtZC_PAGEBAR_COUNT_WAP")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_PAGEBAR_COUNT_WAP",strZC_PAGEBAR_COUNT_WAP)

	Dim strZC_SINGLE_SIZE_WAP
	strZC_SINGLE_SIZE_WAP=Request.Form("edtZC_SINGLE_SIZE_WAP")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_SINGLE_SIZE_WAP",strZC_SINGLE_SIZE_WAP)

	Dim strZC_SINGLE_PAGEBAR_COUNT_WAP
	strZC_SINGLE_PAGEBAR_COUNT_WAP=Request.Form("edtZC_SINGLE_PAGEBAR_COUNT_WAP")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_SINGLE_PAGEBAR_COUNT_WAP",strZC_SINGLE_PAGEBAR_COUNT_WAP)

	Dim strZC_COMMENT_PAGEBAR_COUNT_WAP
	strZC_COMMENT_PAGEBAR_COUNT_WAP=Request.Form("edtZC_COMMENT_PAGEBAR_COUNT_WAP")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_COMMENT_PAGEBAR_COUNT_WAP",strZC_COMMENT_PAGEBAR_COUNT_WAP) 

	Dim strZC_FILENAME_WAP
	strZC_FILENAME_WAP=Request.Form("edtZC_FILENAME_WAP")
	Call SaveValueForSetting(strContent,True,"String","ZC_FILENAME_WAP",strZC_FILENAME_WAP)




	Dim strZC_IMAGE_WIDTH
	strZC_IMAGE_WIDTH=Request.Form("edtZC_IMAGE_WIDTH")
	Call SaveValueForSetting(strContent,True,"Numeric","ZC_IMAGE_WIDTH",strZC_IMAGE_WIDTH)
	If UCase(strZC_IMAGE_WIDTH)<>UCase(CStr(ZC_IMAGE_WIDTH)) Then Call SetBlogHint(Empty,Empty,True)

	Dim strZC_RSS_EXPORT_WHOLE
	strZC_RSS_EXPORT_WHOLE=Request.Form("edtZC_RSS_EXPORT_WHOLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_RSS_EXPORT_WHOLE",strZC_RSS_EXPORT_WHOLE)
	If UCase(strZC_RSS_EXPORT_WHOLE)<>UCase(CStr(ZC_RSS_EXPORT_WHOLE)) Then Call SetBlogHint(Empty,True,Empty)

	Dim strZC_COMMENT_TURNOFF
	strZC_COMMENT_TURNOFF=Request.Form("edtZC_COMMENT_TURNOFF")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_COMMENT_TURNOFF",strZC_COMMENT_TURNOFF)

	Dim strZC_TRACKBACK_TURNOFF
	strZC_TRACKBACK_TURNOFF=Request.Form("edtZC_TRACKBACK_TURNOFF")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_TRACKBACK_TURNOFF",strZC_TRACKBACK_TURNOFF)

	Dim strZC_GUEST_REVERT_COMMENT_ENABLE
	strZC_GUEST_REVERT_COMMENT_ENABLE=Request.Form("edtZC_GUEST_REVERT_COMMENT_ENABLE")
	Call SaveValueForSetting(strContent,True,"Boolean","ZC_GUEST_REVERT_COMMENT_ENABLE",strZC_GUEST_REVERT_COMMENT_ENABLE)

'	Dim str<#>
'	str<#>=Request.Form("edt<#>")
'	Call SaveValueForSetting(strContent,True,"Boolean","<#>",str<#>) 

	Call SaveToFile(BlogPath & "/c_option.asp",strContent,"utf-8",False)


	SaveSetting=True

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Post Tag
'*********************************************************
Function PostTag()

	Dim objTag
	Set objTag=New TTag
	objTag.ID=Request.Form("edtID")
	objTag.Name=Request.Form("edtName")
	objTag.Order=Request.Form("edtOrder")
	objTag.Intro=Request.Form("edtIntro")
	If objTag.Post Then PostTag=True
	Set objTag=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Del Tag
'*********************************************************
Function DelTag(intID)

	Dim objTag
	Set objTag=New TTag
	objTag.ID=intID
	If objTag.Del Then DelTag=True
	Set objTag=Nothing

End Function
'*********************************************************




'/////////////////////////////////////////////////////////////////////////////////////////
'*********************************************************
' 目的：    Post KeyWord
'*********************************************************
Function PostKeyWord()

	Dim objKeyWord
	Set objKeyWord=New TKeyWord
	objKeyWord.ID=Request.Form("edtID")
	objKeyWord.Name=Request.Form("edtName")
	objKeyWord.Url=Request.Form("edtUrl")
	objKeyWord.Intro=Request.Form("edtIntro")
	If objKeyWord.Post Then PostKeyWord=True
	Set objKeyWord=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    Del Tag
'*********************************************************
Function DelKeyWord(intID)

	Dim objKeyWord
	Set objKeyWord=New TKeyWord
	objKeyWord.ID=intID
	If objKeyWord.Del Then DelKeyWord=True
	Set objKeyWord=Nothing

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function PostSiteFile(tpath)

	Dim txaContent
	txaContent=Request.Form("txaContent")

	If IsEmpty(txaContent) Then txaContent=Null

	If Not IsNull(tpath) Then

		If Not IsNull(txaContent) Then

			Call SaveToFile(BlogPath & tpath,txaContent,"utf-8",False)

			PostSiteFile=True

		End IF

	End If


End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function DelSiteFile(tpath)

	Dim Fso
	Set Fso = Createobject("Scripting.Filesystemobject")
	If Fso.FileExists(BlogPath & tpath) Then
		Fso.Deletefile(BlogPath & tpath)
		Set Fso = Nothing

		DelSiteFile=True

		Exit Function
	Else
		Set Fso = Nothing
		Exit Function
	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function GetRealUrlofTrackBackUrl(intID)

	If IsEmpty(Request.Form("edtCheckOut")) Then

		Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /></head><body>"

		Response.Write "<div id=""divMain""><div class=""Header"">" & ZC_MSG145 & "</div>"
		Response.Write "<div id=""divMain2""><form method=""post""  name=""edit"" id=""edit"" action="""& ZC_BLOG_HOST & "cmd.asp?act=gettburl&id=" & intID &""">"
		Response.Write "<p></p>"
		Response.Write "<p>"& ZC_MSG161 &"</p>"
		Response.Write "<p><img style=""border:1px solid black""  src=""function/c_validcode.asp?name=gettburlvalid"" height=""22"" width=""64"" alt="""" title=""""/>&nbsp;<input type=""text"" id=""edtCheckOut"" name=""edtCheckOut"" size=""30"" />&nbsp;<input class=""button"" type=""submit"" value=""" & ZC_MSG087 & """ id=""btnPost""></p>"
		Response.Write "<p></p>"
		Response.Write "</form></div></div>"
		Response.Write "</body></html>"

	ElseIf CheckVerifyNumber(Request.Form("edtCheckOut"))=True Then


		Response.Write "<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd""><html><head><meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"" /><link rel=""stylesheet"" rev=""stylesheet"" href=""CSS/admin.css"" type=""text/css"" media=""screen"" /></head><body>"

		Response.Write "<div id=""divMain""><div class=""Header"">" & ZC_MSG145 & "</div>"
		Response.Write "<div id=""divMain2""><form method=""post""  name=""edit"" id=""edit"" action="""& ZC_BLOG_HOST & "cmd.asp?act=gettburl&id=" & intID &""">"
		Response.Write "<p></p>"
		Response.Write "<p>" & ZC_BLOG_HOST & "cmd.asp?act=tb&amp;id=" & intID & "&amp;key=" & GetVerifyNumber() &"</p>"
		Response.Write "<p></p>"
		Response.Write "</form></div></div>"
		Response.Write "</body></html>"

		Session("gettburlvalid")=Empty

	Else

		Call ShowError(38)

	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function DelCommentBatch()

	Dim i,j
	Dim s,t
	Dim aryArticle()
	s=Request.Form("edtBatch")
	t=Split(s,",")

	ReDim Preserve aryArticle(UBound(t))
	For j=0 To UBound(t)-1
		aryArticle(j)=0
	Next

	Dim objComment
	Dim objArticle


	For i=0 To UBound(t)-1
		Set objComment=New TComment
		If objComment.LoadInfobyID(t(i)) Then
			If objComment.log_ID>0 Then
				Dim objTestArticle
				Set objTestArticle=New TArticle
				If objTestArticle.LoadInfobyID(objComment.log_ID) Then

					For j=0 To UBound(t)-1
						If aryArticle(j)=0 Then
							aryArticle(j)=objComment.log_ID
						End If
						If aryArticle(j)=objComment.log_ID Then Exit For
					Next

					If Not((objComment.AuthorID=BlogUser.ID) Or (objTestArticle.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
				Else
					Call ShowError(9)
				End If
				Set objTestArticle=Nothing
			Else
				If Not((objComment.log_ID=0) And (CheckRights("GuestBookMng")=True)) Then Exit Function
			End If

			objComment.Del
		End If
		Set objComment=Nothing
	Next


	For j=0 To UBound(t)-1
		If aryArticle(j)>0 Then
			Call BuildArticle(aryArticle(j),False,False)
		End If
	Next

	BlogReBuild_Comments
	BlogReBuild_GuestComments
	DelCommentBatch=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function DelTrackBackBatch()

	Dim i,j
	Dim s,t
	Dim aryArticle()
	s=Request.Form("edtBatch")
	t=Split(s,",")

	ReDim Preserve aryArticle(UBound(t))
	For j=0 To UBound(t)-1
		aryArticle(j)=0
	Next

	Dim objTrackBack
	Dim objArticle

	Set objArticle=New TArticle


	For i=0 To UBound(t)-1
		Set objTrackBack=New TTrackBack
		If objTrackBack.LoadInfobyID(t(i)) Then
			Dim objTestArticle
			Set objTestArticle=New TArticle
			If objTestArticle.LoadInfobyID(objTrackBack.log_ID) Then

				For j=0 To UBound(t)-1
					If aryArticle(j)=0 Then
						aryArticle(j)=objTrackBack.log_ID
					End If
					If aryArticle(j)=objTrackBack.log_ID Then Exit For
				Next

				If Not((objTestArticle.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True)) Then Exit Function
			Else
				Call ShowError(9)
			End If
			Set objTestArticle=Nothing
			objTrackBack.Del
		End If
		Set objTrackBack=Nothing
	Next


	For j=0 To UBound(t)-1
		If aryArticle(j)>0 Then
			Call BuildArticle(aryArticle(j),False,False)
		End If
	Next

	BlogReBuild_TrackBacks
	DelTrackBackBatch=True

End Function
'*********************************************************




'*********************************************************
' 目的：     文件删除
'*********************************************************
Function DelFileBatch()

	Dim i,j
	Dim s,t
	
	s=Request.Form("edtBatch")
	t=Split(s,",")

	Dim objUpLoadFile

	For i=0 To UBound(t)-1
		t(i)=CLng(t(i))
		If t(i)>0 Then
			Set objUpLoadFile=New TUpLoadFile
			If objUpLoadFile.LoadInfoByID(t(i)) Then
				If (objUpLoadFile.AuthorID=BlogUser.ID) Or (CheckRights("Root")=True) Then
					objUpLoadFile.Del
				End If
			Else
				Exit Function
			End If
			Set objUpLoadFile=Nothing
		End If
	Next

	DelFileBatch=True

End Function
'*********************************************************
%>