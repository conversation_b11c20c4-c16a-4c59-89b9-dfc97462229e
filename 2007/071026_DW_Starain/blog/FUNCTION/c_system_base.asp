<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    c_system_base.asp
'// 开始时间:    2005.02.11
'// 最后修改:    
'// 备    注:    
'///////////////////////////////////////////////////////////////////////////////

'定义全局变量
Dim objConn

Dim BlogTitle
Dim BlogUser

Dim BlogPath
BlogPath=Server.MapPath("c_system_base.asp")
BlogPath=Left(BlogPath,Len(BlogPath)-Len("c_system_base.asp"))

Dim StarTime
Dim EndTime
StarTime = Timer()

Dim Categorys()
Dim Users()
Dim Tags()
Dim KeyWords


'*********************************************************
' 目的：    System 初始化
'*********************************************************
Sub System_Initialize()

	On Error Resume Next

	If OpenConnect()=False Then
		If Err.Number<>0 Then Err.Clear
		Call ShowError(4)
	End If

	Set BlogUser =New TUser
	BlogUser.Verify()

	Call GetCategory()
	Call GetUser()
	Call GetTags()
	Call GetKeyWords()

	Call LoadGlobeCache

	If Err.Number<>0 Then Call ShowError(10)

End Sub
'*********************************************************




'*********************************************************
' 目的：    System 释放
'*********************************************************
Sub System_Terminate()

	Call CloseConnect()

End Sub
'*********************************************************




'*********************************************************
' 目的：    数据库连接
'*********************************************************
Function OpenConnect()

	GetReallyDirectory()

	'判定是否为子目录调用
	Dim strDbPath

	strDbPath=BlogPath & ZC_DATABASE_PATH

	Set objConn = Server.CreateObject("ADODB.Connection")
	objConn.Open "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" & strDbPath

	OpenConnect=True

End Function
'*********************************************************




'*********************************************************
' 目的：    DB Disable Connect
'*********************************************************
Function CloseConnect()

	objConn.Close
	Set objConn=Nothing

	CloseConnect=True

End Function
'*********************************************************




'*********************************************************
' 目的：    时间计长
'*********************************************************
Function RunTime()

	EndTime=Timer()
	RunTime = CLng(FormatNumber((EndTime-StarTime)*1000,3))

End Function
'*********************************************************




'*********************************************************
' 目的：    分类读取
'*********************************************************
Function GetCategory()

	Dim i,j,k,l

	Dim aryAllData
	Dim arySingleData()

	Erase Categorys

	Dim objRS

	Set objRS=objConn.Execute("SELECT TOP 1 [cate_ID] FROM [blog_Category] ORDER BY [cate_ID] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		i=objRS("cate_ID")
		ReDim Categorys(i)
	End If
	objRS.Close
	Set objRS=Nothing

	Set objRS=objConn.Execute("SELECT [cate_ID],[cate_Name],[cate_Intro],[cate_Order],[cate_Count] FROM [blog_Category] ORDER BY [cate_ID] ASC")
	If (Not objRS.bof) And (Not objRS.eof) Then

		aryAllData=objRS.GetRows(objRS.RecordCount)
		objRS.Close
		Set objRS=Nothing

		k=UBound(aryAllData,1)
		l=UBound(aryAllData,2)
		For i=0 To l
			Set Categorys(aryAllData(0,i))=New TCategory
			Categorys(aryAllData(0,i)).LoadInfoByArray(Array(aryAllData(0,i),aryAllData(1,i),aryAllData(2,i),aryAllData(3,i),aryAllData(4,i)))
		Next

	End If

	GetCategory=True

End Function
'*********************************************************




'*********************************************************
' 目的：    用户读取
'*********************************************************
Function GetUser()

	Dim i,j,k,l

	Dim aryAllData
	Dim arySingleData()

	Erase Users

	Dim objRS

	Set objRS=objConn.Execute("SELECT TOP 1 [mem_ID] FROM [blog_Member] ORDER BY [mem_ID] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		i=objRS("mem_ID")
		ReDim Users(i)
	End If
	objRS.Close
	Set objRS=Nothing


	Set objRS=objConn.Execute("SELECT [mem_ID],[mem_Name],[mem_Level],[mem_Password],[mem_Email],[mem_HomePage],[mem_PostLogs],[mem_Intro] FROM [blog_Member] ORDER BY [mem_ID] ASC")
	If (Not objRS.bof) And (Not objRS.eof) Then

		aryAllData=objRS.GetRows(objRS.RecordCount)
		objRS.Close
		Set objRS=Nothing

		k=UBound(aryAllData,1)
		l=UBound(aryAllData,2)
		For i=0 To l
			Set Users(aryAllData(0,i))=New TUser
			Users(aryAllData(0,i)).LoadInfoByArray(Array(aryAllData(0,i),aryAllData(1,i),aryAllData(2,i),aryAllData(3,i),aryAllData(4,i),aryAllData(5,i),aryAllData(6,i),aryAllData(7,i)))
		Next

	End If

	Getuser=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Tags读取
'*********************************************************
Function GetTags()

	Dim i,j,k,l

	Dim aryAllData
	Dim arySingleData()

	Erase Tags

	Dim objRS

	Set objRS=objConn.Execute("SELECT TOP 1 [tag_ID] FROM [blog_Tag] ORDER BY [tag_ID] DESC")
	If (Not objRS.bof) And (Not objRS.eof) Then
		i=objRS("tag_ID")
		ReDim Tags(i)
	End If

	Set objRS=objConn.Execute("SELECT [tag_ID],[tag_Name],[tag_Intro],[tag_Order],[tag_Count] FROM [blog_Tag] ORDER BY [tag_ID] ASC")
	If (Not objRS.bof) And (Not objRS.eof) Then

		aryAllData=objRS.GetRows(objRS.RecordCount)
		objRS.Close
		Set objRS=Nothing

		k=UBound(aryAllData,1)
		l=UBound(aryAllData,2)
		For i=0 To l
			Set Tags(aryAllData(0,i))=New TTag
			Tags(aryAllData(0,i)).LoadInfoByArray(Array(aryAllData(0,i),aryAllData(1,i),aryAllData(2,i),aryAllData(3,i),aryAllData(4,i)))
		Next

	End If

	GetTags=True

End Function
'*********************************************************




'*********************************************************
' 目的：    KeyWords读取
'*********************************************************
Function GetKeyWords()

	'Dim objRS
	'Set objRS=objConn.Execute("SELECT [key_ID],[key_Name],[key_URL] FROM [blog_Keyword] ORDER BY [key_ID] ASC")

	'If (Not objRS.bof) And (Not objRS.eof) Then
	'	KeyWords=objRS.GetRows
	'End If

	'objRS.Close
	'Set objRS=Nothing

	GetKeyWords=True

End Function
'*********************************************************




'*********************************************************
' 目的：    读取权限
' 备注:     权限最高为1 最低为5 不是则非法
'           "Root"一定只能为1
'           权限配置方式可以变通
'*********************************************************
Function GetRights(strAction)

	Select Case strAction

		Case "Root"
			GetRights=1
		Case "login"
			GetRights=5
		Case "verify"
			GetRights=5
		Case "logout"
			GetRights=5
		Case "admin"
			GetRights=4
		Case "cmt","CommentRev"
			GetRights=5
		Case "tb"
			GetRights=5
		Case "vrs"
			GetRights=5
		Case "rss"
			GetRights=5
		Case "gettburl"
			GetRights=5
		Case "ArticleMng"
			GetRights=3
		Case "ArticleEdt"
			GetRights=3
		Case "ArticlePst"
			GetRights=3
		Case "ArticleDel"
			GetRights=3
		Case "ArticleBud"
			GetRights=3
		Case "CategoryMng"
			GetRights=2
		Case "CategoryEdt"
			GetRights=2
		Case "CategoryPst"
			GetRights=2
		Case "CategoryDel"
			GetRights=2
		Case "TagMng"
			GetRights=1
		Case "TagEdt"
			GetRights=1
		Case "TagPst"
			GetRights=1
		Case "TagDel"
			GetRights=1
		'Case "KeyWordMng"
		'	GetRights=1
		'Case "KeyWordEdt"
		'	GetRights=1
		'Case "KeyWordPst"
		'	GetRights=1
		'Case "KeyWordDel"
		'	GetRights=1
		Case "GuestBookMng"
			GetRights=2
		Case "CommentMng"
			GetRights=4
		Case "CommentDel"
			GetRights=4
		Case "CommentEdt"
			GetRights=4
		Case "CommentSav"
			GetRights=4
		Case "CommentDelBatch"
			GetRights=4
		Case "TrackBackMng"
			GetRights=3
		Case "TrackBackDel"
			GetRights=3
		Case "TrackBackDelBatch"
			GetRights=3
		Case "TrackBackSnd"
			GetRights=3
		Case "UserMng"
			GetRights=4
		Case "UserEdt"
			GetRights=4
		Case "UserDel"
			GetRights=1
		Case "UserCrt"
			GetRights=1
		Case "BlogReBuild"
			GetRights=3
		Case "FileReBuild"
			GetRights=1
		Case "AskFileReBuild"
			GetRights=1
		Case "FileMng"
			GetRights=2
		Case "FileSnd"
			GetRights=2
		Case "FileUpload"
			GetRights=2
		Case "FileDel"
			GetRights=2
		Case "FileDelBatch"
			GetRights=2
		Case "Search"
			GetRights=5
		'Case "BlogMng"
		'	GetRights=4
		Case "SettingMng"
			GetRights=1
		Case "SettingSav"
			GetRights=1
		Case "PlugInMng"
			GetRights=4
		Case "SiteInfo"
			GetRights=4
		Case "SiteFileMng"
			GetRights=1
		Case "SiteFileEdt"
			GetRights=1
		'Case "SiteFileFnd"
		'	GetRights=1
		Case "SiteFilePst"
			GetRights=1
		Case "SiteFileDel"
			GetRights=1
		Case "Update"
			GetRights=1

		Case Else Call ShowError(1)

	End Select

End Function
'*********************************************************




'*********************************************************
' 目的：    检查权限
'*********************************************************
Function CheckRights(strAction)

	If BlogUser.Level>GetRights(strAction) Then
		CheckRights=False
	Else
		CheckRights=True
	End If

End Function
'*********************************************************




'*********************************************************
' 目的：    Make Calendar
'*********************************************************
Function MakeCalendar(dtmYearMonth)

	Dim strCalendar

	Dim y
	Dim m
	Dim d
	Dim firw
	Dim lasw
	Dim ny
	Dim nm

	Dim i
	Dim j
	Dim k
	Dim b
	Dim s
	Dim t

	Call CheckParameter(dtmYearMonth,"dtm",Date())

	y=year(dtmYearMonth)
	m=month(dtmYearMonth)
	ny=y
	nm=m+1
	If m=12 Then ny=ny+1:nm=1

	firw=Weekday(Cdate(y&"-"&m&"-1"))

	For i=28 to 32
		If IsDate(y&"-"&m&"-"&i) Then
			lasw=Weekday(Cdate(y&"-"&m&"-"&i))
		Else
			Exit For
		End If
	Next

	d=i-1
	k=1

	If firw>5 Then b=42 Else b=35
	If (d=28) And (firw=1) Then b=28
	If (firw>5) And (d<31) Then b=35


'//////////////////////////////////////////////////////////
'	逻辑处理
	Dim aryDateLink(32)
	Dim aryDateID(32)
	Dim aryDateArticle(32)
	Dim objRS

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""
	objRS.Open("select [log_ID],[log_CateID],[log_AuthorID],[log_Level],[log_PostTime],[log_Url],[log_Istop] from [blog_Article] where ([log_Level]>2) And ([log_PostTime] BETWEEN #"&y&"-"&m&"-1# AND #"&ny&"-"&nm&"-1#)")

	If (Not objRS.bof) And (Not objRS.eof) Then
		For i=1 To objRS.RecordCount
			j=CInt(Day(CDate(objRS("log_PostTime"))))
			aryDateLink(j)=True
			aryDateID(j)=objRS("log_ID")
			Set aryDateArticle(j)=New TArticle
			aryDateArticle(j).LoadInfobyArray Array(objRS("log_ID"),"",objRS("log_CateID"),"","","",objRS("log_Level"),objRS("log_AuthorID"),objRS("log_PostTime"),"","","",objRS("log_Url"),"")
			objRS.MoveNext
			If objRS.eof Then Exit For
		Next
	End If
	objRS.Close
	Set objRS=Nothing
'//////////////////////////////////////////////////////////

	s="catalog.asp?date="&y&"-"&(m-1)
	t="catalog.asp?date="&y&"-"&(m+1)
	If m=1 Then s="catalog.asp?date="&(y-1)&"-12"
	If m=12 Then t="catalog.asp?date="&(y+1)&"-1"

	strCalendar=strCalendar & "<div class=""year"&y&" month"&m&""">"
	strCalendar=strCalendar & "<p class=""y""><a href="""&ZC_BLOG_HOST &s&""">&lt;&lt;</a>  <a href="""& ZC_BLOG_HOST &"catalog.asp?date="&y&"-"&m&""">"&y&"-"&m&"</a>  <a href="""&ZC_BLOG_HOST &t&""">&gt;&gt;</a></p>"
	strCalendar=strCalendar & "<p class=""w"">"&ZVA_Week_Abbr(1)&"</p><p class=""w"">"&ZVA_Week_Abbr(2)&"</p><p class=""w"">"&ZVA_Week_Abbr(3)&"</p><p class=""w"">"&ZVA_Week_Abbr(4)&"</p><p class=""w"">"&ZVA_Week_Abbr(5)&"</p><p class=""w"">"&ZVA_Week_Abbr(6)&"</p><p class=""w"">"&ZVA_Week_Abbr(7)&"</p>"
	j=0
	For i=1 to b
		If (j=>firw-1) and (k=<d) Then
			If aryDateLink(k) Then
				strCalendar=strCalendar & "<p id=""pCalendar_"&y&"_"&m&"_"&k&""" class=""yd""><a class=""l"" href="""& ZC_BLOG_HOST &"catalog.asp?date="&Year(aryDateArticle(k).PostTime)&"-"&Month(aryDateArticle(k).PostTime)&"-"&Day(aryDateArticle(k).PostTime)& """>"&(k)&"</a></p>"
			Else
				strCalendar=strCalendar & "<p id=""pCalendar_"&y&"_"&m&"_"&k&""" class=""d"">"&(k)&"</p>"
			End If

			k=k+1
		Else
			strCalendar=strCalendar & "<p class=""nd""></p>"
		End If
		j=j+1
	Next

	strCalendar=strCalendar & "</div>"
	MakeCalendar=strCalendar

End Function
'*********************************************************




'*********************************************************
' 目的：    加载指定目录的文件列表
'*********************************************************
Function LoadIncludeFiles(strDir)

	On Error Resume Next

	Dim aryFileList()
	ReDim aryFileList(0)

	Dim fso, f, f1, fc, s, i
	Set fso = CreateObject("Scripting.FileSystemObject")
	Set f = fso.GetFolder(BlogPath & strDir)
	Set fc = f.Files

	i=0

	For Each f1 in fc
		i=i+1
		ReDim Preserve aryFileList(i)
		aryFileList(i)=f1.name 
	Next

	LoadIncludeFiles=aryFileList

	Err.Clear

End Function
'*********************************************************




'*********************************************************
' 目的：    Load 全局 Cache
'*********************************************************
Function LoadGlobeCache()

	On Error Resume Next

	Dim bolReLoadCache
	Application.Lock
	bolReLoadCache=Application(ZC_BLOG_CLSID & "SIGNAL_RELOADCACHE")
	Application.UnLock

	If IsEmpty(bolReLoadCache)=True Then
		bolReLoadCache=False
	Else
		If bolReLoadCache=False Then 
			Exit Function
		End If
		If bolReLoadCache=True Then 
			bolReLoadCache=False
		End If
	End If

	Call GetReallyDirectory

	Application.Lock
	Application(ZC_BLOG_CLSID & "SIGNAL_RELOADCACHE")=bolReLoadCache
	Application.UnLock

	Dim i,j

	'加载模板
	Dim objStream
	Dim strContent


	'加载WAP
	Application.Lock
	Application(ZC_BLOG_CLSID & "TEMPLATE_WAP_ARTICLE_COMMENT")=LoadFromFile(BlogPath & "WAP/wap_article_comment.html","utf-8")
	Application(ZC_BLOG_CLSID & "TEMPLATE_WAP_ARTICLE-MULTI")=LoadFromFile(BlogPath & "WAP/wap_article-multi.html","utf-8")
	Application(ZC_BLOG_CLSID & "TEMPLATE_WAP_SINGLE")=LoadFromFile(BlogPath & "WAP/wap_single.html","utf-8")
	Application.UnLock


	'读取Template目录下的所有文件并写入Cache
	Dim aryFileList
	Dim aryFileNameTemplate()
	Dim aryFileNameTemplate_Variable()

	aryFileList=LoadIncludeFiles(ZC_TEMPLATE_DIRECTORY)

	If IsArray(aryFileList) Then

		j=UBound(aryFileList)

		ReDim aryFileNameTemplate(j)
		ReDim aryFileNameTemplate_Variable(j)


		For i=1 to j

			aryFileNameTemplate(i)=ZC_TEMPLATE_DIRECTORY & "/" & aryFileList(i)
			aryFileNameTemplate_Variable(i)="TEMPLATE_" & UCase(Left(aryFileList(i),InStr(aryFileList(i),".")-1))
			If InStr(aryFileList(i),".")=0 Then 
				aryFileNameTemplate_Variable(i)="TEMPLATE_" & UCase(aryFileList(i))
			End If

			strContent=""
			strContent=LoadFromFile(BlogPath & "" & aryFileNameTemplate(i),"utf-8")

			Application.Lock
			Application(ZC_BLOG_CLSID & aryFileNameTemplate_Variable(i))=strContent
			Application.UnLock
		Next

	End If

	'加载标签
	Dim a,b,c,d
	Dim t()
	Dim s()

	a=0
	b=17
	c=1
	d=300


	'读取Include目录下的所有文件并写入Cache
	'Dim aryFileList
	Dim aryFileNameInclude()
	Dim aryFileNameInclude_Variable()

	aryFileList=LoadIncludeFiles("INCLUDE")

	If IsArray(aryFileList) Then

		a=UBound(aryFileList)

		ReDim aryFileNameInclude(a)
		ReDim aryFileNameInclude_Variable(a)
		ReDim s(a)
		ReDim Preserve aryTemplateTagsName(a)
		ReDim Preserve aryTemplateTagsValue(a)

		For i=1 to a

			aryFileNameInclude(i)="/INCLUDE/" & aryFileList(i)
			aryFileNameInclude_Variable(i)="CACHE_INCLUDE_" & UCase(Left(aryFileList(i),InStr(aryFileList(i),".")-1))
			If InStr(aryFileList(i),".")=0 Then 
				aryFileNameInclude_Variable(i)="CACHE_INCLUDE_" & UCase(aryFileList(i))
			End If

			s(i)=aryFileNameInclude_Variable(i)

			strContent=""
			strContent=LoadFromFile(BlogPath & "" & aryFileNameInclude(i),"utf-8")
			strContent=Replace(strContent,"<"&"%=ZC_BLOG_HOST%"&">",ZC_BLOG_HOST)

			aryTemplateTagsName(i)=s(i)
			aryTemplateTagsValue(i)=strContent
		Next

	End If

	ReDim Preserve aryTemplateTagsName(a+d)
	ReDim Preserve aryTemplateTagsValue(a+d)
	For j=1 to d
		i=Right("000" & CStr(j),3)
		aryTemplateTagsName(a+j)="ZC_MSG" & i
		Call Execute("aryTemplateTagsValue(a+j)=ZC_MSG" & i)
	Next


	ReDim t(b)
	t(1)="ZC_BLOG_VERSION"
	t(2)="ZC_BLOG_LANGUAGE"
	t(3)="ZC_BLOG_HOST"
	t(4)="ZC_BLOG_TITLE"
	t(5)="ZC_BLOG_SUBTITLE"
	t(6)="ZC_BLOG_NAME"
	t(7)="ZC_BLOG_SUB_NAME"
	t(8)="ZC_BLOG_CSS"
	t(9)="ZC_BLOG_COPYRIGHT"
	t(10)="ZC_BLOG_MASTER"
	t(11)="ZC_CONTENT_MAX"
	t(12)="ZC_EMOTICONS_FILENAME"
	t(13)="ZC_EMOTICONS_FILESIZE"
	t(14)="ZC_GUESTBOOK_CONTENT"
	t(15)="ZC_BLOG_CLSID"
	t(16)="ZC_TIME_ZONE"
	t(17)="ZC_IMAGE_WIDTH"


	ReDim Preserve aryTemplateTagsName(a+d+b)
	ReDim Preserve aryTemplateTagsValue(a+d+b)
	For j=1 to b
		aryTemplateTagsName(a+d+j)=t(j)
		Call Execute("aryTemplateTagsValue(a+d+j)="& t(j))
	Next

	ReDim Preserve aryTemplateTagsName(a+d+b+c)
	ReDim Preserve aryTemplateTagsValue(a+d+b+c)
	aryTemplateTagsName(a+d+b+c)="BLOG_CREATE_TIME"
	aryTemplateTagsValue(a+d+b+c)=Now

	Application.Lock
	Application(ZC_BLOG_CLSID & "TemplateTagsName")=aryTemplateTagsName
	Application(ZC_BLOG_CLSID & "TemplateTagsValue")=aryTemplateTagsValue
	Application.UnLock

	Err.Clear

	LoadGlobeCache=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Clear Cache
'*********************************************************
Function ClearGlobeCache()

	Application.Lock
	Application(ZC_BLOG_CLSID & "SIGNAL_RELOADCACHE")=True

	Application(ZC_BLOG_CLSID & "CACHE_ARTICLE_VIEWCOUNT")=Empty

	Application(ZC_BLOG_CLSID & "TemplateTagsName")=Empty
	Application(ZC_BLOG_CLSID & "TemplateTagsValue")=Empty

	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_COMMENT")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_COMMENTPOST")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_COMMENTPOST-VERIFY")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_TAG")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_TRACKBACK")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE-MULTI")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE-SINGLE")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE-GUESTBOOK")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_PAGEBAR")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_NVABAR_L")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_NVABAR_R")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE_MUTUALITY")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_B_ARTICLE-ISTOP")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_CATALOG")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_DEFAULT")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_SEARCH")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_SINGLE")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_TAGS")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_WAP_ARTICLE_COMMENT")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_WAP_ARTICLE-MULTI")=Empty
	Application(ZC_BLOG_CLSID & "TEMPLATE_WAP_SINGLE")=Empty

	Application.UnLock

	ClearGlobeCache=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Parse Tag 并格式化
'*********************************************************
Function ParseTag(strTag)

	Dim s
	Dim t
	Dim i
	Dim Tag
	Dim b
	Dim objTag

	strTag=Trim(strTag)
	strTag=TransferHTML(strTag,"[normalname]")
	t=Split(strTag," ")

	GetTags()
	For i=LBound(t) To UBound(t)

		b=False

		For Each Tag in Tags
			If IsObject(Tag) Then
				If UCase(Tag.Name)=UCase(t(i)) Then
					b=True
				End If
			End If
		Next

		If b=False Then
			Set objTag=New TTag
			objTag.ID=0
			objTag.Name=t(i)
			objTag.Order=0
			objTag.Intro=""
			objTag.Post
			Set objTag=Nothing
		End If

	Next

	GetTags()
	For i=LBound(t) To UBound(t)
		For Each Tag in Tags
			If IsObject(Tag) Then
				If UCase(Tag.Name)=UCase(t(i)) Then
					t(i)="{"&Tag.ID&"}"
				End If
			End If
		Next
	Next

	s=Join(t)
	s=Replace(s," ","")

	ParseTag=s

End Function
'*********************************************************




'*********************************************************
' 目的：    得到实际上的真实目录
'*********************************************************
Function GetReallyDirectory()

	On Error Resume Next

	Dim fso
	Set fso = CreateObject("Scripting.FileSystemObject")
	If fso.FileExists(BlogPath & "\" & ZC_DATABASE_PATH) Then
		BlogPath=BlogPath
	ElseIf fso.FileExists(BlogPath & "\..\" & ZC_DATABASE_PATH) Then
		BlogPath=BlogPath & "..\"
	ElseIf fso.FileExists(BlogPath & "\..\..\" & ZC_DATABASE_PATH) Then
		BlogPath=BlogPath & "..\..\"
	ElseIf fso.FileExists(BlogPath & "\..\..\..\" & ZC_DATABASE_PATH) Then
		BlogPath=BlogPath & "..\..\..\"
	ElseIf fso.FileExists(BlogPath & "\..\..\..\..\" & ZC_DATABASE_PATH) Then
		BlogPath=BlogPath & "..\..\..\..\"
	ElseIf fso.FileExists(BlogPath & "\..\..\..\..\..\" & ZC_DATABASE_PATH) Then
		BlogPath=BlogPath & "..\..\..\..\..\"
	End If
	Set fso=Nothing

	GetReallyDirectory=True

	Err.Clear

End Function
'*********************************************************




'*********************************************************
' 目的：    设置提示标志
'*********************************************************
Function SetBlogHint(bolOperateSuccess,bolRebuildIndex,bolRebuildFiles)

	Call SetBlogHintWithCLSID(bolOperateSuccess,bolRebuildIndex,bolRebuildFiles,ZC_BLOG_CLSID)

End Function
'*********************************************************




'*********************************************************
' 目的：    设置提示标志withCLSID
'*********************************************************
Function SetBlogHintWithCLSID(bolOperateSuccess,bolRebuildIndex,bolRebuildFiles,newCLSID)

	Application.Lock

	Application(newCLSID & "SIGNAL_OPERATESUCCESS")=bolOperateSuccess

	If IsEmpty(bolRebuildIndex)=False Then
		Application(newCLSID & "SIGNAL_REBUILDINDEX")=bolRebuildIndex
	End If

	If IsEmpty(bolRebuildFiles)=False Then
		Application(newCLSID & "SIGNAL_REBUILDFILES")=bolRebuildFiles
	End If

	Application.UnLock

End Function
'*********************************************************




'*********************************************************
' 目的：    输出提示
'*********************************************************
Function GetBlogHint()

	Dim bolOperateSuccess,bolRebuildIndex,bolRebuildFiles

	Application.Lock
	bolOperateSuccess=Application(ZC_BLOG_CLSID & "SIGNAL_OPERATESUCCESS")
	bolRebuildIndex=Application(ZC_BLOG_CLSID & "SIGNAL_REBUILDINDEX")
	bolRebuildFiles=Application(ZC_BLOG_CLSID & "SIGNAL_REBUILDFILES")
	Application.UnLock


	If IsEmpty(bolOperateSuccess)=False Then

		If bolOperateSuccess=True Then
			Response.Write "<p class='hint hint_green'><font color='green'>" & ZC_MSG266 & "</font></p>"
		End If

		If bolOperateSuccess=False Then
			Response.Write "<p class='hint hint_red'><font color='red'>" & ZC_MSG267 & "</font></p>"
		End If

		Application.Lock
		Application(ZC_BLOG_CLSID & "SIGNAL_OPERATESUCCESS")=Empty
		Application.UnLock

	End If


	If IsEmpty(bolRebuildIndex)=False Then

		If bolRebuildIndex=True Then
			Response.Write "<p class='hint hint_blue'><font color='blue'>" & ZC_MSG268 & "</font></p>"
		End If

	End If

	If IsEmpty(bolRebuildFiles)=False Then

		If bolRebuildFiles=True Then
			Response.Write "<p class='hint hint_blue'><font color='blue'>" & ZC_MSG269 & "</font></p>"
		End If

	End If


End Function
'*********************************************************




'*********************************************************
' 目的：    解析ZC_CUSTOM_DIRECTORY_REGEX
'*********************************************************
Function ParseCustomDirectory(strRegex,strPost,strCategory,strUser,strYear,strMonth,strDay,strID,strAlias)

	On Error Resume Next

	Dim s
	s=strRegex

	s=Replace(s,"{%post%}",strPost)
	s=Replace(s,"{%category%}",strCategory)
	s=Replace(s,"{%user%}",strUser)
	s=Replace(s,"{%year%}",strYear)
	s=Replace(s,"{%month%}",Right("0" & strMonth,2))
	s=Replace(s,"{%day%}",Right("0" & strDay,2))
	s=Replace(s,"{%id%}",strID)
	s=Replace(s,"{%alias%}",strAlias)

	ParseCustomDirectory=s

	Err.Clear

End Function
'*********************************************************




'*********************************************************
' 目的：    按照CustomDirectory指示创建相应的目录
'*********************************************************
Sub CreatDirectoryByCustomDirectory(strCustomDirectory)

	On Error Resume Next

	Dim s
	Dim t
	Dim i

	Dim fso
	Set fso = CreateObject("Scripting.FileSystemObject")

	s=BlogPath

	t=Split(strCustomDirectory,"/")
	For i=LBound(t) To UBound(t)
		If (IsEmpty(t(i))=False) And (t(i)<>"") Then
			s=s & t(i) & "\"
			If (fso.FolderExists(fldr)=False) Then
				Call fso.CreateFolder(s)
			End If
		End If
	Next

	Set fso = Nothing

	Err.Clear

End Sub
'*********************************************************
%>