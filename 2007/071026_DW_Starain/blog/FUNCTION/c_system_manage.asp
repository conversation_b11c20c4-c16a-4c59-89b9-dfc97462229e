<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    c_system_manage.asp
'// 开始时间:    2005.02.11
'// 最后修改:    
'// 备    注:    
'///////////////////////////////////////////////////////////////////////////////




'*********************************************************
' 目的：    Manager Articles
'*********************************************************
Function ExportArticleList(intPage,intCate,intLevel,intTitle)

	Dim i
	Dim objRS
	Dim strSQL
	Dim strPage

	Call CheckParameter(intPage,"int",1)
	Call CheckParameter(intCate,"int",-1)
	Call CheckParameter(intLevel,"int",-1)
	Call CheckParameter(intTitle,"sql",-1)
	intTitle=vbsunescape(intTitle)
	intTitle=FilterSQL(intTitle)

	Response.Write "<div class=""Header"">" & ZC_MSG067 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" method=""post"" enctype=""application/x-www-form-urlencoded"" action=""../admin/admin.asp?act=ArticleMng"">"
	Response.Write "<p>"&ZC_MSG158&":</p><p>"

	Response.Write ZC_MSG012&" <select class=""edit"" size=""1"" id=""cate"" name=""cate"" style=""width:100px;"" ><option value=""-1"">"&ZC_MSG157&"</option> "
	Dim Category
	For Each Category in Categorys
		If IsObject(Category) Then
			Response.Write "<option value="""&Category.ID&""" "
			Response.Write ">"&TransferHTML(Category.Name,"[html-format]")&"</option>"
		End If
	Next
	Response.Write "</select> "

	Response.Write ZC_MSG061&" <select class=""edit"" size=""1"" id=""level"" name=""level"" style=""width:80px;"" ><option value=""-1"">"&ZC_MSG157&"</option> "

	For i=LBound(ZVA_Article_Level_Name)+1 to Ubound(ZVA_Article_Level_Name)
			Response.Write "<option value="""&i&""" "
			Response.Write ">"&ZVA_Article_Level_Name(i)&"</option>"
	Next
	Response.Write "</select>"

	Response.Write " "&ZC_MSG224&" <input id=""title"" name=""title"" style=""width:150px;"" type=""text"" value="""" /> "
	Response.Write "<input type=""submit"" class=""button"" value="""&ZC_MSG087&""">"
	
	Response.Write "</p></form>"



	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	strSQL="WHERE ([log_Level]>0) AND (true=true) "

	If CheckRights("Root")=False Then strSQL= strSQL & "AND [log_AuthorID] = " & BlogUser.ID

	If intCate<>-1 Then
		strSQL= strSQL & " AND [log_CateID] = " & intCate
	End If

	If intLevel<>-1 Then
		strSQL= strSQL & " AND [log_Level] = " & intLevel
	End If

	If intTitle<>"-1" Then
		strSQL = strSQL & "AND ( ([log_Title] LIKE '%" & intTitle & "%') OR ([log_Intro] LIKE '%" & intTitle & "%') OR ([log_Content] LIKE '%" & intTitle & "%') )"
	End If

	Response.Write "<table border=""1"" width=""100%"" cellspacing=""1"" cellpadding=""1"">"
	Response.Write "<tr><td>"& ZC_MSG076 &"</td><td>"& ZC_MSG012 &"</td><td>"& ZC_MSG003 &"</td><td>"& ZC_MSG075 &"</td><td>"& ZC_MSG060 &"</td><td></td><td></td></tr>"

	objRS.Open("SELECT * FROM [blog_Article] "& strSQL &" ORDER BY [log_PostTime] DESC")
	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	If (Not objRS.bof) And (Not objRS.eof) Then

		For i=1 to objRS.PageSize

			Response.Write "<tr>"

			Response.Write "<td>" & objRS("log_ID") & "</td>"

			Dim Cate
			For Each Cate in Categorys
				If IsObject(Cate) Then
					If Cate.ID=objRS("log_CateID") Then
						Response.Write "<td>" & Left(Cate.Name,6) & "</td>"
					End If
				End If
			Next

			Dim User
			For Each User in Users
				If IsObject(User) Then
					If User.ID=objRS("log_AuthorID") Then
						Response.Write "<td>" & User.Name & "</td>"
					End If
				End If
			Next

			'Response.Write "<td>" & ZVA_Article_Level_Name(objRS("log_Level")) & "</td>"
			Response.Write "<td>" & FormatDateTime(objRS("log_PostTime"),vbShortDate) & "</td>"
			Response.Write "<td><a href=""../view.asp?id=" & objRS("log_ID") & """ target=""_blank"">" & Left(objRS("log_Title"),14) & "</a></td>"
			Response.Write "<td align=""center""><a href=""../cmd.asp?act=ArticleEdt&type="& ZC_BLOG_WEBEDIT &"&id=" & objRS("log_ID") & """>[" & ZC_MSG100 & "]</a>"
			Response.Write "&nbsp;&nbsp;<a href=""../cmd.asp?act=ArticleEdt&id=" & objRS("log_ID") & """>[" & ZC_MSG101 & "]</a></td>"
			Response.Write "<td align=""center""><a onclick='return window.confirm("""& ZC_MSG058 &""");' href=""../cmd.asp?act=ArticleDel&id=" & objRS("log_ID") & """>[" & ZC_MSG063 & "]</a></td>"
			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

	End If

	Response.Write "</table>"

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='../admin/admin.asp?act=ArticleMng&amp;page="& i &"&cate="&ReQuest("cate")&"&level="&ReQuest("level")&"&title="&Escape(ReQuest("title"))&"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next
	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"

	objRS.Close
	Set objRS=Nothing

	ExportArticleList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager Categorys
'*********************************************************
Function ExportCategoryList(intPage)

	Dim i
	Dim objRS
	Dim strPage

	Response.Write "<div class=""Header"">" & ZC_MSG066 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" method=""post"" action="""">"
	Response.Write "<p>"& ZC_MSG122 &": </p>"
	Response.Write "<p><a href=""../cmd.asp?act=CategoryEdt"">["& ZC_MSG077 &"]</a></p>"
	Response.Write "</form>"

	Call CheckParameter(intPage,"int",1)

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	objRS.Open("SELECT * FROM [blog_Category] ORDER BY [cate_Order] ASC,[cate_ID] ASC")

	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	Response.Write "<table border=""1"" width=""100%"" cellspacing=""1"" cellpadding=""1"">"
	Response.Write "<tr><td>"& ZC_MSG076 &"</td><td>"& ZC_MSG001 &"</td><td>"& ZC_MSG079 &"</td><td>"& ZC_MSG016 &"</td><td></td><td></td></tr>"

	If (Not objRS.bof) And (Not objRS.eof) Then

		For i=1 to objRS.PageSize

			Response.Write "<tr>"
			Response.Write "<td>" & objRS("cate_ID") & "</td>"
			Response.Write "<td><a href=""../catalog.asp?cate="& objRS("cate_ID") &"""  target=""_blank"">" & objRS("cate_Name") & "</a></td>"
			Response.Write "<td>" & objRS("cate_Order") & "</td>"
			Response.Write "<td>" & objRS("cate_Intro") & "</td>"
			Response.Write "<td align=""center""><a href=""../cmd.asp?act=CategoryEdt&id="& objRS("cate_ID") &""">["& ZC_MSG078 &"]</a></td>"
			Response.Write "<td align=""center""><a onclick='return window.confirm("""& ZC_MSG058 &""");' href=""../cmd.asp?act=CategoryDel&amp;id="& objRS("cate_ID") &""">["& ZC_MSG063 &"]</a></td>"
			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

	End If

	Response.Write "</table>"

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='admin.asp?act=CategoryMng&amp;page="& i &"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next
	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"

	objRS.Close
	Set objRS=Nothing

	ExportCategoryList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager Comments
'*********************************************************
Function ExportCommentList(intPage,intContent)

	Dim i
	Dim objRS
	Dim strSQL
	Dim strPage

	Call CheckParameter(intPage,"int",1)
	intContent=FilterSQL(intContent)

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	
	If CheckRights("GuestBookMng")=True Then 
	strSQL=strSQL & " WHERE ( ([log_ID]>0) OR ([log_ID] = 0 ) ) "
	Else
	strSQL=strSQL&" WHERE  ([log_ID]>0) "
	End If
	
	If CheckRights("Root")=False Then strSQL=strSQL & "AND( ([comm_AuthorID] = " & BlogUser.ID & " ) OR ((SELECT [log_AuthorID] FROM [blog_Article] WHERE [blog_Article].[log_ID]=[blog_Comment].[log_ID])=" & BlogUser.ID & " )) "

	If Trim(intContent)<>"" Then strSQL=strSQL & " AND ( ([comm_Author] LIKE '%" & intContent & "%') OR ([comm_Content] LIKE '%" & intContent & "%') OR ([comm_HomePage] LIKE '%" & intContent & "%') ) "

	Response.Write "<div class=""Header"">" & ZC_MSG068 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" method=""post"" enctype=""application/x-www-form-urlencoded"" action=""../admin/admin.asp?act=CommentMng"">"
	Response.Write "<p>"&ZC_MSG287&":</p><p>"

	Response.Write " "&ZC_MSG224&" <input id=""intContent"" name=""intContent"" style=""width:150px;"" type=""text"" value="""" /> "
	Response.Write "<input type=""submit"" class=""button"" value="""&ZC_MSG087&""">"
	
	Response.Write "</p></form>"

	Response.Write "<table border=""1"" width=""100%"" cellspacing=""1"" cellpadding=""1"">"
	Response.Write "<tr><td width='5%'>"& ZC_MSG076 &"</td><td width='14%'>"& ZC_MSG001 &"</td><td>"& ZC_MSG055 &"</td><td width='12%'>"& ZC_MSG080 &"</td><td width='15%'>"& ZC_MSG075 &"</td><td width='6%'></td><td width='6%'></td><td width='5%'  align='center'><a href='' onclick='BatchSelectAll();return false'>"& ZC_MSG229 &"</a></td></tr>"'

	objRS.Open("SELECT * FROM [blog_Comment] "& strSQL &" ORDER BY [comm_ID] DESC")


	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	If (Not objRS.bof) And (Not objRS.eof) Then

		For i=1 to objRS.PageSize

			Response.Write "<tr>"
			Response.Write "<td>" & objRS("comm_ID") & "</td>"
			If Trim(objRS("comm_Email"))="" Then
			Response.Write "<td>"& objRS("comm_Author") & "</td>"
			Else
			Response.Write "<td><a href=""mailto:"& objRS("comm_Email") &""">" & objRS("comm_Author") & "</a></td>"
			End If

			If objRS("log_ID")=0 Then
				Response.Write "<td><a href=""../guestbook.asp#cmt" & objRS("comm_ID") & """ target=""_blank"">" & Left(objRS("comm_Content"),20) & "...</a></td>"
			Else
				Response.Write "<td><a href=""../view.asp?id=" & objRS("log_ID") & "#cmt" & objRS("comm_ID") & """ target=""_blank"" title="""&TransferHTML(TransferHTML(UBBCode(objRS("comm_Content"),"[face][link][autolink][font][code][image][typeset][media][flash][key][upload]"),"[html-japan][upload]"),"[nohtml]")&""">" & Left(objRS("comm_Content"),20) & "...</a></td>"
			End If
			Response.Write "<td>" & objRS("comm_IP") & "</td>"
			Response.Write "<td>" & objRS("comm_PostTime") & "</td>"
			Response.Write "<td align=""center""><a href=""../cmd.asp?act=CommentEdt&id=" & objRS("comm_ID") & "&log_id="& objRS("log_ID") &""">["& ZC_MSG078 &"]</a></td>"
			Response.Write "<td align=""center""><a href=""../cmd.asp?act=CommentDel&id=" & objRS("comm_ID") & "&log_id="& objRS("log_ID") &""" onclick='return window.confirm("""& ZC_MSG058 &""");'>["& ZC_MSG063 &"]</a></td>"
			Response.Write "<td align=""center"" ><input type=""checkbox"" name=""edtDel"" id=""edtDel"" value="""&objRS("comm_ID")&"""/></td>"
			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

	End If

	Response.Write "</table>"

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='admin.asp?act=CommentMng&amp;page="& i &"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next
	Response.Write "<br/><form id=""frmBatch"" method=""post"" action=""../cmd.asp?act=CommentDelBatch""><p><input type=""hidden"" id=""edtBatch"" name=""edtBatch"" value=""""/><input class=""button"" type=""submit"" onclick='BatchDeleteAll(""edtBatch"");if(document.getElementById(""edtBatch"").value){return window.confirm("""& ZC_MSG058 &""");}else{return false}' value="""&ZC_MSG228&""" id=""btnPost""/></p><form><br/>" & vbCrlf

	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"
	
	objRS.Close
	Set objRS=Nothing

	ExportCommentList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager TrackBacks
'*********************************************************
Function ExportTrackBackList(intPage)

	Dim i
	Dim objRS
	Dim strSQL
	Dim strPage

	Call CheckParameter(intPage,"int",1)

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	strSQL="WHERE ([log_ID]>0) "
	If CheckRights("Root")=False Then strSQL=strSQL & "AND( (SELECT [log_AuthorID] FROM [blog_Article] WHERE [blog_Article].[log_ID] =[blog_TrackBack].[log_ID] ) =" & BlogUser.ID & ")"

	Response.Write "<div class=""Header"">" & ZC_MSG069 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<table border=""1"" width=""100%"" cellspacing=""1"" cellpadding=""1"">"
	Response.Write "<tr><td>"& ZC_MSG048 & ZC_MSG076 &"</td><td>"& ZC_MSG014 &"</td><td>"& ZC_MSG060 &"</td><td>"& ZC_MSG055 &"</td><td></td><td width='5%'  align='center'><a href='' onclick='BatchSelectAll();return false'>"& ZC_MSG229 &"</a></td></tr>"


	objRS.Open("SELECT * FROM [blog_TrackBack] "& strSQL &" ORDER BY [tb_ID] DESC")
	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	If (Not objRS.bof) And (Not objRS.eof) Then

		For i=1 to objRS.PageSize

			Response.Write "<tr>"
			Response.Write "<td>" & objRS("log_ID") & "</td>"
			Response.Write "<td><a title="""& objRS("tb_Title") &""" target=""_blank"" href="""&objRS("tb_Url")&""">" & Left(objRS("tb_Blog"),14) & "</a></td>"
			Response.Write "<td><a href=""../view.asp?id=" & objRS("log_ID") & "#tb" & objRS("tb_ID") & """ target=""_blank"">" & Left(objRS("tb_Title"),12) & "</a></td>"
			Response.Write "<td>" & Left(objRS("tb_Excerpt"),18) & "</td>"
			Response.Write "<td align=""center""><a href=""../cmd.asp?act=TrackBackDel&id=" & objRS("tb_ID") & "&log_id="& objRS("log_ID") &""" onclick='return window.confirm(""" & ZC_MSG058 & """);'>["& ZC_MSG063 &"]</a></td>"
			Response.Write "<td align=""center"" ><input type=""checkbox"" name=""edtDel"" id=""edtDel"" value="""&objRS("tb_ID")&"""/></td>"
			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

	End If

	Response.Write "</table>"

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='admin.asp?act=TrackBackMng&amp;page="& i &"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next

	Response.Write "<br/><form id=""frmBatch"" method=""post"" action=""../cmd.asp?act=TrackBackDelBatch""><p><input type=""hidden"" id=""edtBatch"" name=""edtBatch"" value=""""/><input class=""button"" type=""submit"" onclick='BatchDeleteAll(""edtBatch"");if(document.getElementById(""edtBatch"").value){return window.confirm("""& ZC_MSG058 &""");}else{return false}' value="""&ZC_MSG228&""" id=""btnPost""/></p><form><br/>" & vbCrlf


	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"

	objRS.Close
	Set objRS=Nothing

	ExportTrackBackList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager Users
'*********************************************************
Function ExportUserList(intPage)

	Dim i
	Dim objRS
	Dim strSQL
	Dim strPage

	Call CheckParameter(intPage,"int",1)

	Response.Write "<div class=""Header"">" & ZC_MSG070 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	If CheckRights("UserCrt")=True Then
		Response.Write "<form id=""edit"" method=""post"" action="""">"
		Response.Write "<p>"& ZC_MSG123 &": </p>"
		Response.Write "<p><a href=""edit_user.asp"">["& ZC_MSG127 &"]</a></p>"
		Response.Write "</form>"
	End If

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	If CheckRights("Root")=False Then strSQL="WHERE [mem_ID] = " & BlogUser.ID

	objRS.Open("SELECT * FROM [blog_Member] " & strSQL & " ORDER BY [mem_ID] ASC")

	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	If (Not objRS.bof) And (Not objRS.eof) Then

		Response.Write "<table border=""1"" width=""100%"" cellspacing=""1"" cellpadding=""1"">"
		Response.Write "<tr><td>"& ZC_MSG076 &"</td><td>"& ZC_MSG079 &"</td><td>"& ZC_MSG001 &"</td><td>"& ZC_MSG082 &"</td><td>"& ZC_MSG124 &"</td><td></td><td></td></tr>"

		For i=1 to objRS.PageSize

			Response.Write "<tr>"
			Response.Write "<td>" & objRS("mem_ID") & "</td>"
			Response.Write "<td>" & ZVA_User_Level_Name(objRS("mem_Level")) & "</td>"
			Response.Write "<td><a href=""../catalog.asp?auth="& objRS("mem_ID") &"""  target=""_blank"">" & objRS("mem_Name") & "</a></td>"

			Response.Write "<td>" & objRS("mem_PostLogs") & "</td>"
			Response.Write "<td>" & objRS("mem_PostComms") & "</td>"

			Response.Write "<td align=""center""><a href=""edit_user.asp?id="& objRS("mem_ID") &""">["& ZC_MSG078 &"]</a></td>"
			Response.Write "<td align=""center""><a onclick='return window.confirm("""& ZC_MSG058 &""");' href=""../cmd.asp?act=UserDel&amp;id="& objRS("mem_ID") &""">["& ZC_MSG063 &"]</a></td>"

			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

		Response.Write "</table>"

	End If

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='admin.asp?act=UserMng&amp;page="& i &"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next
	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"

	objRS.Close
	Set objRS=Nothing

	ExportUserList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager Files
'*********************************************************
Function ExportFileList(intPage)

	Dim i
	Dim objRS
	Dim strSQL
	Dim strPage

	Call CheckParameter(intPage,"int",1)

	Response.Write "<div class=""Header"">" & ZC_MSG071 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form border=""1"" name=""edit"" id=""edit"" method=""post"" enctype=""multipart/form-data"" action=""../cmd.asp?act=FileUpload"">"
	Response.Write "<p>"& ZC_MSG108 &": </p>"
	Response.Write "<p><input type=""checkbox"" onclick='if(this.checked==true){document.getElementById(""edit"").action=document.getElementById(""edit"").action+""&autoname=1"";}else{document.getElementById(""edit"").action=""../cmd.asp?act=FileUpload"";}' id=""chkAutoName"" id=""chkAutoName""/><label for=""chkAutoName"">"& ZC_MSG131 &"</label></p>"
	Response.Write "<p><input type=""file"" id=""edtFileLoad"" name=""edtFileLoad"" size=""20"">  <input type=""submit"" class=""button"" value="""& ZC_MSG087 &""" name=""B1"" onclick='document.getElementById(""edit"").action=document.getElementById(""edit"").action+""&filename=""+escape(edtFileLoad.value)' /> <input class=""button"" type=""reset"" value="""& ZC_MSG088 &""" name=""B2"" /></p></form>"

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	If CheckRights("Root")=False Then strSQL="WHERE [ul_AuthorID] = " & BlogUser.ID

	Response.Write "<table border='1' width='100%' cellspacing=""1"" cellpadding=""1"">"
	Response.Write "<tr><td width='10%'>"& ZC_MSG076 &"</td><td width='10%'>"& ZC_MSG003 &"</td><td width='30%'>"& ZC_MSG001 &"</td><td width='15%'>"& ZC_MSG041 &"</td><td width='15%'>"& ZC_MSG075 &"</td><td width='7%'></td><td width='5%'  align='center'><a href='' onclick='BatchSelectAll();return false'>"& ZC_MSG229 &"</a></td></tr>"

	objRS.Open("SELECT * FROM [blog_UpLoad] " & strSQL & " ORDER BY [ul_PostTime] DESC")
	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	If (Not objRS.bof) And (Not objRS.eof) Then

		For i=1 to objRS.PageSize

			Response.Write "<tr><td>"&objRS("ul_ID")&"</td>"

			Dim User:For Each User in Users:If IsObject(User) Then:If User.ID=objRS("ul_AuthorID") Then:Response.Write "<td>" & User.Name & "</td>":End If:End If:Next

			Response.Write "<td><a href='../"& ZC_UPLOAD_DIRECTORY &"/"&objRS("ul_FileName")&"' target='_blank'>"&objRS("ul_FileName")&"</a></td>"
			Response.Write "<td>"&objRS("ul_FileSize")&"</td><td>"&objRS("ul_PostTime")&"</td>"
			Response.Write "<td align=""center""><a href='../cmd.asp?act=FileDel&amp;id="&Server.URLEncode(objRS("ul_ID"))&"' onclick='return window.confirm("""& ZC_MSG058 &""");'>["& ZC_MSG063 &"]</a></td>"
			Response.Write "<td align=""center"" ><input type=""checkbox"" name=""edtDel"" id=""edtDel"" value="""&objRS("ul_ID")&"""/></td>"
			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

	End If

	Response.Write "</table>"

	Response.Write "<br/><form id=""frmBatch"" method=""post"" action=""../cmd.asp?act=FileDelBatch""><p><input type=""hidden"" id=""edtBatch"" name=""edtBatch"" value=""""/><input class=""button"" type=""submit"" onclick='BatchDeleteAll(""edtBatch"");if(document.getElementById(""edtBatch"").value){return window.confirm("""& ZC_MSG058 &""");}else{return false}' value="""&ZC_MSG228&""" id=""btnPost""/></p><form><br/>" & vbCrlf

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='admin.asp?act=FileMng&amp;page="& i &"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next
	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"

	objRS.Close
	Set objRS=Nothing

	ExportFileList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manage Setting
'*********************************************************
Function ExportManageList()

	ExportManageList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager KeyWord
'*********************************************************
Function ExportKeyWordList(intPage)

	ExportKeyWordList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager Tag
'*********************************************************
Function ExportTagList(intPage)

	Dim i
	Dim objRS
	Dim strPage

	Response.Write "<div class=""Header"">" & ZC_MSG141 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" method=""post"" action="""">"
	Response.Write "<p>"& ZC_MSG134 &": </p>"
	Response.Write "<p><a href=""../cmd.asp?act=TagEdt"">["& ZC_MSG136 &"]</a></p>"
	Response.Write "</form>"

	Call CheckParameter(intPage,"int",1)

	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	objRS.Open("SELECT * FROM [blog_Tag] ORDER BY [tag_Order] DESC,[tag_Count] DESC,[tag_ID] ASC")

	objRS.PageSize=ZC_MANAGE_COUNT
	If objRS.PageCount>0 Then objRS.AbsolutePage = intPage

	Response.Write "<table border=""1"" width=""100%"" cellspacing=""1"" cellpadding=""1"">"
	Response.Write "<tr><td width=""5%"">"& ZC_MSG076 &"</td><td width=""25%"">"& ZC_MSG001 &"</td><td width=""40%"">"& ZC_MSG016 &"</td><td width=""15%""></td><td width=""15%""></td></tr>"

	If (Not objRS.bof) And (Not objRS.eof) Then

		For i=1 to objRS.PageSize

			Response.Write "<tr>"
			Response.Write "<td>" & objRS("tag_ID") & "</td>"
			Response.Write "<td>" & objRS("tag_Name") & "</td>"
			Response.Write "<td>" & objRS("tag_Intro") & "</td>"
			Response.Write "<td align=""center""><a href=""../cmd.asp?act=TagEdt&id="& objRS("tag_ID") &""">["& ZC_MSG078 &"]</a></td>"
			Response.Write "<td align=""center""><a onclick='return window.confirm("""& ZC_MSG058 &""");' href=""../cmd.asp?act=TagDel&amp;id="& objRS("tag_ID") &""">["& ZC_MSG063 &"]</a></td>"
			Response.Write "</tr>"

			objRS.MoveNext
			If objRS.eof Then Exit For

		Next

	End If

	Response.Write "</table>"

	For i=1 to objRS.PageCount
		strPage=strPage &"<a href='admin.asp?act=TagMng&amp;page="& i &"'>["& Replace(ZC_MSG036,"%s",i) &"]</a> "
	Next
	Response.Write "<hr/>" & ZC_MSG042 & ": " & strPage
	Response.Write "</div>"

	objRS.Close
	Set objRS=Nothing

	ExportTagList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    Manager Plugin
'*********************************************************
Function ExportPluginList()

	On Error Resume Next

	Response.Write "<div class=""Header"">" & ZC_MSG107 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" method=""post"" action="""">"

	Dim objXmlFile,strXmlFile
	Dim fso, f, f1, fc, s
	Set fso = CreateObject("Scripting.FileSystemObject")
	Set f = fso.GetFolder(BlogPath & "/plugin/")
	Set fc = f.SubFolders
	For Each f1 in fc
		If fso.FileExists(BlogPath & "/plugin/" & f1.name & "/" & "plugin.xml") Then

			strXmlFile =BlogPath & "/plugin/" & f1.name & "/" & "plugin.xml"

			Set objXmlFile=Server.CreateObject("Microsoft.XMLDOM")
			objXmlFile.async = False
			objXmlFile.ValidateOnParse=False
			objXmlFile.load(strXmlFile)
			If objXmlFile.readyState=4 Then
				If objXmlFile.parseError.errorCode <> 0 Then
				Else
					If BlogUser.Level<=CInt(objXmlFile.documentElement.selectSingleNode("level").text) Then
						Response.Write "<p>"
						Response.Write "[<a href=""../plugin/" & f1.name & "/" & objXmlFile.documentElement.selectSingleNode("path").text &""">" & objXmlFile.documentElement.selectSingleNode("name").text & "</a>]" & " <small>" & objXmlFile.documentElement.selectSingleNode("note").text & "</small><br/>"
						Response.Write "<p>"& ZC_MSG150 &":"& objXmlFile.documentElement.selectSingleNode("version").text &"&nbsp;&nbsp;&nbsp;&nbsp;" & ZC_MSG151 & ":" & objXmlFile.documentElement.selectSingleNode("modified").text & "&nbsp;&nbsp;&nbsp;&nbsp;"
						Response.Write ZC_MSG149 & ":" & "<a href=""" & objXmlFile.documentElement.selectSingleNode("author/url").text & """>"& objXmlFile.documentElement.selectSingleNode("author/name").text &"</a>"
						Response.Write "</p></p>"
					End If

				End If
			End If
			Set objXmlFile=Nothing
		End If
	Next

	Response.Write "</form>"
	Response.Write "</div>"
	Err.Clear

	ExportPluginList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function ExportSiteInfo()

	On Error Resume Next

	Dim FoundFso
	FoundFso = False
	FoundFso = IsObjInstalled("Scripting.FileSystemObject")


	Dim objRS
	Set objRS=Server.CreateObject("ADODB.Recordset")
	objRS.CursorType = adOpenKeyset
	objRS.LockType = adLockReadOnly
	objRS.ActiveConnection=objConn
	objRS.Source=""

	Dim allArticle,allCommNums,allTrackBackNums,allViewNums,allUserNums,allCateNums,allTagsNums

	objRS.Open("SELECT COUNT([log_ID])AS allArticle,SUM([log_CommNums]) AS allCommNums,SUM([log_ViewNums]) AS allViewNums,SUM([log_TrackBackNums]) AS allTrackBackNums FROM [blog_Article]")
	If (Not objRS.bof) And (Not objRS.eof) Then
		allArticle=objRS("allArticle")
		allCommNums=objRS("allCommNums")
		allTrackBackNums=objRS("allTrackBackNums")
		allViewNums=objRS("allViewNums")
	End If
	objRS.Close

	objRS.Open("SELECT COUNT([tag_ID])AS allTagsNums FROM [blog_Tag]")
	If (Not objRS.bof) And (Not objRS.eof) Then
		allTagsNums=objRS("allTagsNums")
	End If
	objRS.Close

	objRS.Open("SELECT COUNT([mem_ID])AS allUserNums FROM [blog_Member]")
	If (Not objRS.bof) And (Not objRS.eof) Then
		allUserNums=objRS("allUserNums")
	End If
	objRS.Close

	objRS.Open("SELECT COUNT([cate_ID])AS allCateNums FROM [blog_Category]")
	If (Not objRS.bof) And (Not objRS.eof) Then
		allCateNums=objRS("allCateNums")
	End If
	objRS.Close

	Call CheckParameter(allArticle,"int",0)
	Call CheckParameter(allCommNums,"int",0)
	Call CheckParameter(allTrackBackNums,"int",0)
	Call CheckParameter(allViewNums,"int",0)
	Call CheckParameter(allUserNums,"int",0)
	Call CheckParameter(allCateNums,"int",0)
	Call CheckParameter(allTagsNums,"int",0)

	Response.Write "<div class=""Header"">" & ZC_MSG159 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()
	%>

	<table border="0" cellspacing="0" cellpadding="0" align=center width="100%" class="tableBorder">
	<tr><th height=25 colspan=4>&nbsp;<%=ZC_MSG167%></th></tr>
	<tr>
	<td width="22%"><%=ZC_MSG160%></td>
	<td width="27%"><%=BlogUser.Name%> (<%=ZVA_User_Level_Name(BlogUser.Level)%>)</td>
	<td width="27%"><%=ZC_MSG150%></td>
	<td width="24%"><%=ZC_BLOG_VERSION%></td>
	</tr>
	<tr>
	<td width="22%"><%=ZC_MSG082%></td>
	<td width="27%"><%=allArticle%></td>
	<td width="27%"><%=ZC_MSG124%></td>
	<td width="24%"><%=allCommNums%></td>
	</tr>
	<tr>
	<td width="22%"><%=ZC_MSG125%></td>
	<td width="27%"><%=allTrackBackNums%></td>
	<td width="27%"><%=ZC_MSG129%></td>
	<td width="24%"><%=allViewNums%></td>
	</tr>
	<tr>
	<td width="22%"><%=ZC_MSG163%></td>
	<td width="27%"><%=allTagsNums%></td>
	<td width="27%"><%=ZC_MSG162%></td>
	<td width="24%"><%=allCateNums%></td>
	</tr>
	<tr>
	<td width="22%"><%=ZC_MSG083%></td>
	<td width="27%"><%=ZC_BLOG_CSS%>.css</td>
	<td width="27%"><%=ZC_MSG166%></td>
	<td width="24%"><%=allUserNums%></td>
	</tr>
	<tr>
	<td width="22%">MetaWeblog API</td>
	<td colspan="3" width="78%"><%=ZC_BLOG_HOST%>xml-rpc/index.asp</td>
	</tr>
	</table>
<!-- 
	<table border="0" cellspacing="0" cellpadding="0" align=center width="100%" class="tableBorder">
	<tr><th height=25 colspan=4>&nbsp;<%=ZC_MSG164%></th></tr>
	<tr>
	<td width="22%" ><%=ZC_MSG150%></td>
	<td width="27%"><%=ZC_BLOG_VERSION%></td>
	<td width="27%"></td>
	<td width="24%"></td>
	</tr>
	<tr>
	<td width="22%" >FSO </td>
	<td width="27%">
	<%
	If FoundFso Then
		Response.Write "<font color=green><b>ok</b></font>"
	Else
		Response.Write "<font color=red><b>fail</b></font>"
	End If
	%>
	</td>
	<td> Adodb.Stream </td>
	<td><%
	If IsObjInstalled("Adodb.Stream") Then
		Response.Write "<font color=green><b>ok</b></font>"
	Else
		Response.Write "<font color=red><b>fail</b></font>"
	End If
	%>
	</td>
	</tr>
	<tr>
	<td width="22%" >ADODB.Connection</td>
	<td width="27%">
	<%
	If IsObjInstalled("ADODB.Connection") Then
		Response.Write "<font color=green><b>ok</b></font>"
	Else
		Response.Write "<font color=red><b>fail</b></font>"
	End If
	%></td>
	<td> Microsoft.XMLDOM</td>
	<td><%
	If IsObjInstalled("Microsoft.XMLDOM") Then
		Response.Write "<font color=green><b>ok</b></font>"
	Else
		Response.Write "<font color=red><b>fail</b></font>"
	End If
	%>
	</td>
	</tr>
	<tr>
	<td width="22%" >
	MSXML2.ServerXMLHTTP</td>
	<td width="27%">
	<%
	If IsObjInstalled("MSXML2.ServerXMLHTTP") Then
		Response.Write "<font color=green><b>ok</b></font>"
	Else
		Response.Write "<font color=red><b>fail</b></font>"
	End If
	%>
	</td>
	<td > Scripting.Dictionary</td>
	<td><%
	If IsObjInstalled("Scripting.Dictionary") Then
		Response.Write "<font color=green><b>ok</b></font>"
	Else
		Response.Write "<font color=red><b>fail</b></font>"
	End If
	%>
	</td>
	</tr>
	</table>
-->
<%
If Len(ZC_UPDATE_INFO_URL)>0 Then
%>
	<table border="0" cellspacing="0" cellpadding="0" align=center width="100%" class="tableBorder">
	<tr><th height=25 colspan=4>&nbsp;<%=ZC_MSG164%>&nbsp;<a href="javascript:updateinfo('?reload');">[<%=ZC_MSG289%>]</a></th></tr>
	<tr><td height=25 colspan=4 id="tdUpdateInfo">
<script language="JavaScript" type="text/javascript">
function updateinfo(s){
	$.post("c_updateinfo.asp"+s,{},
		function(data){
			$("#tdUpdateInfo").html(data);
		}
	)
};

$(document).ready(function(){updateinfo("");});

</script>
	</td></tr>
	</table>
<%
End If
%>
	<br />
<%
	Response.Write "</div>"

	ExportSiteInfo=True

	Err.Clear

End Function
'*********************************************************




'*********************************************************
' 目的：    目录下文件列表
'*********************************************************
Function ExportSiteFileList(path,opath)

	Response.Write "<div class=""Header"">" & ZC_MSG210 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" method=""post"" action="""">"
%>
<p><%=ZC_MSG246%>:</p>
<p><a href="admin.asp?act=SiteFileEdt&path=<%=Escape("./INCLUDE/link.asp")%>" target="main">[<%=ZC_MSG031%>]</a>
<a href="admin.asp?act=SiteFileEdt&path=<%=Escape("./INCLUDE/favorite.asp")%>" target="main">[<%=ZC_MSG030%>]</a>
<a href="admin.asp?act=SiteFileEdt&path=<%=Escape("./INCLUDE/misc.asp")%>" target="main">[<%=ZC_MSG039%>]</a>
<a href="admin.asp?act=SiteFileEdt&path=<%=Escape("./INCLUDE/navbar.asp")%>" target="main">[<%=ZC_MSG233%>]</a>
</p>
<%
	Response.Write "</form>"


	dim f,fold,item,fpath,jpath
	set f=server.createobject("scripting.filesystemobject")

	  if path<>"" then
		 if instr(path,":")>0 then
		 path=path
		 else
		 path=server.mappath(path)
		 end if
	  else
	  path=blogpath
	  end if
	response.write "<br>"&ZC_MSG240&":"&path	  
	set fold=f.getfolder(path)

	response.write"<br><table width=""100%"" border=""0"">"
	response.write "<tr height=18><td><font face='wingdings' color='#003366'>0</font>&nbsp;<a href='../cmd.asp?act=SiteFileMng&path="&opath&"'>"&ZC_MSG239&"</a>&nbsp;&nbsp;&nbsp;</td></tr>"
	for each item in fold.subfolders
	jpath=replace(path,"\","\\")
	response.write "<tr height=18><td><font face='wingdings' color='#003366'>0</font>&nbsp;<a href='../cmd.asp?act=SiteFileMng&path="&path&"\"&item.name&"&opath="&path&"'>"&item.name&"</a>"
	response.write"</td></tr>"
	next
	for each item in fold.files
	fpath=replace(path&"/"&item.name,blogpath,"")
	fpath=replace(fpath,"\","/")
	response.write "<tr height=18><td><font face='wingdings' color='#ff0000'>2</font>&nbsp;<a href=""javascript:;"" title='"&ZC_MSG261&":"&item.datelastmodified&";"&ZC_MSG238&":"&clng(item.size/1024)&"k'>"&item.name&"</a>&nbsp;&nbsp;"
	response.write"<a href=""../cmd.asp?act=SiteFileEdt&path=."&Escape(fpath)&""">["&ZC_MSG078&"]</a>&nbsp;&nbsp;<a href=""../cmd.asp?act=SiteFileDel&path=."&Escape(fpath)&""" onclick='return window.confirm("""&ZC_MSG058&""");'>["&ZC_MSG063&"]</a>"

	next
	response.write"</table>"
	set fold=nothing

	set f=Nothing

	Response.Write "</div>"

	ExportSiteFileList=True

End Function
'*********************************************************




'*********************************************************
' 目的：    编辑文件
'*********************************************************
Function ExportSiteFileEdit(tpath)

	Dim Del,txaContent

	Response.Write "<div class=""Header"">" & ZC_MSG246 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	If IsEmpty(txaContent) Then txaContent=Null


	If Not IsNull(tpath) Then

		Response.Write "<form id=""edit"" name=""edit"" method=""post"" action=""../cmd.asp?act=SiteFilePst&path="&Escape(tpath)&""">" & vbCrlf
		Response.Write "<p><br/>" & ZC_MSG170 & ": <br/><INPUT TYPE=""text"" Value="""&unEscape(tpath)&""" style=""width:100%"" name=""path"" id=""path"" readonly></p>"
		Response.Write "<p><textarea style=""height:300px;width:100%"" name=""txaContent"" id=""txaContent"">"&TransferHTML(LoadFromFile(BlogPath & unEscape(tpath),"utf-8"),"[textarea]")&"</textarea></p>" & vbCrlf
		Response.Write "<hr/>"
		Response.Write "<p><input class=""button"" type=""submit"" value="""&ZC_MSG087&""" id=""btnPost""/></p>" & vbCrlf
		Response.Write "</form>" & vbCrlf

	End If

	Response.Write "</div>"

	ExportSiteFileEdit=True

End Function
'*********************************************************




'*********************************************************
' 目的：    
'*********************************************************
Function ExportFileReBuildAsk()

	Response.Write "<div class=""Header"">" & ZC_MSG073 & "</div>"
	Response.Write "<div id=""divMain2"">"

	Call GetBlogHint()

	Response.Write "<form id=""edit"" name=""edit"" method=""post"" action=""../cmd.asp?act=FileReBuild"">" & vbCrlf
	Response.Write "<p>"& ZC_MSG112 &"</p>" & vbCrlf
	Response.Write "<p><input class=""button"" type=""submit"" value="""&ZC_MSG087&""" id=""btnPost""/></p>" & vbCrlf
	Response.Write "</form>" & vbCrlf

	Response.Write "</div>"

	ExportFileReBuildAsk=True

End Function
'*********************************************************




%>