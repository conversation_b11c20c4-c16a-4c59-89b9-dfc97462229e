<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    (zx.asd)&(sipo)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    wap.asp
'// 开始时间:    2006-3-19
'// 最后修改:    
'// 备    注:    WAP模块
'///////////////////////////////////////////////////////////////////////////////

Option Explicit
On Error Resume Next
Response.Charset="UTF-8"
Response.Buffer=True
Response.Expires = "0"
Response.AddHeader "Pragma", "no-cache"
Response.AddHeader "Cache-Control", "no-cache, must-revalidate"
%>
<!-- #include file="c_option.asp" -->
<!-- #include file="function/c_function.asp" -->
<!-- #include file="function/c_function_md5.asp" -->
<!-- #include file="function/c_system_lib.asp" -->
<!-- #include file="function/c_system_base.asp" -->
<!-- #include file="function/c_system_event.asp" -->
<!-- #include file="function/c_system_wap.asp" -->
<!-- #include file="function/rss_lib.asp" -->
<!-- #include file="function/atom_lib.asp" -->
<%
If ReQuest("act")="BlogReBuild" Then
	Call System_Initialize()
	Server.ScriptTimeout = 1200
	Call MakeBlogReBuild
	Call System_Terminate()
	Response.Clear
End If
%>
<%
If ZC_IE_DISPLAY_WAP Then
	'If InStr(LCase(Request.ServerVariables("HTTP_ACCEPT")),"text/vnd.wap.wml") > 0 Then Response.ContentType = "text/vnd.wap.wml"
	Response.ContentType = "text/vnd.wap.wml"
Else
	Response.ContentType = "text/vnd.wap.wml"
End If

ShowError_Custom="Call ShowError_WAP(id)"

%><?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE wml PUBLIC "-//WAPFORUM//DTD WML 1.1//EN" "http://www.wapforum.org/DTD/wml_1.1.xml">
<wml>
<head><meta forua="true" http-equiv="Cache-Control" content="max-age=0" /></head>
<%
Call System_Initialize()
PubLic intPageCount
	Select Case ReQuest("act")
		Case "View"
			Call WapView()
		Case "Com"
			Call WapCom()
		Case "Main"
			Call WapMain()
		Case "Login"
			Call WapLogin()
		Case "Err"
			Call WapError()
		Case "Cate"
			Call WapCate()
		Case "Stat"
			Call WapStat()
		Case "AddCom"
			Call WapAddCom(0)
		Case "PostCom"
			Call WapPostCom()
		Case "DelCom"
			Call WapDelCom()
		Case "AddArt"
		    Call WapEdtArt()
		Case "PostArt"
		    Call WapPostArt()
		Case "DelArt"
			Call WapDelArt()
		Case "Logout"
			Call WapLogout()
		Case Else
			Call WapMenu()
	End Select

Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>
<br/>
<a href="<%=ZC_BLOG_HOST&ZC_FILENAME_WAP%>"><%=ZC_MSG213%></a>
</p>
</card>
</wml>