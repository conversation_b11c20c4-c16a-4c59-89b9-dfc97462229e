///////////////////////////////////////////////////////////////////////////////
//              Z-Blog
// 作    者:    朱煊(zx.asd)
// 版权所有:    RainbowSoft Studio
// 技术支持:    <EMAIL>
// 程序名称:    
// 程序版本:    
// 单元名称:    common.js
// 开始时间:    2004.07.25
// 最后修改:    
// 备    注:    全局脚本
///////////////////////////////////////////////////////////////////////////////





//*********************************************************
// 目的：    加载样式表
// 输入：    无
// 返回：    无
//*********************************************************
function LoadActiveStyleSheet(){

	var title=GetCookie("sk")
	var a;

	if (title) {

		a = document.getElementsByTagName("link")[0];

		a.href=str00+"style/"+title+".css"

	}

}
//*********************************************************




//*********************************************************
// 目的：    设置样式表
// 输入：    title
// 返回：    无
//*********************************************************
function SetActiveStyleSheet(title){

	var a;

	if (title) {

		{
			SetCookie("sk",title,365);
		}
		a = document.getElementsByTagName("link")[0];

		a.href=str00+"style/"+title+".css"

	}

}
//*********************************************************




//*********************************************************
// 目的：    设置Cookie
// 输入：    sName, sValue,iExpireDays
// 返回：    无
//*********************************************************
function SetCookie(sName, sValue,iExpireDays) {
	if (iExpireDays){
		var dExpire = new Date();
		dExpire.setTime(dExpire.getTime()+parseInt(iExpireDays*24*60*60*1000));
		document.cookie = sName + "=" + escape(sValue) + "; expires=" + dExpire.toGMTString();
	}
	else{
		document.cookie = sName + "=" + escape(sValue);
	}
}
//*********************************************************




//*********************************************************
// 目的：    返回Cookie
// 输入：    Name
// 返回：    Cookie值
//*********************************************************
function GetCookie(sName) {

	var arr = document.cookie.match(new RegExp("(^| )"+sName+"=([^;]*)(;|$)"));
	if(arr !=null){return unescape(arr[2])};
	return null;

}
//*********************************************************




//*********************************************************
// 目的：    验证信息
// 输入：    无
// 返回：    无
//*********************************************************
var strFormAction;
var objImageValid;
function VerifyMessage() {

	var strName=document.getElementById("inpName").value;
	var strEmail=document.getElementById("inpEmail").value;
	var strHomePage=document.getElementById("inpHomePage").value;
	var strArticle;

	if(document.getElementById("txaArticle").value){
		strArticle=document.getElementById("txaArticle").value;
	}
	else{
		strArticle=document.getElementById("txaArticle").innerText;
	}

	if(strName==""){
		alert(str01);
		return false;
	}
	else{
		re = new RegExp("^[.A-Za-z0-9\u4e00-\u9fa5]+$");
		if (!re.test(strName)){
			alert(str02);
			return false;
		}
	}

	if(strEmail==""){
		//alert(str01);
		//return false;
	}
	else{
		re = new RegExp("^[\\w-]+(\\.[\\w-]+)*@[\\w-]+(\\.[\\w-]+)+$");
		if (!re.test(strEmail)){
			alert(str02);
			return false;
		}
	}

	if(typeof(strArticle)=="undefined"){
		alert(str03);
		return false;
	}

	if(typeof(strArticle)=="string"){
		if(strArticle==""){
			alert(str03);
			return false;
		}
		if(strArticle.length>intMaxLen)
		{
			alert(str03);
			return false;
		}
	}

	document.getElementById("inpArticle").value=strArticle;
	document.getElementById("inpLocation").value=parent.window.location.href;
	if(document.getElementById("frmSumbit").action.search("act=cmt")>0){
		strFormAction=document.getElementById("frmSumbit").action;
	}

	var bolRemember=document.getElementById("chkRemember").checked;

	if(bolRemember==true){
		SaveRememberInfo();
	}
	else{
		SetCookie("chkRemember",bolRemember,365);
	}


	//ajax comment begin
	$.post(document.getElementById("frmSumbit").action,
		{
		"inpAjax":true,
		"inpID":$("#inpId").val(),
		"inpVerify":(document.getElementById("inpVerify")?$("#inpVerify").val():""),
		"inpEmail":strEmail,
		"inpName":strName,
		"inpArticle":strArticle,
		"inpHomePage":strHomePage
		},
		function(data){
			var s =data;
			if((s.search("faultCode")>0)&&(s.search("faultString")>0))
			{
				alert(s.match("<string>.+?</string>")[0].replace("<string>","").replace("</string>",""))
			}
			else{
				var i=Math.round(Math.random()*1000)
				var s ="<div id=\"divAjaxComment"+i+"\">"+data+"</div>";
				$(s).insertBefore("#divAjaxComment");
				$("#divAjaxComment"+i).fadeIn("slow");
				//$("#divAjaxComment"+i).fadeTo("normal", 0);
				//$("#divAjaxComment"+i).fadeTo("normal", 1);
				//$("#divAjaxComment"+i).show("slow");
				if(strFormAction){
					document.getElementById("frmSumbit").action=strFormAction;
				}
				$("#txaArticle").val("");
			}
			if(document.getElementById("inpVerify")){
				$("#inpVerify").val("");
				if(!objImageValid){
					objImageValid=$("img[@src='"+str00+"function/c_validcode.asp?name=commentvalid']");
				}
				objImageValid.src(str00+"function/c_validcode.asp?name=commentvalid"+"&random="+Math.random());
			}
		}
	);

	return false;
	//ajax comment end

}
//*********************************************************




//*********************************************************
// 目的：    加载信息
// 输入：    无
// 返回：    无
//*********************************************************
function LoadRememberInfo() {

	var strName=GetCookie("inpName")
	var strEmail=GetCookie("inpEmail")
	var strHomePage=GetCookie("inpHomePage")
	var bolRemember=GetCookie("chkRemember")

	if(bolRemember=="true"){

		if(strName){document.getElementById("inpName").value=strName;};
		if(strEmail){document.getElementById("inpEmail").value=strEmail;};
		if(strHomePage){document.getElementById("inpHomePage").value=strHomePage;};
		if(bolRemember){document.getElementById("chkRemember").checked=bolRemember;};

	}

	if(GetCookie("username")){
		document.getElementById("inpName").value=unescape(GetCookie("username"))
	}

}
//*********************************************************




//*********************************************************
// 目的：    保存信息
// 输入：    无
// 返回：    无
//*********************************************************
function SaveRememberInfo() {

	var strName=document.getElementById("inpName").value;
	var strEmail=document.getElementById("inpEmail").value;
	var strHomePage=document.getElementById("inpHomePage").value;
	var bolRemember=document.getElementById("chkRemember").checked;


	SetCookie("inpName",strName,365);
	SetCookie("inpEmail",strEmail,365);
	SetCookie("inpHomePage",strHomePage,365);
	SetCookie("chkRemember",bolRemember,365);

}
//*********************************************************





//*********************************************************
// 目的：    输出UBB
// 输入：    无
// 返回：    无
//*********************************************************
function ExportUbbFrame() {

	if(!objActive){objActive="txaArticle"};

	document.write("<p id=\"UbbFrame\" style=\"display:none;\"></p>")

	document.write("<p>")

	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[URL]','[/URL]'),true);\" style=\"padding:2px;cursor:pointer;\">[URL]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[URL=http://]','[/URL]'),true);\" style=\"padding:2px;cursor:pointer;\">[URL2]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[EMAIL]','[/EMAIL]'),true);\" style=\"padding:2px;cursor:pointer;\">[EMAIL]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[EMAIL=@]','[/EMAIL]'),true);\" style=\"padding:2px;cursor:pointer;\">[EMAIL2]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[B]','[/B]'),true);\" style=\"padding:2px;cursor:pointer;\">[B]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[I]','[/I]'),true);\" style=\"padding:2px;cursor:pointer;\">[I]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[U]','[/U]'),true);\" style=\"padding:2px;cursor:pointer;\">[U]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[S]','[/S]'),true);\" style=\"padding:2px;cursor:pointer;\">[S]</a>  ")
	document.write("<a alt=\"\" onmousedown=\"InsertText(objActive,ReplaceText(objActive,'[QUOTE]','[/QUOTE]'),true);\" style=\"padding:2px;cursor:pointer;\">[QUOTE]</a>  ")

	document.write("<u><a style=\"cursor:pointer;text-align:right;\" onclick=\"InsertUbbFace();if(document.getElementById('UbbFrame').style.display=='none'){document.getElementById('UbbFrame').style.display='block';}else{document.getElementById('UbbFrame').style.display='none'};this.style.display='none'\">"+str06+"</a></u> ")

	document.write("</p>")
}
//*********************************************************




//*********************************************************
// 目的：    插入表情图片HTML代码
// 输入：    无
// 返回：    无
//*********************************************************
function InsertUbbFace() {

	if(!document.getElementById("UbbFrame").innerHTML && strFaceName){

		var aryFileName="";
		var strFileName="";
		var strFaceHtml="";

		aryFileName = strFaceName.split("|");

		for (var i=0;i<aryFileName.length;i++)
		{
			strFileName = aryFileName[i];
			strFaceHtml=strFaceHtml + "<img src=\""+str00+"image/face/"+strFileName+".gif\" title=\""+strFileName+"\" alt=\""+strFileName+"\" width=\""+strFaceSize+"\" height=\""+strFaceSize+"\" onclick=\"InsertText(objActive,'[F]'+this.alt+'[/F]',false);\" style=\"padding:2px;cursor:pointer;\">"
		}
		document.getElementById("UbbFrame").innerHTML=strFaceHtml;
	}

}
//*********************************************************




//*********************************************************
// 目的：    自动插入并替换
// 输入：    无
// 返回：    无
//*********************************************************
var objActive;
function GetActiveText(objHTML) {
	objActive=objHTML;
	if(document.selection){
		var obj=document.getElementById(objHTML);
		obj.currPos = document.selection.createRange().duplicate()
	}
}

function InsertText(objHTML,strText,bolReplace) {
	if(strText==""){return("")}
	var obj=document.getElementById(objHTML);
	if(document.selection){
		if (obj.currPos){
			if(bolReplace && (obj.value=="")){
				obj.currPos.text=strText
			}
			else{
				obj.currPos.text+=strText
			}
		}
		else{
			obj.value+=strText
		}
	}
	else{
		if(bolReplace){
			obj.value=obj.value.slice(0,obj.selectionStart) + strText + obj.value.slice(obj.selectionEnd,obj.value.length)
		}
		else{
			obj.value=obj.value.slice(0,obj.selectionStart) + strText + obj.value.slice(obj.selectionStart,obj.value.length)
		}
	}
	//obj.focus();
}

function ReplaceText(objHTML,strPrevious,strNext) {
	var obj=document.getElementById(objHTML);
	var strText;
	if(document.selection && document.selection.type == "Text"){
		if (obj.currPos){
			var range = document.selection.createRange();
			range.text = strPrevious + range.text + strNext;
			return("");
		}
		else{
			strText=strPrevious + strNext;
			return(strText);
		}
	}
	else{
		if(obj.selectionStart || obj.selectionEnd){
			strText=strPrevious + obj.value.slice(obj.selectionStart,obj.selectionEnd) + strNext;
			return(strText);
		}
		else{
			strText=strPrevious + strNext;
			return(strText);
		}
	}
}
//*********************************************************




//*********************************************************
// 目的：    ShowMedia By UBB
// 输入：    无
// 返回：    无
//*********************************************************
function ShowMedia(objHTML,strURL,strType,intWidth,intHeight) {
	var strMedia="";
	var blnShow = false;
	var objMedia=objHTML;

	if(objMedia.innerHTML){blnShow = true};

	var re = new RegExp("\.[a-z0-9]+$","ig");

	var strExtend = re.exec(strURL);
	strExtend="|"+String(strExtend).toLowerCase()+"|";

	if(strType=="AUTO"){
		if(String("|.swf|").indexOf(strExtend)>=0){strType="SWF"}
		if(String("|.mov|.qt|").indexOf(strExtend)>=0){strType="QT"}
		if(String("|.wmv|.wmp|.wm|.avi|.mp4|.mpg|.mpeg|.m3u|.pls|.wvx|.wax|.wmx|").indexOf(strExtend)>=0){strType="WMV"}
		if(String("|.asf|.wma|.asx|.wav|.mp3|.mpa|.mp2|.m1a|.m2a|.aac|").indexOf(strExtend)>=0){strType="WMA"}
		if(String("|.rm|.ram|.rmvb|.rpm|.amr|.3gp|.3gpp|.3g2|.3gp2|.rt|.rp|.smi|.smil|").indexOf(strExtend)>=0){strType="RM"}
		if(String("|.ra|").indexOf(strExtend)>=0){strType="RA"}
	}

	if(blnShow){
		objMedia.innerHTML = strMedia;
	}
	else{
		switch(strType){
			case "SWF":
				strMedia="<object classid=\"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000\" codebase=\"http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,29,0\" width=\""+intWidth+"\" height=\""+intHeight+"\"><param name=\"movie\" value=\""+strURL+"\"><param name=\"quality\" value=\"high\"><param name=\"play\" value=\"true\"><embed src=\""+strURL+"\" quality=\"high\" pluginspage=\"http://www.macromedia.com/go/getflashplayer\" type=\"application/x-shockwave-flash\" width=\""+intWidth+"\" height=\""+intHeight+"\" play=\"true\"></embed></object>"
				break;
			case "QT":
				strMedia="<object classid=\"clsid:02BF25D5-8C17-4B23-BC80-D3488ABDDC6B\" codebase=\"http://www.apple.com/qtactivex/qtplugin.cab\" width=\""+intWidth+"\" height=\""+intHeight+"\" ><param name=\"src\" value=\""+strURL+"\" ><param name=\"autoplay\" value=\"true\" ><embed  src=\"qtmimetype.pntg\" type=\"image/x-macpaint\"pluginspage=\"http://www.apple.com/quicktime/download\" qtsrc=\""+strURL+"\" width=\""+intHeight+"\" height=\""+intHeight+"\" autoplay=\"true\" ></embed></object>"
				break;
			case "WMV":
				strMedia="<object classid=\"clsid:22D6F312-B0F6-11D0-94AB-0080C74C7E95\" width=\""+intWidth+"\" height=\""+intHeight+"\"><param name=\"ShowStatusBar\" value=\"-1\"><param name=\"AutoStart\" value=\"true\"><param name=\"Filename\" value=\""+strURL+"\"><embed type=\"application/x-mplayer2\" pluginspage=\"http://www.microsoft.com/Windows/MediaPlayer/\" src=\""+strURL+"\" autostart=\"true\" width=\""+intWidth+"\" height=\""+intHeight+"\"></embed></object>"
				break;
			case "WMA":
				strMedia="<object classid=\"clsid:22D6F312-B0F6-11D0-94AB-0080C74C7E95\" height=\"68\" width=\"350\"><param name=\"ShowStatusBar\" value=\"-1\"><param name=\"AutoStart\" value=\"true\"><param name=\"Filename\" value=\""+strURL+"\"><embed type=\"application/x-mplayer2\" pluginspage=\"http://www.microsoft.com/Windows/MediaPlayer/\" src=\""+strURL+"\" autostart=\"true\" width=\"350\" height=\"45\"></embed></object>"
				break;
			case "RM":
				strMedia="<object classid=\"clsid:CFCDAA03-8BE4-11CF-B84B-0020AFBBCCFA\" width=\""+intWidth+"\" height=\""+intWidth+"\"><param name=\"src\" value=\""+strURL+"\"><param name=\"controls\" value=\"imagewindow\"><param name=\"console\" value=\"one\"><param name=\"AutoStart\" value=\"true\"><embed src=\""+strURL+"\" width=\""+intWidth+"\" height=\""+intWidth+"\" type=\"audio/x-pn-realaudio-plugin\" nojava=\"true\" controls=\"imagewindow,ControlPanel,StatusBar\" console=\"one\" autostart=\"true\"></object>"
				break;
			case "RA":
				strMedia="<object classid=\"clsid:CFCDAA03-8BE4-11CF-B84B-0020AFBBCCFA\" width=\"350\" height=\"36\"><param name=\"src\" value=\""+strURL+"\"><param name=\"controls\" value=\"ControlPanel\"><param name=\"console\" value=\"one\"><param name=\"AutoStart\" value=\"true\"><embed src=\""+strURL+"\" type=\"audio/x-pn-realaudio-plugin\" nojava=\"true\" controls=\"ControlPanel,StatusBar\" console=\"one\" autostart=\"true\" width=\"350\" height=\"36\"></object>"
		}
		objMedia.innerHTML = strMedia;
	}
}
//*********************************************************




//*********************************************************
// 目的：    InsertQuote To txaArticle
// 输入：    无
// 返回：    无
//*********************************************************
function InsertQuote(strName,strText) {
	if(!objActive){objActive="txaArticle"};

	var re;
	re=new RegExp("<br/?>","ig");
	strText=strText.replace(re, "\n");
	re=new RegExp("<[^>]*>","ig");
	strText=strText.replace(re, "");

	InsertText(objActive,"[QUOTE=",false);
	InsertText(objActive,strName,false);
	InsertText(objActive,"]",false);
	InsertText(objActive,strText,false);
	InsertText(objActive,"[/QUOTE]",false);
}
//*********************************************************




//*********************************************************
// 目的：    全选
// 输入：    无
// 返回：    无
//*********************************************************
function BatchSelectAll() {
	var aryChecks = document.getElementsByTagName("input");

	for (var i = 0; i < aryChecks.length; i++){
		if((aryChecks[i].type=="checkbox")&&(aryChecks[i].id.indexOf("edt")!==-1)){
			if(aryChecks[i].checked==true){
				aryChecks[i].checked=false;
			}
			else{
				aryChecks[i].checked=true;
			};
		}
	}
}
//*********************************************************




//*********************************************************
// 目的：    
// 输入：    无
// 返回：    无
//*********************************************************
function BatchDeleteAll(objEdit) {

	objEdit=document.getElementById(objEdit)
	objEdit.value=""
	var aryChecks = document.getElementsByTagName("input");
	for (var i = 0; i < aryChecks.length; i++){
		if((aryChecks[i].type=="checkbox")&&(aryChecks[i].id.indexOf("edt")!==-1)){
			if(aryChecks[i].checked){
				objEdit.value=aryChecks[i].value+","+objEdit.value
			}
		}
	}

}
//*********************************************************




//*********************************************************
// 目的：    图片自动缩小 点击放大
// 输入：    无
// 返回：    无
//*********************************************************
function ResizeImage(objImage,maxWidth) {
	try{
		if(maxWidth>0){
			if(objImage.width>maxWidth){
				objImage.width=maxWidth;
				objImage.attachEvent('onclick', function(){try{showModelessDialog(objImage.src);}catch(e){window.open(objImage.src);}});
				objImage.attachEvent('onmouseover', function(){objImage.style.cursor='pointer';});
			}
		}
	}catch(e){};
}
//*********************************************************




//*********************************************************
// 目的：    回复留言
// 输入：    无
// 返回：    无
//*********************************************************
function RevertComment(intRevertID) {
	var f;
	if(objActive){
		f=document.getElementById(objActive).form
	}
	else{
		f=document.getElementsByTagName("form")[document.getElementsByTagName("form").length-1]
	}

	if(f.action.search("act=cmt")>0){
		f.action=f.action.replace("cmd.asp?act=cmt","cmd.asp?act=CommentRev&id="+intRevertID);
		return true;
	}
	else{
		f.action=f.action.substr(0,f.action.search("id=")) + "id="+intRevertID +"&" + f.action.substr(f.action.search("key="))
		return true;
	}
}
//*********************************************************




///////////////////////////////////////////////////////////////////////////////
//              嵌入 jQuery
///////////////////////////////////////////////////////////////////////////////
eval(function(p,a,c,k,e,d){e=function(c){return(c<a?"":e(parseInt(c/a)))+((c=c%a)>35?String.fromCharCode(c+29):c.toString(36))};if(!''.replace(/^/,String)){while(c--){d[e(c)]=k[c]||e(c)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('l(1l 1x.6=="Q"){1x.Q=1x.Q;u 6=q(a,c){l(a&&1l a=="q"&&6.C.21&&!a.1G&&a[0]==Q)v 6(Y).21(a);a=a||Y;l(a.3n)v 6(6.1Q(a,[]));l(c&&c.3n)v 6(c).1X(a);l(1x==7)v 1m 6(a,c);l(1l a=="24"){u m=/^[^<]*(<.+>)[^>]*$/.3c(a);l(m)a=6.3F([m[1]])}7.2a(a.14==2o||a.D&&a!=1x&&!a.1G&&a[0]!=Q&&a[0].1G?6.1Q(a,[]):6.1X(a,c));u C=15[15.D-1];l(C&&1l C=="q")7.V(C);v 7};l(1l $!="Q")6.3W$=$;u $=6;6.C=6.8h={3n:"1.0.4",66:q(){v 7.D},1S:q(2R){v 2R==Q?6.1Q(7,[]):7[2R]},2a:q(64){7.D=0;[].1q.17(7,64);v 7},V:q(C,1h){v 6.V(7,C,1h)},8k:q(1j){u 2h=-1;7.V(q(i){l(7==1j)2h=i});v 2h},1r:q(1I,11,B){v 1I.14!=3X||11!=Q?7.V(q(){l(11==Q)J(u E 1z 1I)6.1r(B?7.1o:7,E,1I[E]);G 6.1r(B?7.1o:7,1I,11)}):6[B||"1r"](7[0],1I)},1a:q(1I,11){v 7.1r(1I,11,"3j")},2D:q(e){e=e||7;u t="";J(u j=0;j<e.D;j++){u r=e[j].2x;J(u i=0;i<r.D;i++)l(r[i].1G!=8)t+=r[i].1G!=1?r[i].56:6.C.2D([r[i]])}v t},1W:q(){u a=6.3F(15);v 7.V(q(){u b=a[0].3I(P);7.1i.2M(b,7);1V(b.26)b=b.26;b.49(7)})},5y:q(){v 7.2V(15,P,1,q(a){7.49(a)})},5z:q(){v 7.2V(15,P,-1,q(a){7.2M(a,7.26)})},5A:q(){v 7.2V(15,W,1,q(a){7.1i.2M(a,7)})},5C:q(){v 7.2V(15,W,-1,q(a){7.1i.2M(a,7.7L)})},4m:q(){l(!(7.2n&&7.2n.D))v 7;v 7.2a(7.2n.7W())},1X:q(t){v 7.2i(6.2C(7,q(a){v 6.1X(t,a)}),15)},4F:q(4E){v 7.2i(6.2C(7,q(a){v a.3I(4E!=Q?4E:P)}),15)},18:q(t){v 7.2i(t.14==2o&&6.2C(7,q(a){J(u i=0;i<t.D;i++)l(6.18(t[i],[a]).r.D)v a;v L})||t.14==8n&&(t?7.1S():[])||1l t=="q"&&6.2P(7,t)||6.18(t,7).r,15)},2q:q(t){v 7.2i(1l t=="24"?6.18(t,7,W).r:6.2P(7,q(a){v a!=t}),15)},29:q(t){v 7.2i(6.1Q(7,1l t=="24"?6.1X(t):t.14==2o?t:[t]),15)},4s:q(2z){v 2z?6.18(2z,7).r.D>0:W},2V:q(1h,23,2T,C){u 4F=7.66()>1;u a=6.3F(1h);v 7.V(q(){u 1j=7;l(23&&7.2t.2d()=="8p"&&a[0].2t.2d()!="8q"){u 25=7.51("25");l(!25.D){1j=Y.5Y("25");7.49(1j)}G 1j=25[0]}J(u i=(2T<0?a.D-1:0);i!=(2T<0?2T:a.D);i+=2T){C.17(1j,[4F?a[i].3I(P):a[i]])}})},2i:q(a,1h){u C=1h&&1h[1h.D-1];u 2m=1h&&1h[1h.D-2];l(C&&C.14!=1A)C=L;l(2m&&2m.14!=1A)2m=L;l(!C){l(!7.2n)7.2n=[];7.2n.1q(7.1S());7.2a(a)}G{u 20=7.1S();7.2a(a);l(2m&&a.D||!2m)7.V(2m||C).2a(20);G 7.2a(20).V(C)}v 7}};6.1y=6.C.1y=q(){u 1T=15[0],a=1;l(15.D==1){1T=7;a=0}u E;1V(E=15[a++])J(u i 1z E)1T[i]=E[i];v 1T};6.1y({5R:q(){6.68=P;6.V(6.2c.5J,q(i,n){6.C[i]=q(a){u R=6.2C(7,n);l(a&&1l a=="24")R=6.18(a,R).r;v 7.2i(R,15)}});6.V(6.2c.2w,q(i,n){6.C[i]=q(){u a=15;v 7.V(q(){J(u j=0;j<a.D;j++)6(a[j])[n](7)})}});6.V(6.2c.V,q(i,n){6.C[i]=q(){v 7.V(n,15)}});6.V(6.2c.18,q(i,n){6.C[n]=q(2R,C){v 7.18(":"+n+"("+2R+")",C)}});6.V(6.2c.1r,q(i,n){n=n||i;6.C[i]=q(h){v h==Q?7.D?7[0][n]:L:7.1r(n,h)}});6.V(6.2c.1a,q(i,n){6.C[n]=q(h){v h==Q?(7.D?6.1a(7[0],n):L):7.1a(n,h)}})},V:q(1j,C,1h){l(1j.D==Q)J(u i 1z 1j)C.17(1j[i],1h||[i,1j[i]]);G J(u i=0;i<1j.D;i++)l(C.17(1j[i],1h||[i,1j[i]])===W)3Y;v 1j},1e:{29:q(o,c){l(6.1e.3k(o,c))v;o.1e+=(o.1e?" ":"")+c},28:q(o,c){l(!c){o.1e=""}G{u 2N=o.1e.3B(" ");J(u i=0;i<2N.D;i++){l(2N[i]==c){2N.69(i,1);3Y}}o.1e=2N.4N(\' \')}},3k:q(e,a){l(e.1e!=Q)e=e.1e;v 1m 3V("(^|\\\\s)"+a+"(\\\\s|$)").1U(e)}},3O:q(e,o,f){J(u i 1z o){e.1o["20"+i]=e.1o[i];e.1o[i]=o[i]}f.17(e,[]);J(u i 1z o)e.1o[i]=e.1o["20"+i]},1a:q(e,p){l(p=="27"||p=="3J"){u 20={},3G,36,d=["6a","6p","6q","6j"];J(u i=0;i<d.D;i++){20["6e"+d[i]]=0;20["6c"+d[i]+"6h"]=0}6.3O(e,20,q(){l(6.1a(e,"1b")!="1O"){3G=e.78;36=e.6k}G{e=6(e.3I(P)).1X(":4n").5N("2U").4m().1a({4p:"1Y",2W:"6l",1b:"2r",8t:"0",5D:"0"}).5x(e.1i)[0];u 2I=6.1a(e.1i,"2W");l(2I==""||2I=="3M")e.1i.1o.2W="8s";3G=e.6n;36=e.6o;l(2I==""||2I=="3M")e.1i.1o.2W="3M";e.1i.3v(e)}});v p=="27"?3G:36}v 6.3j(e,p)},3j:q(I,E,4L){u R;l(E==\'1g\'&&6.T.1n)v 6.1r(I.1o,\'1g\');l(E=="3y"||E=="2B")E=6.T.1n?"3b":"2B";l(!4L&&I.1o[E]){R=I.1o[E]}G l(Y.3H&&Y.3H.3P){l(E=="2B"||E=="3b")E="3y";E=E.1E(/([A-Z])/g,"-$1").4A();u 1c=Y.3H.3P(I,L);l(1c)R=1c.4O(E);G l(E==\'1b\')R=\'1O\';G 6.3O(I,{1b:\'2r\'},q(){u c=Y.3H.3P(7,\'\');R=c&&c.4O(E)||\'\'})}G l(I.4w){u 4Q=E.1E(/\\-(\\w)/g,q(m,c){v c.2d()});R=I.4w[E]||I.4w[4Q]}v R},3F:q(a){u r=[];J(u i=0;i<a.D;i++){u 1C=a[i];l(1l 1C=="24"){u s=6.2Q(1C),2b=Y.5Y("2b"),1W=[0,"",""];l(!s.1f("<89"))1W=[1,"<3E>","</3E>"];G l(!s.1f("<6t")||!s.1f("<25"))1W=[1,"<23>","</23>"];G l(!s.1f("<3Q"))1W=[2,"<23>","</23>"];G l(!s.1f("<6v")||!s.1f("<6w"))1W=[3,"<23><25><3Q>","</3Q></25></23>"];2b.31=1W[1]+s+1W[2];1V(1W[0]--)2b=2b.26;1C=2b.2x}l(1C.D!=Q&&((6.T.2l&&1l 1C==\'q\')||!1C.1G))J(u n=0;n<1C.D;n++)r.1q(1C[n]);G r.1q(1C.1G?1C:Y.81(1C.7Z()))}v r},2z:{"":"m[2]== \'*\'||a.2t.2d()==m[2].2d()","#":"a.48(\'35\')&&a.48(\'35\')==m[2]",":":{5G:"i<m[3]-0",5H:"i>m[3]-0",5V:"m[3]-0==i",5F:"m[3]-0==i",2j:"i==0",1R:"i==r.D-1",5f:"i%2==0",5g:"i%2","5V-3z":"6.1B(a,m[3]).1c","2j-3z":"6.1B(a,0).1c","1R-3z":"6.1B(a,0).1R","6A-3z":"6.1B(a).D==1",5L:"a.2x.D",5P:"!a.2x.D",5I:"6.C.2D.17([a]).1f(m[3])>=0",6C:"a.B!=\'1Y\'&&6.1a(a,\'1b\')!=\'1O\'&&6.1a(a,\'4p\')!=\'1Y\'",1Y:"a.B==\'1Y\'||6.1a(a,\'1b\')==\'1O\'||6.1a(a,\'4p\')==\'1Y\'",6D:"!a.2O",2O:"a.2O",2U:"a.2U",4o:"a.4o || 6.1r(a, \'4o\')",2D:"a.B==\'2D\'",4n:"a.B==\'4n\'",5T:"a.B==\'5T\'",4G:"a.B==\'4G\'",5W:"a.B==\'5W\'",4x:"a.B==\'4x\'",4V:"a.B==\'4V\'",4v:"a.B==\'4v\'",4j:"a.B==\'4j\'",4W:"/4W|3E|6H|4j/i.1U(a.2t)"},".":"6.1e.3k(a,m[2])","@":{"=":"z==m[4]","!=":"z!=m[4]","^=":"z && !z.1f(m[4])","$=":"z && z.2Z(z.D - m[4].D,m[4].D)==m[4]","*=":"z && z.1f(m[4])>=0","":"z"},"[":"6.1X(m[2],a).D"},3u:["\\\\.\\\\.|/\\\\.\\\\.","a.1i",">|/","6.1B(a.26)","\\\\+","6.1B(a).3s","~",q(a){u s=6.1B(a);v s.n>=0?s.5o(s.n+1):[]}],1X:q(t,1u){l(1u&&1u.1G==Q)1u=L;1u=1u||Y;l(t.14!=3X)v[t];l(!t.1f("//")){1u=1u.47;t=t.2Z(2,t.D)}G l(!t.1f("/")){1u=1u.47;t=t.2Z(1,t.D);l(t.1f("/")>=1)t=t.2Z(t.1f("/"),t.D)}u R=[1u];u 1N=[];u 1R=L;1V(t.D>0&&1R!=t){u r=[];1R=t;t=6.2Q(t).1E(/^\\/\\//i,"");u 3t=W;J(u i=0;i<6.3u.D;i+=2){l(3t)5e;u 2p=1m 3V("^("+6.3u[i]+")");u m=2p.3c(t);l(m){r=R=6.2C(R,6.3u[i+1]);t=6.2Q(t.1E(2p,""));3t=P}}l(!3t){l(!t.1f(",")||!t.1f("|")){l(R[0]==1u)R.3S();1N=6.1Q(1N,R);r=R=[1u];t=" "+t.2Z(1,t.D)}G{u 4H=/^([#.]?)([a-5c-9\\\\*3W-]*)/i;u m=4H.3c(t);l(m[1]=="#"){u 3R=Y.5b(m[2]);r=R=3R?[3R]:[];t=t.1E(4H,"")}G{l(!m[2]||m[1]==".")m[2]="*";J(u i=0;i<R.D;i++)r=6.1Q(r,m[2]=="*"?6.4k(R[i]):R[i].51(m[2]))}}}l(t){u 1K=6.18(t,r);R=r=1K.r;t=6.2Q(1K.t)}}l(R&&R[0]==1u)R.3S();1N=6.1Q(1N,R);v 1N},4k:q(o,r){r=r||[];u s=o.2x;J(u i=0;i<s.D;i++)l(s[i].1G==1){r.1q(s[i]);6.4k(s[i],r)}v r},1r:q(I,19,11){u 2e={"J":"6L","6N":"1e","3y":6.T.1n?"3b":"2B",2B:6.T.1n?"3b":"2B",31:"31",1e:"1e",11:"11",2O:"2O",2U:"2U",6P:"7t"};l(19=="1g"&&6.T.1n&&11!=Q){I[\'6Q\']=1;l(11==1)v I["18"]=I["18"].1E(/3o\\([^\\)]*\\)/54,"");G v I["18"]=I["18"].1E(/3o\\([^\\)]*\\)/54,"")+"3o(1g="+11*55+")"}G l(19=="1g"&&6.T.1n){v I["18"]?3T(I["18"].6S(/3o\\(1g=(.*)\\)/)[1])/55:1}l(19=="1g"&&6.T.33&&11==1)11=0.6U;l(2e[19]){l(11!=Q)I[2e[19]]=11;v I[2e[19]]}G l(11==Q&&6.T.1n&&I.2t&&I.2t.2d()==\'7l\'&&(19==\'7k\'||19==\'6X\')){v I.6Y(19).56}G l(I.6Z){l(11!=Q)I.7f(19,11);v I.48(19)}G{19=19.1E(/-([a-z])/71,q(z,b){v b.2d()});l(11!=Q)I[19]=11;v I[19]}},58:["\\\\[ *(@)S *([!*$^=]*) *(\'?\\"?)(.*?)\\\\4 *\\\\]","(\\\\[)\\s*(.*?)\\s*\\\\]","(:)S\\\\(\\"?\'?([^\\\\)]*?)\\"?\'?\\\\)","([:.#]*)S"],18:q(t,r,2q){u g=2q!==W?6.2P:q(a,f){v 6.2P(a,f,P)};1V(t&&/^[a-z[({<*:.#]/i.1U(t)){u p=6.58;J(u i=0;i<p.D;i++){u 2p=1m 3V("^"+p[i].1E("S","([a-z*3W-][a-5c-73-]*)"),"i");u m=2p.3c(t);l(m){l(!i)m=["",m[1],m[3],m[2],m[5]];t=t.1E(2p,"");3Y}}l(m[1]==":"&&m[2]=="2q")r=6.18(m[3],r,W).r;G{u f=6.2z[m[1]];l(f.14!=3X)f=6.2z[m[1]][m[2]];4c("f = q(a,i){"+(m[1]=="@"?"z=6.1r(a,m[3]);":"")+"v "+f+"}");r=g(r,f)}}v{r:r,t:t}},2Q:q(t){v t.1E(/^\\s+|\\s+$/g,"")},3q:q(I){u 3Z=[];u 1c=I.1i;1V(1c&&1c!=Y){3Z.1q(1c);1c=1c.1i}v 3Z},1B:q(I,2h,2q){u 12=[];l(I){u 2g=I.1i.2x;J(u i=0;i<2g.D;i++){l(2q===P&&2g[i]==I)5e;l(2g[i].1G==1)12.1q(2g[i]);l(2g[i]==I)12.n=12.D-1}}v 6.1y(12,{1R:12.n==12.D-1,1c:2h=="5f"&&12.n%2==0||2h=="5g"&&12.n%2||12[2h]==I,4h:12[12.n-1],3s:12[12.n+1]})},1Q:q(2j,3a){u 1D=[];J(u k=0;k<2j.D;k++)1D[k]=2j[k];J(u i=0;i<3a.D;i++){u 40=P;J(u j=0;j<2j.D;j++)l(3a[i]==2j[j])40=W;l(40)1D.1q(3a[i])}v 1D},2P:q(12,C,42){l(1l C=="24")C=1m 1A("a","i","v "+C);u 1D=[];J(u i=0;i<12.D;i++)l(!42&&C(12[i],i)||42&&!C(12[i],i))1D.1q(12[i]);v 1D},2C:q(12,C){l(1l C=="24")C=1m 1A("a","v "+C);u 1D=[];J(u i=0;i<12.D;i++){u 1K=C(12[i],i);l(1K!==L&&1K!=Q){l(1K.14!=2o)1K=[1K];1D=6.1Q(1D,1K)}}v 1D},F:{29:q(O,B,1L){l(6.T.1n&&O.4d!=Q)O=1x;l(!1L.2s)1L.2s=7.2s++;l(!O.1H)O.1H={};u 2L=O.1H[B];l(!2L){2L=O.1H[B]={};l(O["2K"+B])2L[0]=O["2K"+B]}2L[1L.2s]=1L;O["2K"+B]=7.5n;l(!7.1k[B])7.1k[B]=[];7.1k[B].1q(O)},2s:1,1k:{},28:q(O,B,1L){l(O.1H)l(B&&O.1H[B])l(1L)5m O.1H[B][1L.2s];G J(u i 1z O.1H[B])5m O.1H[B][i];G J(u j 1z O.1H)7.28(O,j)},1J:q(B,H,O){H=$.1Q([],H||[]);l(!O){u g=7.1k[B];l(g)J(u i=0;i<g.D;i++)7.1J(B,H,g[i])}G l(O["2K"+B]){H.5p(7.2e({B:B,1T:O}));O["2K"+B].17(O,H)}},5n:q(F){l(1l 6=="Q")v W;F=6.F.2e(F||1x.F||{});l(!F)v W;u 3r=P;u c=7.1H[F.B];u 1h=[].5o.5a(15,1);1h.5p(F);J(u j 1z c){l(c[j].17(7,1h)===W){F.32();F.3i();3r=W}}l(6.T.1n)F.1T=F.32=F.3i=L;v 3r},2e:q(F){l(6.T.1n){l(F.5r)F.1T=F.5r;u e=Y.47,b=Y.7a;F.7c=F.7d+(e.5s||b.5s);F.7e=F.7g+(e.5t||b.5t)}G l(6.T.2l&&F.1T.1G==3){F=6.1y({},F);F.1T=F.1T.1i}l(!F.32)F.32=q(){7.3r=W};l(!F.3i)F.3i=q(){7.7h=P};v F}}});1m q(){u b=7i.7j.4A();6.T={2l:/5v/.1U(b),30:/30/.1U(b),1n:/1n/.1U(b)&&!/30/.1U(b),33:/33/.1U(b)&&!/(7m|5v)/.1U(b)};6.7n=!6.T.1n||Y.7o=="7p"};6.2c={2w:{5x:"5y",7q:"5z",2M:"5A",7s:"5C"},1a:"3J,27,7u,5D,2W,3y,43,7x,7y".3B(","),18:["5F","5G","5H","5I"],1r:{1K:"11",3D:"31",35:L,7z:L,19:L,7A:L,3w:L,7C:L},5J:{5L:"a.1i",7D:6.3q,3q:6.3q,3s:"6.1B(a).3s",4h:"6.1B(a).4h",2g:"6.1B(a, L, P)",7E:"6.1B(a.26)"},V:{5N:q(1I){6.1r(7,1I,"");7.7F(1I)},1s:q(){7.1o.1b=7.2G?7.2G:"";l(6.1a(7,"1b")=="1O")7.1o.1b="2r"},1p:q(){7.2G=7.2G||6.1a(7,"1b");l(7.2G=="1O")7.2G="2r";7.1o.1b="1O"},3h:q(){6(7)[6(7).4s(":1Y")?"1s":"1p"].17(6(7),15)},7H:q(c){6.1e.29(7,c)},7I:q(c){6.1e.28(7,c)},7J:q(c){6.1e[6.1e.3k(7,c)?"28":"29"](7,c)},28:q(a){l(!a||6.18(a,[7]).r)7.1i.3v(7)},5P:q(){1V(7.26)7.3v(7.26)},34:q(B,C){6.F.29(7,B,C)},4B:q(B,C){6.F.28(7,B,C)},1J:q(B,H){6.F.1J(B,H,7)}}};6.5R();6.C.1y({5S:6.C.3h,3h:q(a,b){v a&&b&&a.14==1A&&b.14==1A?7.5X(q(e){7.1R=7.1R==a?b:a;e.32();v 7.1R.17(7,[e])||W}):7.5S.17(7,15)},7M:q(f,g){q 4r(e){u p=(e.B=="39"?e.7N:e.7Q)||e.7R;1V(p&&p!=7)37{p=p.1i}3e(e){p=7};l(p==7)v W;v(e.B=="39"?f:g).17(7,[e])}v 7.39(4r).60(4r)},21:q(f){l(6.3d)f.17(Y);G{6.2y.1q(f)}v 7}});6.1y({3d:W,2y:[],21:q(){l(!6.3d){6.3d=P;l(6.2y){J(u i=0;i<6.2y.D;i++)6.2y[i].17(Y);6.2y=L}l(6.T.33||6.T.30)Y.7U("65",6.21,W)}}});1m q(){u e=("7V,7X,2Y,7Y,80,4D,5X,82,"+"83,84,85,39,60,86,4v,3E,"+"4x,8a,8b,8d,2E").3B(",");J(u i=0;i<e.D;i++)1m q(){u o=e[i];6.C[o]=q(f){v f?7.34(o,f):7.1J(o)};6.C["8e"+o]=q(f){v 7.4B(o,f)};6.C["8g"+o]=q(f){u O=6(7);u 1L=q(){O.4B(o,1L);O=L;v f.17(7,15)};v 7.34(o,1L)}};l(6.T.33||6.T.30){Y.8l("65",6.21,W)}G l(6.T.1n){Y.8o("<8r"+"8u 35=59 8v=P "+"3w=//:><\\/1Z>");u 1Z=Y.5b("59");l(1Z)1Z.2H=q(){l(7.38!="1t")v;7.1i.3v(7);6.21()};1Z=L}G l(6.T.2l){6.3L=4d(q(){l(Y.38=="6d"||Y.38=="1t"){5j(6.3L);6.3L=L;6.21()}},10)}6.F.29(1x,"2Y",6.21)};l(6.T.1n)6(1x).4D(q(){u F=6.F,1k=F.1k;J(u B 1z 1k){u 3N=1k[B],i=3N.D;l(i>0)6m l(B!=\'4D\')F.28(3N[i-1],B);1V(--i)}});6.C.1y({4M:6.C.1s,1s:q(16,K){v 16?7.22({27:"1s",3J:"1s",1g:"1s"},16,K):7.4M()},4P:6.C.1p,1p:q(16,K){v 16?7.22({27:"1p",3J:"1p",1g:"1p"},16,K):7.4P()},6r:q(16,K){v 7.22({27:"1s"},16,K)},6s:q(16,K){v 7.22({27:"1p"},16,K)},6u:q(16,K){v 7.V(q(){u 4T=6(7).4s(":1Y")?"1s":"1p";6(7).22({27:4T},16,K)})},6x:q(16,K){v 7.22({1g:"1s"},16,K)},6y:q(16,K){v 7.22({1g:"1p"},16,K)},6B:q(16,2w,K){v 7.22({1g:2w},16,K)},22:q(E,16,K){v 7.1w(q(){7.2S=6.1y({},E);J(u p 1z E){u e=1m 6.2X(7,6.16(16,K),p);l(E[p].14==4Y)e.2v(e.1c(),E[p]);G e[E[p]](E)}})},1w:q(B,C){l(!C){C=B;B="2X"}v 7.V(q(){l(!7.1w)7.1w={};l(!7.1w[B])7.1w[B]=[];7.1w[B].1q(C);l(7.1w[B].D==1)C.17(7)})}});6.1y({16:q(s,o){o=o||{};l(o.14==1A)o={1t:o};u 4Z={6E:6G,6I:4I};o.2J=(s&&s.14==4Y?s:4Z[s])||53;o.3x=o.1t;o.1t=q(){6.52(7,"2X");l(o.3x&&o.3x.14==1A)o.3x.17(7)};v o},1w:{},52:q(I,B){B=B||"2X";l(I.1w&&I.1w[B]){I.1w[B].3S();u f=I.1w[B][0];l(f)f.17(I)}},2X:q(I,2A,E){u z=7;z.o={2J:2A.2J||53,1t:2A.1t,2u:2A.2u};z.U=I;u y=z.U.1o;u 44=6.1a(z.U,\'1b\');y.1b="2r";y.43="1Y";z.a=q(){l(2A.2u)2A.2u.17(I,[z.2f]);l(E=="1g")6.1r(y,"1g",z.2f);G l(5w(z.2f))y[E]=5w(z.2f)+"6V"};z.57=q(){v 3T(6.1a(z.U,E))};z.1c=q(){u r=3T(6.3j(z.U,E));v r&&r>-70?r:z.57()};z.2v=q(4C,2w){z.4e=(1m 5h()).5i();z.2f=4C;z.a();z.41=4d(q(){z.2u(4C,2w)},13)};z.1s=q(){l(!z.U.1v)z.U.1v={};z.U.1v[E]=7.1c();z.o.1s=P;z.2v(0,z.U.1v[E]);l(E!="1g")y[E]="5d"};z.1p=q(){l(!z.U.1v)z.U.1v={};z.U.1v[E]=7.1c();z.o.1p=P;z.2v(z.U.1v[E],0)};z.3h=q(){l(!z.U.1v)z.U.1v={};z.U.1v[E]=7.1c();l(44==\'1O\'){z.o.1s=P;l(E!="1g")y[E]="5d";z.2v(0,z.U.1v[E])}G{z.o.1p=P;z.2v(z.U.1v[E],0)}};z.2u=q(4l,4f){u t=(1m 5h()).5i();l(t>z.o.2J+z.4e){5j(z.41);z.41=L;z.2f=4f;z.a();z.U.2S[E]=P;u 1N=P;J(u i 1z z.U.2S)l(z.U.2S[i]!==P)1N=W;l(1N){y.43=\'\';y.1b=44;l(6.1a(z.U,\'1b\')==\'1O\')y.1b=\'2r\';l(z.o.1p)y.1b=\'1O\';l(z.o.1p||z.o.1s)J(u p 1z z.U.2S)l(p=="1g")6.1r(y,p,z.U.1v[p]);G y[p]=\'\'}l(1N&&z.o.1t&&z.o.1t.14==1A)z.o.1t.17(z.U)}G{u p=(t-7.4e)/z.o.2J;z.2f=((-5B.7r(p*5B.7v)/2)+0.5)*(4f-4l)+4l;z.a()}}}});6.C.1y({7B:q(N,1P,K){7.2Y(N,1P,K,1)},2Y:q(N,1P,K,1F){l(N.14==1A)v 7.34("2Y",N);K=K||q(){};u B="67";l(1P){l(1P.14==1A){K=1P;1P=L}G{1P=6.3g(1P);B="62"}}u 4i=7;6.3C({N:N,B:B,H:1P,1F:1F,1t:q(2F,1d){l(1d=="2k"||!1F&&1d=="5u"){4i.3D(2F.3p).4y().V(K,[2F.3p,1d,2F])}G K.17(4i,[2F.3p,1d,2F])}});v 7},7G:q(){v 6.3g(7)},4y:q(){v 7.1X(\'1Z\').V(q(){l(7.3w)6.61(7.3w);G{6.4u(7.2D||7.7K||7.31||"")}}).4m()}});l(6.T.1n&&1l 3f=="Q")3f=q(){v 1m 7O("7S.7T")};1m q(){u e="4S,5O,5M,5K,5E,5q".3B(",");J(u i=0;i<e.D;i++)1m q(){u o=e[i];6.C[o]=q(f){v 7.34(o,f)}}};6.1y({1S:q(N,H,K,B,1F){l(H&&H.14==1A){K=H;H=L}6.3C({N:N,H:H,2k:K,3K:B,1F:1F})},87:q(N,H,K,B){6.1S(N,H,K,B,1)},61:q(N,K){l(K)6.1S(N,L,K,"1Z");G{6.1S(N,L,L,"1Z")}},8c:q(N,H,K){6.1S(N,H,K,"5Q")},8f:q(N,H,K,B){6.3C({B:"62",N:N,H:H,2k:K,3K:B})},1M:0,8i:q(1M){6.1M=1M},3A:{},3C:q(s){s=6.1y({1k:P,1F:W,B:"67",1M:6.1M,1t:L,2k:L,2E:L,3K:L,N:L,H:L,50:"8w/x-6b-6f-6i",4J:P,4U:P,46:L},s);l(s.H){l(s.4J&&1l s.H!=\'24\')s.H=6.3g(s.H);l(s.B.4A()=="1S")s.N+=((s.N.1f("?")>-1)?"&":"?")+s.H}l(s.1k&&!6.4z++)6.F.1J("4S");u 4q=W;u M=1m 3f();M.6z(s.B,s.N,s.4U);l(s.H)M.3l("6F-6J",s.50);l(s.1F)M.3l("6K-4t-6M",6.3A[s.N]||"6O, 6R 6T 6W 3U:3U:3U 72");M.3l("X-74-75","3f");l(M.76)M.3l("77","79");l(s.46)s.46(M);l(s.1k)6.F.1J("5q",[M,s]);u 2H=q(4b){l(M&&(M.38==4||4b=="1M")){4q=P;u 1d=6.63(M)&&4b!="1M"?s.1F&&6.4K(M,s.N)?"5u":"2k":"2E";l(1d!="2E"){u 3m;37{3m=M.45("4R-4t")}3e(e){}l(s.1F&&3m)6.3A[s.N]=3m;u H=6.5k(M,s.3K);l(s.2k)s.2k(H,1d);l(s.1k)6.F.1J("5E",[M,s])}G{l(s.2E)s.2E(M,1d);l(s.1k)6.F.1J("5K",[M,s])}l(s.1k)6.F.1J("5M",[M,s]);l(s.1k&&!--6.4z)6.F.1J("5O");l(s.1t)s.1t(M,1d);M.2H=q(){};M=L}};M.2H=2H;l(s.1M>0)5Z(q(){l(M){M.7P();l(!4q)2H("1M");M=L}},s.1M);M.88(s.H);v M},4z:0,63:q(r){37{v!r.1d&&8j.8m=="4G:"||(r.1d>=4I&&r.1d<6g)||r.1d==5U||6.T.2l&&r.1d==Q}3e(e){}v W},4K:q(M,N){37{u 4X=M.45("4R-4t");v M.1d==5U||4X==6.3A[N]||6.T.2l&&M.1d==Q}3e(e){}v W},5k:q(r,B){u 4a=r.45("7b-B");u H=!B&&4a&&4a.1f("M")>=0;H=B=="M"||H?r.7w:r.3p;l(B=="1Z"){6.4u(H)}l(B=="5Q")4c("H = "+H);l(B=="3D")6("<2b>").3D(H).4y();v H},3g:q(a){u s=[];l(a.14==2o||a.3n){J(u i=0;i<a.D;i++)s.1q(a[i].19+"="+4g(a[i].11))}G{J(u j 1z a){l(a[j].14==2o){J(u k=0;k<a[j].D;k++){s.1q(j+"="+4g(a[j][k]))}}G{s.1q(j+"="+4g(a[j]))}}}v s.4N("&")},4u:q(H){l(1x.5l)1x.5l(H);G l(6.T.2l)1x.5Z(H,0);G 4c.5a(1x,H)}})}',62,529,'||||||jQuery|this||||||||||||||if|||||function||||var|return||||||type|fn|length|prop|event|else|data|elem|for|callback|null|xml|url|element|true|undefined|ret||browser|el|each|false||document|||value|elems||constructor|arguments|speed|apply|filter|name|css|display|cur|status|className|indexOf|opacity|args|parentNode|obj|global|typeof|new|msie|style|hide|push|attr|show|complete|context|orig|queue|window|extend|in|Function|sibling|arg|result|replace|ifModified|nodeType|events|key|trigger|val|handler|timeout|done|none|params|merge|last|get|target|test|while|wrap|find|hidden|script|old|ready|animate|table|string|tbody|firstChild|height|remove|add|set|div|macros|toUpperCase|fix|now|siblings|pos|pushStack|first|success|safari|fn2|stack|Array|re|not|block|guid|nodeName|step|custom|to|childNodes|readyList|expr|options|cssFloat|map|text|error|res|oldblock|onreadystatechange|parPos|duration|on|handlers|insertBefore|classes|disabled|grep|trim|num|curAnim|dir|checked|domManip|position|fx|load|substr|opera|innerHTML|preventDefault|mozilla|bind|id|oWidth|try|readyState|mouseover|second|styleFloat|exec|isReady|catch|XMLHttpRequest|param|toggle|stopPropagation|curCSS|has|setRequestHeader|modRes|jquery|alpha|responseText|parents|returnValue|next|foundToken|token|removeChild|src|oldComplete|float|child|lastModified|split|ajax|html|select|clean|oHeight|defaultView|cloneNode|width|dataType|safariTimer|static|els|swap|getComputedStyle|tr|oid|shift|parseFloat|00|RegExp|_|String|break|matched|noCollision|timer|inv|overflow|oldDisplay|getResponseHeader|beforeSend|documentElement|getAttribute|appendChild|ct|isTimeout|eval|setInterval|startTime|lastNum|encodeURIComponent|prev|self|button|getAll|firstNum|end|radio|selected|visibility|requestDone|handleHover|is|Modified|globalEval|reset|currentStyle|submit|evalScripts|active|toLowerCase|unbind|from|unload|deep|clone|file|re2|200|processData|httpNotModified|force|_show|join|getPropertyValue|_hide|newProp|Last|ajaxStart|state|async|image|input|xmlRes|Number|ss|contentType|getElementsByTagName|dequeue|400|gi|100|nodeValue|max|parse|__ie_init|call|getElementById|z0|1px|continue|even|odd|Date|getTime|clearInterval|httpData|execScript|delete|handle|slice|unshift|ajaxSend|srcElement|scrollLeft|scrollTop|notmodified|webkit|parseInt|appendTo|append|prepend|before|Math|after|left|ajaxSuccess|eq|lt|gt|contains|axis|ajaxError|parent|ajaxComplete|removeAttr|ajaxStop|empty|json|init|_toggle|checkbox|304|nth|password|click|createElement|setTimeout|mouseout|getScript|POST|httpSuccess|array|DOMContentLoaded|size|GET|initDone|splice|Top|www|border|loaded|padding|form|300|Width|urlencoded|Left|offsetWidth|absolute|do|clientHeight|clientWidth|Bottom|Right|slideDown|slideUp|thead|slideToggle|td|th|fadeIn|fadeOut|open|only|fadeTo|visible|enabled|slow|Content|600|textarea|fast|Type|If|htmlFor|Since|class|Thu|readonly|zoom|01|match|Jan|9999|px|1970|method|getAttributeNode|tagName|10000|ig|GMT|9_|Requested|With|overrideMimeType|Connection|offsetHeight|close|body|content|pageX|clientX|pageY|setAttribute|clientY|cancelBubble|navigator|userAgent|action|FORM|compatible|boxModel|compatMode|CSS1Compat|prependTo|cos|insertAfter|readOnly|top|PI|responseXML|color|background|title|href|loadIfModified|rel|ancestors|children|removeAttribute|serialize|addClass|removeClass|toggleClass|textContent|nextSibling|hover|fromElement|ActiveXObject|abort|toElement|relatedTarget|Microsoft|XMLHTTP|removeEventListener|blur|pop|focus|resize|toString|scroll|createTextNode|dblclick|mousedown|mouseup|mousemove|change|getIfModified|send|opt|keydown|keypress|getJSON|keyup|un|post|one|prototype|ajaxTimeout|location|index|addEventListener|protocol|Boolean|write|TABLE|THEAD|scr|relative|right|ipt|defer|application'.split('|'),0,{}))