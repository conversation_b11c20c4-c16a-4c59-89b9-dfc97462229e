﻿<div class="post cate6 auth1">
	<h4 class="post-date">2007年9月10日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/37.html">局域网消息工具</a></h2>
	<div class="post-body">用于局域网的消息发送和接受，使用非常方便，设置完地址就可以使用了，不过需要开启 messenger 服务，因为完全依赖系统组件和服务，相当于就是提供了一个 UI 而已，所以不会对系统造成任何影响。眼睛好的可能早看出来这个小工具其实是一套维护工具里面的一个组件；我仅仅对界面进行了一些修改，去掉了 发送 旁边的 退出 按钮，因为离 发送 太近容易误操作，也调整了一些编辑框的大小，使其看起来非常整齐。[IMG]upload/20070918175544862...</div>
	<h5 class="post-tags"><#ZC_MSG138#>: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		<#ZC_MSG011#>:starain | <#ZC_MSG012#>:一些作品 | <#ZC_MSG013#>:0 | <#ZC_MSG014#>:0 | <#ZC_MSG130#>:<span id="spn37"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn37=37,"</script>
	</h6>
</div>