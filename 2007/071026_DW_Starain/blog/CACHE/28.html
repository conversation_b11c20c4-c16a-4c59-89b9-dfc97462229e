﻿<div class="post cate6 auth1">
	<h4 class="post-date">2007年4月21日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/28.html">网页改版，主要针对页面宽度</a></h2>
	<div class="post-body">由于越来越多的计算机使用更高分辨率的显示器，以前常用于 800&times;600 分辨率的网页，现在与我们普遍使用的 1024&times;768 和 1280&times;1024 不大相衬，于是我使用动态页面宽度对以往 800&times;600 的页面进行了改版重排使其能够自动适应多种平面分辨率。其主要难点在于固定宽度图片的伸缩问题，为此，我选取了小部分近似循环出现的图片内容作为动态宽度区域的背景部分，然后对该部分做了稍微修饰，整体效果很好，但如果仔细去观察，会发现那一小部分是重复循环出...</div>
	<h5 class="post-tags"><#ZC_MSG138#>: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%BD%91%E9%A1%B5%E5%88%B6%E4%BD%9C">网页制作</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		<#ZC_MSG011#>:starain | <#ZC_MSG012#>:一些作品 | <#ZC_MSG013#>:0 | <#ZC_MSG014#>:0 | <#ZC_MSG130#>:<span id="spn28"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn28=28,"</script>
	</h6>
</div>