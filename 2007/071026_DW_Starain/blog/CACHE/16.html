﻿<div class="post cate6 auth1">
	<h4 class="post-date">2006年7月7日</h4>
	<h2 class="post-title"><a href="http://starain.net.cn/blog/post/16.html">图书管理系统界面</a></h2>
	<div class="post-body">这是我大学数据库考试的题目，说是让做一个数据库管理系统，可以自己选编程语言，我选了 Microsoft Visual Basic 2005 Express Edition 这是当时微软为学生提供的免费使用的一个编译平台，属于 Visual Studio 2005 家族，且先不说编程内容的技术问题，现在看来我倒觉得当时使用这么大的按钮倒是很新鲜，因为从来没有看到有软件这么做的，当时就觉得操作方便呗。时隔一年后的今天，似乎这种设计已经很普遍了，随着 Vista 的推广，我发现越来越多的软件中运...</div>
	<h5 class="post-tags"><#ZC_MSG138#>: <a href="http://starain.net.cn/blog/catalog.asp?tags=%E7%95%8C%E9%9D%A2%E8%AE%BE%E8%AE%A1">界面设计</a>&nbsp;&nbsp;</h5>
	<h6 class="post-footer">
		<#ZC_MSG011#>:starain | <#ZC_MSG012#>:一些作品 | <#ZC_MSG013#>:0 | <#ZC_MSG014#>:0 | <#ZC_MSG130#>:<span id="spn16"></span>
		<script language="JavaScript" type="text/javascript">strBatchView+="spn16=16,"</script>
	</h6>
</div>