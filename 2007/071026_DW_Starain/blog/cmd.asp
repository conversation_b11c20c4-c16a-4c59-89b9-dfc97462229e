<%@ CODEPAGE=65001 %>
<%
'///////////////////////////////////////////////////////////////////////////////
'//              Z-Blog
'// 作    者:    朱煊(zx.asd)
'// 版权所有:    RainbowSoft Studio
'// 技术支持:    <EMAIL>
'// 程序名称:    
'// 程序版本:    
'// 单元名称:    cmd.asp
'// 开始时间:    2004.07.27
'// 最后修改:    
'// 备    注:    命令执行&跳转页
'///////////////////////////////////////////////////////////////////////////////
%>
<% Option Explicit %>
<% On Error Resume Next %>
<% Response.Charset="UTF-8" %>
<% Response.Buffer=True %>
<!-- #include file="c_option.asp" -->
<!-- #include file="function/c_function.asp" -->
<!-- #include file="function/c_function_md5.asp" -->
<!-- #include file="function/c_system_lib.asp" -->
<!-- #include file="function/c_system_base.asp" -->
<!-- #include file="function/c_system_event.asp" -->
<!-- #include file="function/rss_lib.asp" -->
<!-- #include file="function/atom_lib.asp" -->
<%
Call System_Initialize()

Dim strAct
strAct=Request.QueryString("act")

'如果不是"接收引用"就要检查非法链接
If (strAct<>"tb") And (strAct<>"search") Then Call CheckReference("")

'权限检查
If Not CheckRights(strAct) Then Call ShowError(6)


Select Case strAct

	'命令列表

	Case "login" 

		Call BlogLogin()

	Case "verify"

		Call BlogVerify()

	Case "logout"

		Call BlogLogout()

	Case "admin" 

		Call BlogAdmin()

	Case "cmt"

		Call CommentPost()

	Case "tb"
		Call TrackBackPost()

	Case "vrs"
		Call ViewRights()

	Case "ArticleMng"

		Call ArticleMng()

	Case "ArticleEdt"

		Call ArticleEdt()

	Case "ArticlePst"

		Call ArticlePst()

	Case "ArticleDel"

		Call ArticleDel()

	Case "CategoryMng"

		Call CategoryMng()

	Case "CategoryEdt"

		Call CategoryEdt()

	Case "CategoryPst"

		Call CategoryPst()

	Case "CategoryDel"

		Call CategoryDel()

	Case "CommentMng"

		Call CommentMng()

	Case "CommentDel"

		Call CommentDel()

	Case "CommentRev"

		Call CommentRev()

	Case "CommentEdt"

		Call CommentEdt()

	Case "CommentSav"

		Call CommentSav()

	Case "TrackBackMng"

		Call TrackBackMng()

	Case "TrackBackDel"

		Call TrackBackDel()

	Case "TrackBackSnd"

		Call TrackBackSnd()

	Case "UserMng"

		Call UserMng()

	Case "UserCrt"

		Call UserCrt()

	Case "UserEdt"

		Call UserEdt()

	Case "UserDel"

		Call UserDel()

	Case "FileReBuild"

		Call FileReBuild()

	Case "FileMng"

		Call FileMng()

	Case "FileSnd"

		Call FileSnd()

	Case "FileUpload"

		Call FileUpload()

	Case "FileDel"

		Call FileDel()

	Case "BlogMng"

		Call BlogMng()

	Case "BlogReBuild"

		Call BlogReBuild()

	Case "Search"

		Call Search()

	Case "SettingMng"

		Call SettingMng()

	Case "SettingSav"

		Call SettingSav()

	Case "TagMng"

		Call TagMng()

	Case "TagEdt"

		Call TagEdt()

	Case "TagPst"

		Call TagPst()

	Case "TagDel"

		Call TagDel()

	Case "KeyWordMng"

		Call KeyWordMng()

	Case "KeyWordEdt"

		Call KeyWordEdt()

	Case "KeyWordPst"

		Call KeyWordPst()

	Case "KeyWordDel"

		Call KeyWordDel()

	Case "ExportXml"

		Call ExportXml()

	Case "PlugInMng"

		Call PlugInMng()

	Case "SiteInfo"

		Call SiteInfo()

	Case "SiteFileMng"

		Call SiteFileMng()

	Case "SiteFileEdt"

		Call SiteFileEdt()

	Case "SiteFilePst"

		Call SiteFilePst()

	Case "SiteFileDel"

		Call SiteFileDel()

	Case "AskFileReBuild"

		Call AskFileReBuild()

	Case "gettburl"
		Call TrackBackUrlGet()

	Case "CommentDelBatch"

		Call CommentDelBatch()

	Case "TrackBackDelBatch"

		Call TrackBackDelBatch()

	Case "FileDelBatch"

		Call FileDelBatch()

	Case "Update"

		Call Update()

End Select



Function BlogLogin
	If BlogUser.Verify=False Then
		Response.Redirect "login.asp"
	Else
		Call BlogAdmin()
	End If
End Function

Function BlogVerify()
	Call Login
End Function

Function BlogLogout
	Call Logout
End Function

Function BlogAdmin

	Call ClearGlobeCache
	Call LoadGlobeCache

	Response.Redirect "admin/admin_default.asp"

End Function

Function ViewRights()
	Call ListUser_Rights()
End Function


Function ArticleMng
	Response.Redirect "admin/admin.asp?act=ArticleMng&page=" & Request.QueryString("id")
End Function

Function ArticleEdt
	If Request.QueryString("type")<>"" Then
		If IsEmpty(Request.QueryString("id"))=False Then Response.Redirect "admin/edit_"& ZC_BLOG_WEBEDIT &".asp?id="& Request.QueryString("id") Else Response.Redirect "admin/edit_"& ZC_BLOG_WEBEDIT &".asp"
	Else
		If IsEmpty(Request.QueryString("id"))=False Then Response.Redirect "admin/edit.asp?id="& Request.QueryString("id") Else Response.Redirect "admin/edit.asp"
	End If
End Function

Function ArticlePst

	If PostArticle Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=ArticleMng"
	Else
		Call ShowError(11)
	End If
End Function

Function ArticleDel

	If DelArticle(Request.QueryString("id")) Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=ArticleMng"
	Else
		Call ShowError(11)
	End If
End Function


Function CategoryMng
	Response.Redirect "admin/admin.asp?act=CategoryMng&page=" & Request.QueryString("id")
End Function

Function CategoryEdt

	If IsEmpty(Request.QueryString("id"))=False Then
		Response.Redirect "admin/edit_catalog.asp?id=" & Request.QueryString("id")
	Else
		Response.Redirect "admin/edit_catalog.asp"
	End If
End Function

Function CategoryPst

	If PostCategory Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=CategoryMng"
	Else
		Call ShowError(12)
	End If
End Function

Function CategoryDel

	If DelCategory(Request.QueryString("id")) Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=CategoryMng"
	Else
		Call ShowError(12)
	End If
End Function


Function CommentMng
	Response.Redirect "admin/admin.asp?act=CommentMng&page=" & Request.QueryString("id")
End Function

Function CommentPost

	If PostComment(Request.QueryString("key")) Then
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect Request.Form("inpLocation")
	Else
		Call ShowError(14)
	End If
End Function

Function CommentDel

	If DelComment(Request.QueryString("id"),Request.QueryString("log_id")) Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=CommentMng"
	Else
		Call ShowError(18)
	End If
End Function

Function CommentRev

	If RevertComment(Request.QueryString("key"),Request.QueryString("id")) Then
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect Request.Form("inpLocation")
	Else
		Call ShowError(14)
	End If
End Function


Function CommentEdt

	Response.Redirect "admin/edit_comment.asp?id="& Request.QueryString("id") & "&log_id="& Request.QueryString("log_id") 

End Function


Function CommentSav

	If SaveComment(Request.Form("edtID"),Request.Form("inpID")) Then
		Call SetBlogHint(True,Empty,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=CommentMng"
	Else
		Call ShowError(42)
	End If

End Function


Function TrackBackMng
	Response.Redirect "admin/admin.asp?act=TrackBackMng&page=" & Request.QueryString("id")
End Function

Function TrackBackPost
	Call PostTrackBack(Request.QueryString("id"),Request.QueryString("key"))
End Function

Function TrackBackDel

	If DelTrackBack(Request.QueryString("id"),Request.QueryString("log_id")) Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=TrackBackMng"
	Else
		Call ShowError(19)
	End If
End Function

Function TrackBackSnd
	If SendTrackBack() Then
		Call SetBlogHint(True,Empty,Empty)
		Response.Redirect "cmd.asp?act=ArticleMng"
	Else
		Call ShowError(20)
	End If
End Function


Function UserMng()
		Response.Redirect "admin/admin.asp?act=UserMng&page=" & Request.QueryString("id")
End Function

Function UserCrt()
	If EditUser Then
		Call SetBlogHint(True,True,Empty)
		Response.Redirect "cmd.asp?act=UserMng"
	Else
		Call ShowError(16)
	End If
End Function

Function UserEdt()
	If EditUser Then
		Call SetBlogHint(True,True,Empty)
		Response.Redirect "cmd.asp?act=UserMng"
	Else
		Call ShowError(16)
	End If
End Function

Function UserDel()
	If DelUser(Request.QueryString("id")) Then
		Call SetBlogHint(True,True,Empty)
		Response.Redirect "cmd.asp?act=UserMng"
	Else
		Call ShowError(17)
	End If
End Function


Function FileMng()
	Response.Redirect "admin/admin.asp?act=FileMng&page=" & Request.QueryString("id")
End Function

Function FileSnd()
	Call SendFile()
End Function

Function FileUpload()
	Server.ScriptTimeout = 1200
	If UploadFile(CBool(Request.QueryString("autoname")),CBool(Request.QueryString("reload"))) Then
		Call SetBlogHint(True,Empty,Empty)
		Response.Redirect "admin/admin.asp?act=FileMng&page=" & Request.QueryString("id")
	Else
		Call ShowError(21)
	End If
End Function

Function FileDel()
	If DelFile(Request.QueryString("id")) Then
		Call SetBlogHint(True,Empty,Empty)
		Response.Redirect "cmd.asp?act=FileMng"
	Else
		Call ShowError(22)
	End If
End Function


Function Search()
	RedirectBy301(ZC_BLOG_HOST & "search.asp?q=" & Server.URLEncode(Request.Form("edtSearch")))
End Function

Function SettingMng()
	Call ClearGlobeCache
	Call LoadGlobeCache
	Response.Redirect "admin/edit_setting.asp"
End Function

Function SettingSav()
	If SaveSetting Then
		Call SetBlogHint(True,Empty,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=SettingMng"
	Else
		Call ShowError(25)
	End If
End Function

Function TagMng()
	Response.Redirect "admin/admin.asp?act=TagMng&page=" & Request.QueryString("id")
End Function

Function TagEdt()
	If IsEmpty(Request.QueryString("id"))=False Then
		Response.Redirect "admin/edit_tag.asp?id=" & Request.QueryString("id")
	Else
		Response.Redirect "admin/edit_tag.asp"
	End If
End Function

Function TagPst()
	If PostTag Then
		Call SetBlogHint(True,True,Empty)
		Response.Redirect "cmd.asp?act=TagMng"
	Else
		Call ShowError(35)
	End If
End Function

Function TagDel()
	If DelTag(Request.QueryString("id")) Then
		Call SetBlogHint(True,True,Empty)
		Response.Redirect "cmd.asp?act=TagMng"
	Else
		Call ShowError(36)
	End If
End Function


Function KeyWordMng()
End Function

Function KeyWordEdt()
End Function

Function KeyWordPst()
End Function

Function KeyWordDel()
End Function


Function BlogReBuild()

	Call ClearGlobeCache
	Call LoadGlobeCache

	Server.ScriptTimeout = 1200
	If MakeBlogReBuild Then
		Call ClearGlobeCache
		Call LoadGlobeCache
	Else
		Call ShowError(23)
	End If
End Function

Function FileReBuild()
	Server.ScriptTimeout = 1200
	Call MakeFileReBuild()
End Function

Function BlogMng()
	Response.Redirect "admin/admin.asp?act=BlogMng"
End Function

Function PlugInMng()
	Response.Redirect "admin/admin.asp?act=PlugInMng"
End Function


Function SiteInfo()
	Response.Redirect "admin/admin.asp?act=SiteInfo"
End Function

Function SiteFileMng()
	Response.Redirect "admin/admin.asp?act=SiteFileMng&path=" & Request.QueryString("path") & "&opath=" & Request.QueryString("opath") 
End Function

Function SiteFileEdt()
	Response.Redirect "admin/admin.asp?act=SiteFileEdt&path=" & Request.QueryString("path") 
End Function

Function SiteFilePst()
	If PostSiteFile(Request.QueryString("path")) Then
		Call SetBlogHint(True,Empty,Empty)
		Response.Redirect "admin/admin.asp?act=SiteFileMng"
	End If
End Function

Function SiteFileDel()
	If DelSiteFile(Request.QueryString("path")) Then
		Call SetBlogHint(True,Empty,Empty)
		Response.Redirect "admin/admin.asp?act=SiteFileMng"
	End If
End Function

Function AskFileReBuild()

	Call ClearGlobeCache
	Call LoadGlobeCache

	Response.Redirect "admin/admin.asp?act=AskFileReBuild"
End Function

Function TrackBackUrlGet()
	Call GetRealUrlofTrackBackUrl(Request.QueryString("id"))
End Function

Function CommentDelBatch

	If DelCommentBatch() Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=CommentMng"
	End If
End Function

Function TrackBackDelBatch

	If DelTrackBackBatch() Then
		Call SetBlogHint(True,True,Empty)
		Call ClearGlobeCache
		Call LoadGlobeCache
		Response.Redirect "cmd.asp?act=TrackBackMng"
	End If
End Function

Function FileDelBatch
	If DelFileBatch() Then
		Call SetBlogHint(True,Empty,Empty)
		Response.Redirect "cmd.asp?act=FileMng"
	End If
End Function

Function Update()
	Response.Redirect "admin/update.asp"
End Function


Call System_Terminate()

If Err.Number<>0 then
	Call ShowError(0)
End If
%>