<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>李楠 简历</title>
<style type="text/css">
<!--
body {
	width: 760px;
	height: auto;
	margin: 20px;
}
#header {
	font-family: "黑体", "微软雅黑";
	font-size: 24px;
	text-align: center;
	line-height: 30px;
}
.link {
	font-size: 12px;
	text-align: right;
	color: #666666;
}
.subtitle {
	text-indent: 10px;
	font-family: "黑体", "微软雅黑";
	font-size: 15px;
	background-color: #B2B2B2;
	color: #FFFFFF;
	line-height: 20px;
	clear: both;
	float: none;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: none;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: none;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
}
.content {
	font-size: 13px;
	margin: 10px;
	height: auto;
	color: #000000;
	line-height: 19px;
}
.content   ul {
	clear: right;
	float: left;
	width: auto;
}
-->
</style>
</head>

<body>
<div id="header">
	李楠 个人简历
</div>

<div class="link">
	<script language="JavaScript" type="text/javascript">
		document.write("Last Updated:&nbsp;" + document.lastModified);</script><br />
  <a href="http://starain.net.cn/docx/Resume.html">http://starain.net.cn/docx/Resume.html</a>
</div>

<div class="subtitle">
	基本情况
</div>

<div class="content">
    <ul>姓名：李楠<br/>学历：本科<br/>专业：计算机应用</ul>
	<ul>性别：男<br/>电话：13260306494<br/>出生年月：1986.05</ul>
    <ul>电子信箱：<a href="mailto:<EMAIL>"><EMAIL></a><br/>
		个人站点：<a href="http://starain.net.cn/" target="_blank">http://starain.net.cn/</a><br/>
        毕业院校：解放军重庆通信学院</ul>
</div>

<div class="subtitle">
	求职意向
</div>
<div class="content">
	网页设计 平面设计 网络工程 或相关复合型职位
</div>

<div class="subtitle">
	专业技能
</div>
<div class="content">
	<li>网页方面：<p>熟悉网站建设流程，熟练运用 Macromedia Studio 8 和 Adobe CS3 Web Standard 套件及 FrontPage11 设计制作符合 W3C 标准的静、动态网站；具备良好的美术修养，运用色彩原理设计各种风格的网页效果，运用 asp 内置对象实现用户与服务器端信息交互，制作简单留言板、聊天室、信息发布、身份验证、信息查询、用户注册表单验证提交等功能模块；会用 div＋CSS 对现有网站进行重构或改版；具备手写 XHTML 代码能力；熟练掌握网页优化技巧；熟悉市场潮流和用户习惯，并根据各种需求设计页面，实现交互。</p></li>
	<li>平面方向：<p>熟练运用 Photoshop、Indesign、PageMaker、Illustrator、CorelDraw 等设计软件创作平面图形图像及各种印刷出版物,标识（LOGO）、广告画、竖旗、各种招贴等，熟悉从与客户沟通、创意、设计到实现的完成工作流程，具备图书出版、广告、图文艺术、企业识别（VI）整体设计能力，了解涨色、拼版、发排、打样，行业纸张及印刷全流程（印前输出、印刷工艺、后期加工与成本核算、印刷报价)和后期处理技巧及后期印刷技术如裁切、装订等工艺。 </p>	
	</li>
	<li>网络工程：<p>具备思科网络安装和支持助理能力，熟悉当今主流以太网技术和接入网技术及常见组网方式，能够迅速完成小型办公及家庭办公（SOHO）和普通民用网络工程设计和建设项目；曾深入学习 TCP/IP 协议，了解当前常见网络攻击方式。</p></li>
	<li>其它技能：<p>熟练使用及维护微软系列操作系统和办公软件及常用软件。能对计算机软、硬件故障进行分析与解决；撰写各种技术文档；熟悉常见病毒、恶意程序及流氓软件入侵原理，能采取有效手段避免病毒入侵；熟悉 VB 和 VB.Net 编程语言，能读懂简单的 C 和 C＋＋ 程序代码；对嵌入式操作系统 UC－OSII 有过研究。</p></li>
</div>

<div class="subtitle">
	教育经历
</div>
<div class="content">
    <li>从2007年03月——到2007年05月 东方标准 电脑美术设计师就业班</li>
    <li>从2006年02月——到2006年04月 重庆大学 思科网络技术教育学院</li>
    <li>从2004年11月——到2005年01月 通信学院影像工程 三维动画制作</li>
    <li>从2003年08月——到2007年07月 中国人民解放军重庆通信学院</li>
</div>

<div class="subtitle">
	近期作品
</div>
<div class="content">
	<div class="link">最近作品请访问：
    <a href="http://starain.net.cn/blog/tags.asp">http://starain.net.cn/blog/tags.asp</a></div>
</div>

<div class="subtitle">
	自我评价
</div>
<div class="content">
	意志坚强，身体健康，积极进取、努力拼搏、为人正直，重视团队沟通和协作，严格遵守职业操守。
</div>


</body>
</html>
