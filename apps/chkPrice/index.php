<?php

define('SCK', 'SCT13766TOaaIaOXp71ofJWRkWE59VswY');

function sc_send($text, $desp = '', $key = SCK)
{
    $postdata = http_build_query(array(
        'text' => $text,
        'desp' => $desp
    ));
    $opts = array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => $postdata
        )
    );

    $context = stream_context_create($opts);
    return $result = file_get_contents('https://sctapi.ftqq.com/' . $key . '.send', false, $context);

}


print_r(file_get_contents('https://sc.ftqq.com/SCU108780Td10658f2c91e21e8ec03b6eb754774dc5f2ebf32a8ac7.send?text='.urlencode('测试1').'&desp='.urlencode('测试2')));

