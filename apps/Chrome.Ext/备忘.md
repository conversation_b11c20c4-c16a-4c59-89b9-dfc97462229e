/Volumes/USER/Applications/Google\ Chrome\ Canary.app/Contents/MacOS/Google\ Chrome\ Canary
--enable-logging --v=1
--vmodule=extension_updater=2 --enable-logging

http://open.chrome.360.cn/extension_dev/manifest.html

var imgURL = chrome.extension.getURL("images/myimage.png");
document.getElementById("someImage").src = imgURL;


小图片可使用 Base64 处理；
定义 web_accessible_resources 属性可以在 CSS 中使用 chrome-extension://__MSG_@@extension_id__ 代替路径

获取图片地址：
console.log(chrome.extension.getURL('MISC/play.png'));

扩展程序列表：     chrome://extensions/

图标尺寸：
工具栏目：16px
程序列表：48px
应用商店：50px     Retina：100px
安装窗口：128px


 GoQR：简单的生成二维码
二维码 43 像素倍数


 bigPic：获取 iTunes App Store 大尺寸截图 for 设计师

[ ] 跳转至大图链接
[x] 使用 ShadowBox 弹出
[ ] 自动下载？
[ ] 支持 iTunes App Store
[ ] 支持 Google Play
[ ] 支持 Dribbble.com


一个或者多个图标来表示扩展，app，和皮肤。你通常可以提供一个128x128的图标，这个图标将在webstore安装时候使用。扩展需要一个48x48的图标，扩展管理页面需要这个图标。同时，你还可以提供给一个16x16的图标作为扩页面的fa网页图标 。这个16x16的图标，还将显示在实验性的扩展infobar特性上。


检测升级数据：多个扩展时浏览器合并请求，超长会由 get 转换到 post 格式：
```
$gets = '{"os":"mac","arch":"x64","nacl_arch":"x86-64","prod":"chromecrx","prodchannel":"stable","prodversion":"44.0.2403.89","lang":"zh-CN","x":"id=ailleeijcmdnlhfkmmjjknpplgkdggfk&v=0.0.4&uc"}';
$post = '[]';
$svrs = '{"HTTP_HOST":"wei.padoor.com.cn","HTTP_CONNECTION":"keep-alive","HTTP_DNT":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_10_4) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/44.0.2403.89 Safari\/537.36","HTTP_ACCEPT_ENCODING":"gzip, deflate, sdch","HTTP_ACCEPT_LANGUAGE":"zh-CN,zh;q=0.8","PATH":"\/sbin:\/usr\/sbin:\/bin:\/usr\/bin","SERVER_SIGNATURE":"<address>Apache\/2.2.15 (CentOS) Server at wei.padoor.com.cn Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.2.15 (CentOS)","SERVER_NAME":"wei.padoor.com.cn","SERVER_ADDR":"************","SERVER_PORT":"80","REMOTE_ADDR":"**************","DOCUMENT_ROOT":"\/var\/www\/tata\/web","SERVER_ADMIN":"root@localhost","SCRIPT_FILENAME":"\/var\/www\/tata\/web\/chrome\/index.php","REMOTE_PORT":"61758","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"os=mac&arch=x64&nacl_arch=x86-64&prod=chromecrx&prodchannel=stable&prodversion=44.0.2403.89&lang=zh-CN&x=id%3Dailleeijcmdnlhfkmmjjknpplgkdggfk%26v%3D0.0.4%26uc","REQUEST_URI":"\/chrome\/?os=mac&arch=x64&nacl_arch=x86-64&prod=chromecrx&prodchannel=stable&prodversion=44.0.2403.89&lang=zh-CN&x=id%3Dailleeijcmdnlhfkmmjjknpplgkdggfk%26v%3D0.0.4%26uc","SCRIPT_NAME":"\/chrome\/index.php","PHP_SELF":"\/chrome\/index.php","REQUEST_TIME_FLOAT":1437735967.605,"REQUEST_TIME":1437735967}';

$file = 'vCRM.crx';
if (file_exists($file)) {
    header('Content-Description: File Transfer');
    header('Content-Type: application/x-chrome-extension');
    header('Content-Disposition: attachment; filename='.basename($file));
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: '.filesize($file));
    ob_clean();
    flush();
    readfile($file);
    exit;
}
```

