<html manifest=""><head><title>GoQR</title>
<meta charset="utf-8">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=0">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
<link rel="shortcut icon" href="">
<link rel="apple-touch-icon-precomposed" href="misc/128.png">
<link rel="apple-touch-startup-image" href="misc/startup.png">
<link rel="stylesheet" href="libs/jquery.mobile.css">
<link rel="stylesheet" href="misc/index.css">
<script type="text/javascript" src="libs/jquery.js"></script>
<script type="text/javascript" src="libs/jquery.mobile.js"></script>
<script type="text/javascript" src="libs/jquery.qrcode.js"></script>
<script type="text/javascript" src="misc/index.js"></script>

</head><body>

<div id="pagHome" data-role="page">
    <header data-role="header"><h1>GoQR</h1>
        <a href="#pagInfo" class="ui-btn-right" data-icon="info">关于</a>
    </header>
    <section data-role="content">
        <div id="tipCode">
            <a id="lnkCode" href="#pagCode"></a>
            <p id="txtInfo"></p>
        </div>
        <form id="fomData" method="post" action="">
            <input placeholder="姓名" name="name" type="text" class="card">
            <input placeholder="电话" name="call" type="tel" class="card call msgs">
            <input placeholder="网址" name="http" type="url" class="http">
            <input placeholder="邮箱" name="mail" type="email" class="card mail">
            <input placeholder="文本" name="text" type="text" class="msgs mail text">
            <button type="submit" data-role="button">生成</button>
        </form>
    </section>
    <footer data-role="footer" data-position="fixed">
        <nav id="barMenu" data-role="navbar"><ul>
            <li><a href="#" rel="card">名片</a></li>
    	<!--<li><a href="#" rel="call">电话</a></li>-->
            <li><a href="#" rel="msgs">短信</a></li>
            <li><a href="#" rel="http">网址</a></li>
            <li><a href="#" rel="mail">邮件</a></li>
            <li><a href="#" rel="text">文本</a></li>
        </ul></nav>
    </footer>
</div>

<div id="pagInfo" data-role="dialog">
    <header data-role="header"><h1>GoQR</h1></header>
    <section data-role="content">
        <p>由于不同的扫描软件对于二维码的解析能力并不相同，如果出现不能识别或识别不全，请尝试更换二维码扫描软件或通过以下地址反馈：</p>
        <p><center>==&gt; <a target="_blank" href="http://nan.im/blog/1136#fomNote">问题反馈</a> &lt;==</center></p>
        <p>行动二维码生成器 GoQR 是基于 jQuery · jQueryMobile 及 Google Chart API 等优秀的开源库构建。</p>
    </section>
</div>

<div id="pagCode" data-role="dialog">
    <header data-role="header"><h1>GoQR</h1></header>
    <section data-role="content"><center>
        <a target="_blank" id="bigCode" href="#this"></a>
        <p><b>Generated by GoQR</b></p>
    </center></section>
</div>


</body>
</html>

