WordPress Embed 文章视频自动修改插入播放器

添加ED2K支持

 http://deerchao.net/tutorials/regex/regex.htm

在 WordPress 2.9 中新增的 Easy Embeds 的功能，它能让你通过直接发布一个 URL（需要单独一行，纯文本，不带链接才行），就能把这个 URL 中的视频显示到 WordPress 博客上，并且会根据博客的布局调整视频的大小。

http://fairyfish.net/2010/01/20/easy-embeds-wordpress-29/

```php
function wp_embed_handler_youku( $matches, $attr, $url, $rawattr ) {
    // If the user supplied a fixed width AND height, use it
    if ( !empty($rawattr['width']) && !empty($rawattr['height']) ) {
        $width  = (int) $rawattr['width'];
        $height = (int) $rawattr['height'];
    } else {
        list( $width, $height ) = wp_expand_dimensions( 480, 400, $attr['width'], $attr['height'] );
    }
 
    return apply_filters( 'embed_youku', '<embed src="http://player.youku.com/player.php/sid/' . esc_attr($matches[1]) . '/v.swf" quality="high" width="' . esc_attr($width) . '" height="' . esc_attr($height) . '" align="middle" allowScriptAccess="sameDomain" type="application/x-shockwave-flash"></embed>', $matches, $attr, $url, $rawattr );
 
}
wp_embed_register_handler( 'youku', '#http://v.youku.com/v_show/id_(.*?).html#i', 'wp_embed_handler_youku' );
```

