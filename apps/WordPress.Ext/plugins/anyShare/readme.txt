=== Plugin Name ===

Contributors:      nan
Requires at least: 3.0.0
Tested up to:      3.9.1
Stable tag:        trunk
Donate link:       http://nan.im/blog/1302
Tags:              SNS, China, Share, Button
License:           GPLv2 or later
License URI:       http://www.gnu.org/licenses/gpl-2.0.html

专为中国网友设计的社交网络纯绿色分享插件，率先支持 Retina 显示屏，另有 Chrome drupal 等平台支持 ...


== Description ==

anyShare 是专为中国用户设计制作的社会化网络分享纯绿色小插件，率先支持 Retina 显示屏，安装简单无需任何配置和主题修改，卸载无任何数据库残留信息，插件启用后会在文章单页的底部添加一些国内流行的社会化分享按钮例如：腾讯微博、新浪微博、腾讯空间、开心网、人人网、百度搜藏、腾讯书签等等国内流行的社会化分享服务，希望大家喜欢。

目前国内已经有很多提供第三方收藏或者分享服务的工具和站点例如 JiaThis AddThis bShare 等等，这些站点提供的插件虽然使用上非常方便但或许是出于数据统计和商业价值等原因，这些插件在用户点击按钮后都需要先跳转到第三方站点，或者是让用户茫然的新建页面然后继续操作才能分享内容到目标，我个人不太喜欢。于是我设计制作了 anyShare 插件与同类插件的区别是：完全原生接口支持、无需数据库、无需中转页面、无用户信息收集、无需加载任何第三方代码、安装简单卸载干净、绿色、轻巧、迅速！

目前插件还在不断开发完善之中，除了 WordPress 以外 anyShare 还提供 Chrome 及 drupal 平台插件支持，相关插件下载及任何问题可在我的博客页面反馈： http://nan.im/blog/1302 谢谢支持！

本插件已开源托管于 GitHub: https://github.com/NanL/WordPressExt 大家随时可以提交修改请求及反馈。


== Installation ==

直接在后台搜索 anyShare 在线安装启用即可，无需任何设置和代码修改！ 卸载也同样方便，直接删除即可，没有任何残留！


== Frequently Asked Questions ==

极个别主题由于主色调与插件图标不协调，可自己在主题内定义 CSS 即可。


== Changelog ==

= 2014-07-06 1.1 =
修复一个第三方标题获取问题，感谢 prohorse 在 GitHub 的代码提交；

= 2013-07-14 1.0 =
修复了一个针对 32 位服务器的问题；

= 2013-03-30 0.9 =
率先支持 Retina 显示屏；
去掉了我个人很喜欢但让很多网友不爽的二维码；
接纳网友意见在页面类型的内容页不再显示本插件；
为分享链接加上 nofollow 标记；
代码几乎完全重构；

= 2011-04-11 0.5 =
网友建议只保留图标，去掉文字；
重新设计了 UI 并加入了 QR 二维码 希望大家喜欢；

= 2010-10-09 0.4 =
修正了由于 WordPress 官方服务器目录大小写引起的问题；

= 2010-10-01 0.3 =
添加了 腾讯微博 等分享按钮；

= 2010-09-12 0.2 =
样式上的简单调整以适合更多人的博客主题；

= 2010-09-01 0.1 =
插件正式上线 ^_^

== Upgrade Notice ==
小改动，可直接升级。

== Screenshots ==

1. anyShare for WordPress

