=== Plugin Name ===

Contributors:      nan
Requires at least: 3.0.0
Tested up to:      3.5.2
Stable tag:        trunk
Donate link:       http://nan.im/blog/
Tags:              Gist, ED2K
License:           GPLv2 or later
License URI:       http://www.gnu.org/licenses/gpl-2.0.html

支持自动嵌入 Gist 代码块，自动为 ED2K 添加链接，更多网站支持陆续添加中。


== Description ==

anyEmbed 可以将 Gist 网址直接变成代码高亮模块，也可以将长的 ed2k 电驴下载链接变成可点击的链接，未来更多格式陆续添加中，敬请期待。

如何使用：

如下直接粘贴 Gist 网址即可，需要在单独一行：

https://gist.github.com/nanL/5750198

任何问题可邮件到 <EMAIL> 反馈！


== Installation ==

直接在后台搜索 anyEmbed 在线安装启用即可，无需任何设置和代码修改！ 卸载也同样方便，直接删除即可，没有任何数据库残留！


== Frequently Asked Questions ==

常见问题：

嵌入的链接需要单独一行，并确认没有任何样式代码，插件才能自动识别。

