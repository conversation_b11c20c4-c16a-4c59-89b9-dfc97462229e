<?php

// view-source:http://127.0.0.1/im.nan/apps/ical/
// https://p38-calendars.icloud.com/holidays/cn_zh.ics
// https://p50-calendars.icloud.com/holidays/cn_zh.ics
// https://dev.heweather.com/docs/api/weather#%E8%AF%B7%E6%B1%82%E5%8F%82%E6%95%B0
// webcal://p22-calendars.icloud.com/published/2/MTg2MTI3NzA2MTE4NjEyN2RFGWe4SWa1bknJ2yi327b5JzxT_sJlGpg7ekWO_VexOtzP8tERanEWw6W9jDORr1RSFSjizD-jsYJYVjPvCL0



// $holiday = 'https://p50-calendars.icloud.com/holidays/cn_zh.ics';
$holiday = 'https://calendars.icloud.com/holidays/cn_zh.ics';
$weather = 'https://free-api.heweather.net/s6/weather/forecast?location=beijing&key=0b5cb545438848afb0ab9f70ac5016b8';
$quality = 'https://free-api.heweather.net/s6/air/now?location=beijing&key=0b5cb545438848afb0ab9f70ac5016b8';

/* 
BEGIN:VCALENDAR
METHOD:PUBLISH
VERSION:2.0
X-WR-CALNAME:测试
PRODID:-//Apple Inc.//Mac OS X 10.15.6//EN
X-APPLE-CALENDAR-COLOR:#B90E28
X-WR-TIMEZONE:Asia/Shanghai
CALSCALE:GREGORIAN

BEGIN:VEVENT
TRANSP:TRANSPARENT
DTEND;VALUE=DATE:20200909
UID:4D65F222-85B7-4DA6-BE18-28927BDA9E9A
DTSTAMP:20200908T082044Z
LOCATION:位置
DESCRIPTION:备注
SEQUENCE:1
X-APPLE-TRAVEL-ADVISORY-BEHAVIOR:AUTOMATIC
SUMMARY:标题
LAST-MODIFIED:20200908T082038Z
CREATED:20200908T082018Z
DTSTART;VALUE=DATE:20200908
END:VEVENT

END:VCALENDAR
*/

$vcalendar = implode(PHP_EOL,[
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:icalendar',
    'X-APPLE-REGION:CN',
    'X-APPLE-LANGUAGE:zh',
    'X-APPLE-CALENDAR-COLOR:#B90E28',
    'X-WR-CALNAME:天气',
    'X-WR-TIMEZONE:Asia/Shanghai',
    '{VEVENT}',
    'END:VCALENDAR',
]);
$vevent = implode(PHP_EOL,[
    'BEGIN:VEVENT',
    // 'DTSTAMP:{DTSTAMP}T080000Z',
    'DTSTART;VALUE=DATE:{DTSTART}',
    'SUMMARY:{SUMMARY}',
    // 'TRANSP:TRANSPARENT',
    // 'CATEGORIES:订阅',
    // 'LOCATION:{LOCATION}',
    'DESCRIPTION:{DESCRIPTION}',
    // 'UID:{UID}',
    'END:VEVENT',
]);

// BEGIN:VEVENT
// CREATED:20200901T102855Z
// UID:6A1606FF-42E4-4BD0-AA49-EF0C5ED69FEA
// DTEND;VALUE=DATE:20200902
// TRANSP:TRANSPARENT
// X-APPLE-TRAVEL-ADVISORY-BEHAVIOR:AUTOMATIC
// SUMMARY:测试
// LAST-MODIFIED:20200901T102855Z
// DTSTAMP:20200901T102855Z
// DTSTART;VALUE=DATE:20200901
// SEQUENCE:0
// URL;VALUE=URI:
// END:VEVENT

// 获取并缓存网络文件资源，默认 24 小时。
function getHttpFile($http, $live = 86400){
    $file = sys_get_temp_dir().'/'.md5($http);
    if(!file_exists($file)) fclose(fopen($file,'w'));
    if(filesize($file) < 99 || time() - filemtime($file) > $live){
        file_put_contents($file, file_get_contents('compress.zlib://'.$http));
        // file_put_contents($file,file_get_contents($http));
    }
    return file_get_contents($file);
}

$ical = getHttpFile($holiday,604800); // 缓存一周
$days = json_decode(getHttpFile($weather,3600))->{'HeWeather6'}[0]->daily_forecast;
// exit($ical);
foreach ($days as $d):
    // print_r($d);
    $title  = ($d->cond_txt_d == $d->cond_txt_n) ? $d->cond_txt_d : ($d->cond_txt_d.'转'.$d->cond_txt_n);
    $title .= ' '.$d->tmp_min.'~'.$d->tmp_max.'℃';
    // $notes  = implode(PHP_EOL,[
    //     '紫外线'.$d->uv_index,
    //     $d->wind_dir.$d->wind_sc.'级',
    // ]);
    $notes  = '紫外线'.$d->uv_index.' '.$d->wind_dir.$d->wind_sc.'级';
    $event  = str_replace(
        ['{DTSTAMP}','{DTSTART}','{DTEND}','{SUMMARY}','{LOCATION}','{DESCRIPTION}','{UID}'],
        [
            str_replace('-', '', $d->date),
            str_replace('-', '', $d->date),
            '',
            $title,
            '',
            $notes,
            md5($d->date.$title),
        ], $vevent).PHP_EOL;
    $ical = str_replace('END:VCALENDAR', $event.'END:VCALENDAR', $ical);
endforeach;

$ical = str_replace([
    'X-WR-CALNAME:中国节假日',
    // 'CATEGORIES:節慶',
], [
    'X-WR-CALNAME:订阅',
    // '',
], $ical);

// header('Content-type: text/calendar; charset=UTF-8');
// header('Content-Disposition: attachment; filename=smart.ics');
exit($ical);
