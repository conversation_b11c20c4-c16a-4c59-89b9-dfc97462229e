<html manifest=""><head><title>GoQR</title><meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
<link rel="shortcut icon" href="">
<link rel="apple-touch-icon-precomposed" href="misc/128.png">
<link rel="apple-touch-startup-image" href="misc/startup.png">
<link rel="stylesheet" href="//libs.baidu.com/jquerymobile/1.3.0/jquery.mobile-1.3.0.min.css">
<script src="//libs.baidu.com/jquery/2.1.1/jquery.min.js"></script>
<script src="//libs.baidu.com/jquerymobile/1.3.0/jquery.mobile-1.3.0.min.js"></script>
<script src="misc/jquery.qrcode.js"></script>
<script src="misc/index.js"></script>
<style>
body { min-width: 320px; min-height: 450px; }
#tipCode { background-color: #FFF; margin: -15px -15px 20px; padding: 15px; box-shadow: 0 0 4px; }
#tipCode a { float: left; width: 130px; height: 130px; background: #FFF url(misc/128.png) no-repeat center center; box-shadow: 0 0 4px #999; border-radius: 3px; }
#tipCode a > canvas,
#tipCode a > div { background-color: #FFF; }
#tipCode p { display: block; margin: 0 0 0 140px; font-size: 14px; line-height: 1.6em; }
#tipCode p b { font-weight: normal; display: none; }
#bigCode { display: block; border: 4px solid #FFF; width: 258px; height: 258px; margin: 1em auto; background: url(misc/128.png) no-repeat center center; }
#bigCode > canvas,
#bigCode > div { background-color: #FFF; }
#fomData input { padding: .4em 0 .3em; }
#fomData textarea { resize:none; height: 80px; }
</style>
</head><body>

<div id="pagHome" data-role="page">
    <header data-role="header"><h1>GoQR</h1>
        <a href="#pagInfo" data-icon="info" data-rel="dialog" data-transition="flip" class="ui-btn-right">关于</a>
    </header>
    <section data-role="content">
        <div id="tipCode">
            <a id="lnkCode" href="#pagCode"></a>
            <p id="txtInfo">Tips:&nbsp;
                <b>移动版的 GoQR 已经支持 iPhone 的【添加到桌面】功能</b>
                <b>目前已经有 GoQR for Chrome 免费提供</b>
                <b>二维码将成为链接物联网的桥梁</b>
                <b>现在 GoQR 已经在 GitHub 上开源</b>
                <b>名片码需要扫码软件支持 meCard 标准如：微信等等</b>
                <b>再点一次放大的二维码可保存图片</b>
                <b>不能识别时可尝试点击二维码图片进行放大</b>
            </p><br clear="all">
        </div>
        <form id="fomData" method="post" action="">
            <input placeholder="姓名" name="name" type="text" class="card">
            <input placeholder="网络" name="ssid" type="text" class="wifi">
            <input placeholder="密码" name="pass" type="text" class="wifi">
            <input placeholder="电话" name="call" type="tel" class="card call msgs">
            <input placeholder="网址" name="http" type="url" class="http">
            <input placeholder="邮箱" name="mail" type="email" class="card mail">
            <textarea placeholder="内容" name="text" class="msgs mail text"></textarea>
            <button type="submit" data-role="button" data-mini="true">生成二维码</button>
        </form>
    </section>
    <footer data-role="footer" data-position="fixed">
        <nav id="barMenu" data-role="navbar"><ul>
            <li><a href="#" rel="card" data-rule="MECARD:N:{name};TEL:{call};EMAIL:{mail};">名片</a></li>
    	<!--<li><a href="#" rel="call" data-rule="TEL:{call}">电话</a></li>-->
        <!--<li><a href="#" rel="msgs" data-rule="SMSTO:{call}:{text}">短信</a></li>-->
            <li><a href="#" rel="http" data-rule="{http}">网址</a></li>
            <li><a href="#" rel="wifi" data-rule="WIFI:T:WPA;S:{ssid};P:{pass};">WiFi</a></li>
            <li><a href="#" rel="mail" data-rule="MAILTO:{mail}?subject=&body={text}">邮件</a></li>
            <li><a href="#" rel="text" data-rule="{text}">文本</a></li>
        </ul></nav>
    </footer>
</div>

<div id="pagInfo" data-role="dialog">
    <header data-role="header"><h1>GoQR</h1></header>
    <section data-role="content">
        <p>由于不同的扫码软件对于二维码的解析能力并不相同，如果出现不能识别或识别不全，请尝试更换二维码扫码软件或通过以下地址反馈：</p>
        <p><center>==&gt; <a target="_blank" href="http://nan.im/blog/1136#fomNote">问题反馈</a> &lt;==</center></p>
        <p>二维码生成器 GoQR 是基于 jQuery · jQueryMobile 及 GoogleChart 等优秀的开源库构建。</p>
    </section>
</div>

<div id="pagCode" data-role="dialog" data-transition="flip">
    <header data-role="header"><h1>GoQR</h1></header>
    <section data-role="content"><center>
        <a target="_blank" id="bigCode" href="#this"></a>
        <p><b>Generated by GoQR</b></p>
    </center></section>
</div>

</body>
</html>

