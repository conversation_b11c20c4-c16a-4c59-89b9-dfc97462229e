jQuery(document).bind("mobileinit").ready(function($){function toUtf8(t){var e,a,i,o;for(e="",i=t.length,a=0;i>a;a++)o=t.charCodeAt(a),o>=1&&127>=o?e+=t.charAt(a):o>2047?(e+=String.fromCharCode(224|o>>12&15),e+=String.fromCharCode(128|o>>6&63),e+=String.fromCharCode(128|o>>0&63)):(e+=String.fromCharCode(192|o>>6&31),e+=String.fromCharCode(128|o>>0&63));return e}$("#txtInfo > b").eq(Math.floor(Math.random()*$("#txtInfo>b").length)).fadeIn(),$("#barMenu a").click(function(){$("textarea").hide().filter("textarea."+$(this).attr("rel")).fadeIn("fast"),$("#fomData > div.ui-input-text").hide().has("input."+$(this).attr("rel")).fadeIn("fast")}).eq(0).click(),$("#fomData").submit(function(){var code=$("#barMenu").find("a.ui-btn-active").data("rule");return $("#fomData input:visible").add("#fomData textarea:visible").each(function(){""!==$(this).val()&&(code=code.replace(eval("/{"+$(this).attr("name")+"}/g"),$(this).val()))}),code=toUtf8(code),$(this).find(".ui-submit.ui-btn-active").removeClass("ui-btn-active"),$("#lnkCode canvas, #lnkCode > div, #bigCode canvas, #bigCode > div").remove(),$("#lnkCode").qrcode({render:"div",text:code,size:130}),$("#bigCode").qrcode({render:"div",text:code,size:260}),$("#bigCode").attr("href","http://chart.apis.google.com/chart?cht=qr&chld=|0&choe=UTF-8&chs=430x430&chl="+encodeURIComponent(code)),!1}),"object"==typeof chrome.tabs&&chrome.tabs.getSelected(null,function(t){$("#barMenu a").eq(2).click(),$('input[name="http"]').val(t.url),$('input[name="text"]').val(t.title+" "+t.url),$("#fomData").submit()})});