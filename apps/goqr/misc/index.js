jQuery(document).bind('mobileinit').ready(function($){

//
function toUtf8(str) {
    var out, i, len, c;
    out = '';
    len = str.length;
    for(i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if ((c >= 0x0001) && (c <= 0x007F)) {
            out += str.charAt(i);
        } else if (c > 0x07FF) {
            out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
            out += String.fromCharCode(0x80 | ((c >>  6) & 0x3F));
            out += String.fromCharCode(0x80 | ((c >>  0) & 0x3F));
        } else {
            out += String.fromCharCode(0xC0 | ((c >>  6) & 0x1F));
            out += String.fromCharCode(0x80 | ((c >>  0) & 0x3F));
        }
    }
    return out;
}

// for tips ...
$('#txtInfo > b').eq(Math.floor(Math.random()*$('#txtInfo>b').length)).fadeIn();

// for filt input ...
$('#barMenu a').click(function(){
    $('textarea').hide().filter('textarea.'+$(this).attr('rel')).fadeIn('fast');
    $('#fomData > div.ui-input-text').hide().has('input.'+$(this).attr('rel')).fadeIn('fast');
}).eq(0).click();

// for make qrCode ...
$('#fomData').submit(function(){
    //
    var code = $('#barMenu').find('a.ui-btn-active').data('rule');
    $('#fomData input:visible').add('#fomData textarea:visible').each(function(){
        if($(this).val() === '') return;
        code = code.replace(eval('/{' + $(this).attr('name') + '}/g'), $(this).val());
    });
    code = toUtf8(code);
    // console.log(code);
    // var edge = (window.devicePixelRatio === 2) ? 258 : 129;
    $(this).find('.ui-submit.ui-btn-active').removeClass('ui-btn-active');
    $('#lnkCode canvas, #lnkCode > div, #bigCode canvas, #bigCode > div').remove();
    $('#lnkCode').qrcode({ render:'div', text:code, size:130 });
    $('#bigCode').qrcode({ render:'div', text:code, size:260 });
    $('#bigCode').attr('href', 'http://chart.apis.google.com/chart?cht=qr&chld=|0&choe=UTF-8&chs=430x430&chl=' + encodeURIComponent(code));
    //
    return false;
});

// for ChromeExt
// if(typeof WeixinJSBridge == 'undefined'){}
if(typeof chrome.tabs === 'object'){
    chrome.tabs.getSelected(null, function(tabs){
        $('#barMenu a').eq(2).click();
        $('input[name="http"]').val(tabs.url);
        $('input[name="text"]').val(tabs.title + ' ' + tabs.url);
        $('#fomData').submit();
    });
}



//
});
