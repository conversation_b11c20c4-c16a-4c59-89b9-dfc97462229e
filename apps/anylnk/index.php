<?php // anyLnk for MobilePhone by anyLiv.com 0618-2011

// some info
$beta = 'b7';
$path = 'http://nan.im/apps/anylnk/';
$info = 'http://anyliv.com/blog/1371';
$ibug = 'mailto:<EMAIL>?subject=anyLnk '.$beta.' feedback &body=%5CnDiagnostics: '.$_SERVER['HTTP_USER_AGENT'];
$lang = $_SERVER["HTTP_ACCEPT_LANGUAGE"];
$open = $_SERVER['SERVER_NAME'] == 'nan.im' ? TRUE : FALSE;
$mobi = strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone') ? TRUE : FALSE;
$work = in_array($_GET['type'], array('test', 'call', 'face', 'isms', 'mail', 'tube', 'skyp')) ? TRUE : FALSE;
//$mobi = TRUE;

// work and mobi
if($work && $mobi):
	// some data
	$type = $_GET['type'];
	$call = $_GET['call'] ? $_GET['call'] : '';
	$name = $_GET['name'] ? $_GET['name'] : 'anyLnk';
	$iurl = array('call'=>'tel://', 'face'=>'facetime://', 'isms'=>'sms://', 'mail'=>'mailto:','tube'=>'youtube://', 'skyp'=>'skype://');
	$exec = $iurl[$type].$call;
	$icon = $path.'image/'.$type.'.png';
//	$icon = 'http://gravatar.com/avatar/'.md5(strtolower(trim($_GET['mail']))).'?s=57&d='.urlencode($icon);
//	$icon = 'data:image/jpeg;base64,'.base64_encode(file_get_contents($icon));
//	$icon = file_get_contents('cache/'.$type.'.dat');
	// make data page
	$html  = '<!DOCTYPE html><head>';
	$html .= '<meta charset="utf-8"><title>'.$name.'</title>';
	$html .= '<meta name="apple-mobile-web-app-capable" content="yes">';
	$html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
	$html .= '<link rel="apple-touch-icon-precomposed" href="'.$icon.'">';
	$html .= '<style>body { line-height:1.6em; background-color:#EEE; } center { padding:20px 0; } h1 img { height:60px; width:60px; border-radius:10px; box-shadow:0 0 3px #CCC; }</style>';
	$html .= '</head><body><center>';
	$html .= '<h1><a title="'.$name.'" id="a" href="'.$exec.'"><img alt="'.$type.'" src="'.$icon.'"></a></h1>';
	$html .= '<p>轻触下方的 <img id="i" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAPCAQAAABDj1eZAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAUdJREFUKFNtkLtLw1AYxS/qJLhXVKr2ZRulUNtiqgSb3CziICI6ucTFVYcOnaQOFRwUnNTRwUWXgpP/QdHNUEQUHGxofYBTlRs83iZNjKTncOGe7/vx3QchXUWn6FL3jhfKUdCCr5zuifV5oDiHQM+c+CIhiiCSWNu08iq9oHXKLAiqrgR4UXqlOEYZt++ExEL0wW7+OW0G10muLv9gmqfe5FAWKmTMYQYiFL7PYwyLOD8lSjNh2gdnPzMII4QUBxc4OothbAF7GCBKQ0YbSWyPQsIhqvetS+y0ygGMo/KFZfviDvR4AhwgZU9dGYnA0J/6ndc15i3ouYIMcVVUcEXIoOxCeRCfwP8sXBSdjtpUv/1QW+K16kCCIUC4id9Fa0JtkluwVkSfqPL6RwfSDA0aNlx7k/bWgViB7bMS2/1vk5sdsZLN/ALSuL3tylO4RAAAAABJRU5ErkJggg=="> 按钮并选择<br>「<b>添加至主屏幕</b>」<br>将图标快捷方式添加到桌面</p>';
//	$html .= '<p>使用“<b>添加至主屏幕</b>”菜单<br>将快捷方式添加到桌面</p>';
	$html .= '<p><small><a href="'.$info.'">帮助</a> · <a href="'.$ibug.'">反馈</a><br>Generated by anyLnk '.$beta.'.</small></p>';
	$html .= '</center>';
	$html .= '<script> if(window.navigator.standalone){ var a = document.getElementById("a"); var c = document.createEvent("MouseEvents"); c.initEvent("click", true, true); a.dispatchEvent(c); } </script>';
	$html .= '</body></html>';
//	echo $html; exit; //test
	header('Location: data:text/html;charset=UTF-8;base64,'.base64_encode($html)); exit;
endif;

//
?>
<!DOCTYPE html manifest="">
<head><meta charset="utf-8"><title>anyLnk</title>
<meta name="robots" content="noindex, nofollow">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta name="apple-mobile-web-app-capable" content="no">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<link type="image/png" rel="icon" href="image/icon.png">
<link type="image/png" rel="shortcut icon" href="image/icon.png">
<link type="image/png" rel="apple-touch-icon-precomposed" href="image/icon.png">
<style>
body { padding:0; width:320px; margin:0 auto;	color:#333; -webkit-font-smoothing:antialiased;
	font:14px/20px "Lucida Grande", "Lucida Sans Unicode", Helvetica, Arial, Verdana, sans-serif;
}
a { color:#08C; text-decoration:none; }
form { display:block; }
style, script { display:none; }
input, select, button { display:block; margin:9px auto; font-size:14px; }
input { width:275px; height:35px; text-align:center; }
select, button { width:285px; height:30px; }
header, footer { text-align:center; padding:0 10px; line-height:40px; background-color:#5E5E5E; font-weight:bold; color:#FFF; }
footer { font-size:12px; }
footer hr { margin:0; display:block; border:none; height:2px; background-color:#08C; box-shadow:0 0 3px #90F; }
section { padding:10px; min-height:240px; max-height:300px; }
#page { margin:5px; background-color:#EEE; border:1px solid #FFF; box-shadow:0 0 3px #999; }
</style>
<script src="cache/jquery.min.js"></script>
<script>
// for Google Analytics ...
var _gaq = _gaq || [];
_gaq.push(['_setAccount', 'UA-4779703-15']);
_gaq.push(['_trackPageview']);
_gaq.push(['_trackPageLoadTime', 'anyLnk/<?php echo $_GET['type'] ?>']);

// for
jQuery(function($){
	//
	window.scrollTo(0, 0);
	$('section').height($(window).height() - 115);

	// for confirm NULL info...
	$('form').submit(function(){
		var balk = '';
		$(this).find('input').each(function(){
			balk += $(this).val() ? '' : ($(this).attr('alt') + ' ');
		});
		if(balk){ alert(balk + '不能为空'); return false; }
		// for Google Analytics ...
		_gaq.push(['_trackPageview', 'anyLnk/' + $('select[name="type"]').val()]);
	});
	
	// for YouTube icon ...
	$('select[name="type"]').change(function(){
		if($(this).val() == 'tube'){
			$('input[name="name"]').val('YouTube');
			$('input[name="call"]').val(' ');
			$('#form').submit();
		}
	});
	//
// endof jQuery
});


</script>
<?php if($open){ ?>
<script async src="http://www.google-analytics.com/ga.js"></script>
<script async src="http://hm.baidu.com/h.js?9a5cf8c041364f910f61f05739307295"></script>
<?php } else { ?>

<?php } ?>
</head><body>

<div id="page">
<header><b><a href="<?php echo $path ?>">anyLnk</a></b></header>
<section>

<?php if($work && !$mobi){ ?>

<?php // for PC ...
	$qpix  = 150;
	$qimg  = 'http://chart.apis.google.com/chart?cht=qr&chld=|0&choe=UTF-8&chs='.$qpix.'x'.$qpix.'&chl=';
	$qimg .= rawurlencode('http://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI']);
?>
<center>
	<img src="<?php echo $qimg ?>" height="<?php echo $qpix ?>" width="<?php echo $qpix ?>">
	<p>请使用 <b><a target="_blank" href="http://itunes.apple.com/app/id387433941">ScanIt</a></b> 等扫描</p>
    <p>此二维码可能携带有您的个人信息<br>不要将此二维码置于开放的环境中</p>
</center>

<?php } else { ?>

<form method="get" id="form">
	<select name="type">
		<option value="call">&nbsp;1. 请选择快捷方式要执行的动作：</option>
        <!--<optgroup label="系统应用">-->
            <option value="call">拨打电话</option>
            <option value="isms">发送短信 iMessage</option>
            <option value="face">视频通话 facetime</option>
            <option value="mail">发送邮件 eMail</option>
        <!--</optgroup><optgroup label="软件应用">-->
			<option value="tube">视频服务 YouTube</option>
			<option value="skyp">网络电话 skype</option>
        <!--</optgroup>-->
	</select>
	<input required name="name" alt="姓名" autocomplete="off" placeholder=" 2. 图标名称 - 联系人的姓名昵称将用于屏幕显示">
	<input required name="call" alt="号码" autocomplete="off" placeholder=" 3. 号码邮箱 - 电话号码 facetime 邮箱 skype 用户名等">
	<button type="submit">创建快捷方式</button>
</form>

<?php } ?>

</section>
<footer><a href="<?php echo $info ?>">帮助</a> · <a href="<?php echo $ibug ?>">反馈</a></footer>
</div></body></html>
