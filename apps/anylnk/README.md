## anyLnk




项目 anyLnk - 用于 iOS iPhone 的桌面联系人快捷方式图标

http://anyliv.com/blog/1371

增加 YouTube 快捷方式？？
youtube://


使用 Google 通讯录 API 列出所有联系人 -> 选择联系人 -> 交与生成模块 -> 生成专用页面 -> 生成桌面图标
未使用 Google 通讯录用户直接表单填写生成 -

头像部分两种调用模式：由于 WEB APP 无法调用系统级通讯录内容，无法调用用户的头像，尝试从 http://cn.gravatar.com/ 调用头像；

$hash = md5(strtolower(trim("<EMAIL>")));
$href = 'http://gravatar.com/avatar/'.$hash.'?s=57';
$data = file_get_contents('http://gravatar.com/avatar/'.$hash.'?s=57');

链接形式：<img src="<?=$href?>">

编码形式：<img src="data:image/jpeg;base64,<?php echo base64_encode($data) ?>">
几乎不消耗任何流量，但更新需要重新生成快捷图标；

window.navigator.standalone 与 apple-mobile-web-app-capable 对应
