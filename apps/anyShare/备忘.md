项目 - anyShare 分享工具

重要账号：
https://github.com/nanL/anyShare
http://plugins.svn.wordpress.org/anyshare/     anyLiv 456852

应用名称：anyShare
应用网址：http://nan.im/blog/1302
类型：博客/个人网站组件
应用简介：支持 WordPress Drupal 及 Chrome 的 SNS 分享挂件


未来功能：
[]将选中的内容分享；
[]定制需要的按钮；
[]加入图片链接地址；
[]加入内容摘要属性；
[x]支持 Retina 显示器；
[]



使用的社交分享按钮依据 百度分享 排名：http://share.baidu.com/

1. QQ空间    http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=&title=&desc=&summary=&site=&pics=
2. 新浪微博    http://service.weibo.com/share/share.php?url=&title=&appkey=&pic=&sudaref=
3. 腾讯微博    http://share.v.t.qq.com/index.php?c=share&a=index&url=&title=&appkey=
4. 人人网    http://widget.renren.com/dialog/share?resourceUrl=&srcUrl=&title=&description=
5. 百度搜藏    http://cang.baidu.com/do/add?iu=&it=&linkid=     排除收藏类
6. 百度贴吧    http://tieba.baidu.com/f/commit/share/openShareApi?url=&title=&desc=&comment=
7. 开心网    http://www.kaixin001.com/rest/records.php?url=&style=11&content=&stime=&sig=
8. 百度空间    http://hi.baidu.com/pub/show/share?url=&title=&content=&linkid=
9. 腾讯朋友    http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?to=pengyou&url=&title=&desc=&summary=&pics=
10. QQ收藏     http://shuqian.qq.com/post?from=3&uri=&title=      排除收藏类
11. 豆瓣    http://shuo.douban.com/!service/share?href=&name=&image=
12. 搜狐微博    http://t.sohu.com/third/post.jsp?url=&title=&pic=
1. GooglePlus    https://plus.google.com/share?url=
2. Twitter    https://twitter.com/intent/tweet?source=webclient&text=
3. FaceBook    https://www.facebook.com/sharer/sharer.php?u=&t=
4. QQ好友    http://connect.qq.com/widget/shareqq/index.html?url=&title=&desc=&summary=&site=&pics=


腾讯微博AppKey：2e295ab2ff8245229d96fa3768a9f779
新浪微博AppKey：3581453612



邮件分享 bShare
http://b.bshare.cn/bshareEmail?url=&title=&content=&site=




javascript:(function(){var c;c=document.createElement("script");c.type="text/javascript";c.src="http://www.ctrlq.org/plusone/index.js?r="+Math.random();document.body.appendChild(c);})();

(function(){  var iframe_url = "http://ctrlq.org/plusone/" + "?u=" + encodeURIComponent(document.location.href);  var div = document.createElement("div");  div.id = "labnol_plusone";var str = "<style>#labnol_plusone{position: fixed; top: 10px; right: 20px; width: 150px; height: 90px;z-index: 9999;background: #f4f4f4;padding: 10px;border: thin solid #999;}#ex small {font:Verdana, Geneva, sans-serif;font-size:9px;float:right;display:block}</style><div id='ex'><small><a href='#' onClick=\"document.getElementById('labnol_plusone').style.display='none'; return false;\" title='Click to close this window'>Close</a></small><iframe frameborder='0' scrolling='no' src='" + iframe_url + "' width='120px' height='75px' style='backgroundColor: white;'></iframe></div>";  div.innerHTML = str;  document.body.insertBefore(div, document.body.firstChild);})()

<html><head><title>Google +1</title> <link rel="canonical" href="http://www.labnol.org/" /><script type="text/javascript" src="https://apis.google.com/js/plusone.js"> </script> </head><body><style>body {padding:20px; background-color:#f4f4f4}</style> <g:plusone size="standard" count="true" href="chrome://newtab/"></g:plusone></body></html>

示例代码：
javascript:
function iprl5(){
     var d=document,z=d.createElement('scr'+'ipt'),b=d.body,l=d.location;
     try{
          if(!b)throw(0);
          if (!l) {alert('请输入网址！');return;}
          d.title='(Shortening...)'+d.title;
z.setAttribute('src','http://www.ruanyifeng.com/webapp/url_shortener_plugin.php?longUrl='+encodeURIComponent(l));
          b.appendChild(z);
     }catch(e){alert('请等待网页加载完毕！');}}
     iprl5();void(0)

捐赠按钮：
<a href="http://lab.alipay.com/p.htm?id=2011061200227244" target="_blank"><img src="https://img.alipay.com/life/guarantee/btn-buy.jpg" alt="支付宝担保交易购买" style="border:0px;width:182px;height:33px"></img></a>

腾讯微博
App Key：2e295ab2ff8245229d96fa3768a9f779
App Secret：dba266f62b7c0db31a88a5e72c2674ae

javascript:void((function(d){if(!!d){d.toggle();return;};var src='http://dict.qq.com/cloudgetjs';var e=document.createElement('script');e.setAttribute('src',src);document.getElementsByTagName('head')[0].appendChild(e);})(window.QQCloudDict))

javascript:(function(){EN_CLIP_HOST='http://www.evernote.com';try{var x=document.createElement('SCRIPT');x.type='text/javascript';x.src=EN_CLIP_HOST+'/public/bookmarkClipper.js?'+(new Date().getTime()/100000);document.getElementsByTagName('head')[0].appendChild(x);}catch(e){location.href=EN_CLIP_HOST+'/clip.action?url='+encodeURIComponent(location.href)+'&title='+encodeURIComponent(document.title);}})();





选中文字分享……

三、方法与代码
选中即分享的功能看上去比较高级，其实实现是相当简单的。其中的会让人头大，一般人也不感兴趣的原理这里就直接跳过。这个js文字选中后分享到新浪微博的功能我简单的封装了下，方法名是：$sinaMiniBlogShare，当然，您不喜欢可以换掉，甚至不要，此方法完整代码如下：
var $sinaMiniBlogShare = function(eleShare, eleContainer) {
var eleTitle = document.getElementsByTagName("title")[0];
eleContainer = eleContainer || document;
var funGetSelectTxt = function() {
var txt = "";
if(document.selection) {
txt = document.selection.createRange().text; // IE
} else {
txt = document.getSelection();
}
return txt.toString();
};
eleContainer.onmouseup = function(e) {
e = e || window.event;
var txt = funGetSelectTxt(), sh = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
var left = (e.clientX - 40 < 0) ? e.clientX + 20 : e.clientX - 40, top = (e.clientY - 40 < 0) ? e.clientY + sh + 20 : e.clientY + sh - 40;
if (txt) {
eleShare.style.display = "inline";
eleShare.style.left = left + "px";
eleShare.style.top = top + "px";
} else {
eleShare.style.display = "none";
}
};
eleShare.onclick = function() {
var txt = funGetSelectTxt(), title = (eleTitle && eleTitle.innerHTML)? eleTitle.innerHTML : "未命名页面";
if (txt) {
window.open('http://v.t.sina.com.cn/share/share.php?title=' + txt + '→来自页面"' + title + '"的文字片段&url=' + window.location.href);
}
};
};
可以看到$sinaMiniBlogShare方法有两个参数，eleShare和eleContainer，其中，前一个参数是必须的，指的是文字选中后出现的浮动层元素（在本文demo中就是新浪眼睛图标）；后面一个参数指文字选择的容器元素，可选参数，如果不设置则指document元素，也就是整个页面文字选中都会触发分享的功能。
假设新浪微博分享图标的HTML如下：
<img id="imgSinaShare" class="img_sina_share" title="将选中内容分享到新浪微博" src="http://simg.sinajs.cn/blog7style/images/common/share.gif" />
则直接：
$sinaMiniBlogShare(document.getElementById("imgSinaShare"));
就实现了选中文字分享到新浪微博的功能了。
这里的方法没有兼容性问题，IE之流，firefox或是chrome浏览器可以轻松分享；另外，方法原生的javascript代码，不依赖于任何库，所以，只要浏览器不禁用javascript，哪里都可以使用，真可谓方便快捷，无孔不入，网页开发，必备良药。
四、结语及补充
其实呢，此方法不仅支持新浪微博，支持企鹅微博（腾讯微博），狐狸微博（搜狐微博），也是可以的，只要根据各个微博分享页面的API地址，将window.open()中的地址换换就可以了，我想，应该很简单的。您要是有兴致，可以把这些乱七八糟的分享都集合到一个方法中，做出插件性质的，各个网站任意分享，通过参数接口灵活控制，估计会流行的。不过我个人不太喜欢选中一段文字后面跟着个浮动的跟屁虫，尤其是大大的跟屁虫，看着眼烦，无兴趣，所以，我是不会去整一个分享集合的插件的。
时间仓促，技术有限，文中出现表述不准确的地方在所难免，欢迎指正。
原创文章，转载请注明来自张鑫旭-鑫空间-鑫生活[http://www.zhangxinxu.com]
本文地址：http://www.zhangxinxu.com/wordpress/?p=1428




'QQ空间' => 'http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url={URL}&title={TXT}&desc=&summary=&site=&pics=',
'新浪微博' => 'http://service.weibo.com/share/share.php?url={URL}&title={TXT}&appkey=3581453612&pic=&sudaref={URL}',
'腾讯微博' => 'http://share.v.t.qq.com/index.php?c=share&a=index&url={URL}&title={TXT}&appkey=2e295ab2ff8245229d96fa3768a9f779',
'人人网' => 'http://widget.renren.com/dialog/share?resourceUrl={URL}&srcUrl=&title={TXT}&description=',
'百度贴吧' => 'http://tieba.baidu.com/f/commit/share/openShareApi?url={URL}&title={TXT}&desc=&comment=',
'开心网' => 'http://www.kaixin001.com/rest/records.php?url={URL}&style=11&content={TXT}&stime=&sig=',
'百度空间' => 'http://hi.baidu.com/pub/show/share?url={URL}&title=&content={TXT}&linkid=',
//'腾讯朋友' => 'http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?to=pengyou&url={URL}&title={TXT}&desc=&summary=&pics=',
'豆瓣' => 'http://shuo.douban.com/!service/share?href={URL}&name={TXT}&image=',
'搜狐微博' => 'http://t.sohu.com/third/post.jsp?url={URL}&title={TXT}&pic=',
'FaceBook' => 'https://www.facebook.com/sharer/sharer.php?u={URL}&t=',
'Twitter' => 'https://twitter.com/intent/tweet?source=webclient&text={TXT}',
'Google+' => 'https://plus.google.com/share?url={TXT}%20{URL}',

QQ邮件分享：

<script type="text/javascript">
(function(){
var p = {
url:location.href,
to:'qqmail',
desc:'', /*默认分享理由(可选)*/
summary:'',/*摘要(可选)*/
title:'',/*分享标题(可选)*/
site:'',/*分享来源 如：腾讯网(可选)*/
pics:'' /*分享图片的路径(可选)*/
};
var s = [];
for(var i in p){
s.push(i + '=' + encodeURIComponent(p[i]||''));
}
document.write(["<a target='_blank' ", 'href="http://mail.qq.com/cgi-bin/qm_share?', s.join("&"), '"', ' style="cursor:pointer;text-decoration:none;outline:none"><img src="http://rescdn.qqmail.com/zh_CN/htmledition/images/function/qm_open/ico_share_02.png"/></a>'].join(""));
})();
</script>