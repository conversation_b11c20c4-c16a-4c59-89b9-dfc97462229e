@charset "utf-8"; /* CSS Document for anyShare by nan.im 0330-2013 */

#anyShare { display: block; margin: 20px auto; line-height: 1.5em; }
#anyShare:after { visibility: hidden; display: block; font-size: 0; content: " "; clear: both; height: 0; }
#anyShare b { display: block; font-weight: normal; }
#anyShare i { float: right; font-size: 10px; opacity: 0.618; }
#anyShare a { float: left; height: 32px; width: 32px; margin: 2px; overflow: hidden; text-indent: -9em; opacity: 0.7; background-image: url('ICON.png'); }
#anyShare a { background-image: -webkit-image-set(url(ICON.png) 1x, url(ICON-2X.png) 2x); }
#anyShare img { float: right; width: 70px; height: 70px; border: 1px solid #DDD; box-shadow: 0 0 3px #CCC; }
#anyShare a:focus { outline: none; }
#anyShare a:hover { opacity: 1; }
#anyShare a#L766 { background-position: 35px -3px; }
#anyShare a#L398 { background-position: -3px -3px; }
#anyShare a#L161 { background-position: -38px -3px; }
#anyShare a#L301 { background-position: -73px -38px; }
#anyShare a#L280 { background-position: -38px -73px; }
#anyShare a#L315 { background-position: -38px -38px; }
#anyShare a#L472 { background-position: -3px -73px; }
#anyShare a#L937 { background-position: -108px -38px; }
#anyShare a#L115 { background-position: -73px -3px; }
#anyShare a#L944 { background-position: -3px -38px; }
#anyShare a#L050 { background-position: -108px -74px; }
#anyShare a#L918 { background-position: -73px -74px; }
