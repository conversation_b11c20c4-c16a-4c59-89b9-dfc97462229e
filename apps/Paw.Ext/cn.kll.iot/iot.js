//

loadScript('sha1.js');
// console.log(sha1('Message to hash'));


var KllSignVal = function() {

    // evaluate 定义执行后的动态变量数值
    this.evaluate = function(context) {

        var request = context.getCurrentRequest();
        // var objSign = request.getUrlParameters();
        // console.log('request::::' + JSON.stringify(request.body));
        // console.log(request.body);
        // console.log('request.BodyStr::' + request.body);
        // console.log('request.BodyObj::' + JSON.parse(request.body).timestamp);
        // console.log('bodySha1::::' + sha1(request.body).toLocaleUpperCase());

        // secret = this.kllSecret;
        // secret = '8Fzo9I6majA967DTpa5UurbKdtY4oJpa';
        console.log('thisSecret::::' + this.kllSecret);
        // console.dir(JSON.parse(request.body));

        var sign = sha1(request.body + JSON.parse(request.body).timestamp + this.kllSecret);
        // sha1( PostRequestBody + Timestamp + DeviceSecret )

        // objSign.body = request.body;
        // objSign.sign = 'appid' + objSign.appid + 'timestamp' + objSign.timestamp;
        // objSign.sign = objSign.sign + sha1(objSign.body).toLocaleUpperCase();
        // objSign.sign = sha1((objSign.sign + objSign.secret).toLocaleUpperCase());

        // console.log('objSign::::' + JSON.stringify(objSign));
        // return objSign.sign;
        return sign;
    };

    // title 显示在 value 选择列表中
    this.title = function(){
        return 'KelaileSignIOT';
    };

    // text 显示在 value title 后面
    this.text = function(){
        return '1.0';
    };

};


// identifier 必须与扩展目录名相同
KllSignVal.identifier = "cn.kll.iot";
// title 会显示在扩展管理界面
KllSignVal.title = "Kelaile Sign IOT";
// inputs 动态变量扩展专用属性，界面中点击输入值
KllSignVal.inputs = [
    // DynamicValueInput("kllAppID", "AppID：", "String"),
    DynamicValueInput("kllSecret", "Secret：", "String")
];



// 声明方法
registerDynamicValueClass(KllSignVal);

