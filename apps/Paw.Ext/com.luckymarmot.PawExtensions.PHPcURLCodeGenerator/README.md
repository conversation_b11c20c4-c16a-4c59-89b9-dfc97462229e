[![Build Status](https://travis-ci.org/luckymarmot/Paw-PHPcURLCodeGenerator.svg?branch=master)](https://travis-ci.org/luckymarmot/Paw-PHPcURLCodeGenerator)

# PHP + cURL Code Generator (Paw Extension)

A [Paw Extension](http://luckymarmot.com/paw/extensions/) that generates PHP code for the [cURL](http://php.net/manual/en/book.curl.php) library.

## Installation

Easily install this Paw Extension: [Install PHP + cURL Code Generator](http://luckymarmot.com/paw/extensions/PHPcURLCodeGenerator)

## Development

### Build & Install

```shell
npm install
cake build
cake install
```

### Watch

During development, watch for changes:

```shell
cake watch
```

## License

This Paw Extension is released under the [MIT License](LICENSE). Feel free to fork, and modify!

Copyright © 2014 Paw Inc.

## Contributors

Created by <PERSON> ([@kwent](https://github.com/kwent)). See [Contributors](https://github.com/luckymarmot/Paw-PHPcURLCodeGenerator/graphs/contributors).

## Credits

* [Mustache.js](https://github.com/janl/mustache.js/), also released under the MIT License
