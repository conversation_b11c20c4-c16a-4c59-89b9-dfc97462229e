// Generated by CoffeeScript 1.10.0
(function() {
  var EmailRandomizerDynamicValue;

  EmailRandomizerDynamicValue = function() {
    this.names = ["john.appleseed", "contact", "hello", "hi", "webmaster", "postmaster", "evon.burleigh", "joaquina.sipple", "madelene.callender", "santa.baker", "alva.marksberry", "myrl.dahl", "sana.thomas", "kacey.schueller", "rosaline.strout", "zachery.mcclary", "kimber.fallen", "billie.southall", "arvilla.casebeer", "leda.mcgaugh", "margarite.cadogan", "jonelle.bowers", "lou.devera", "ivory.ansell", "lashawnda.koester", "ena.viens", "gia.delara", "tierra.soriano", "felisha.weldy", "dion.vartanian", "valery.flemister", "lance.caster", "sam.dease", "sunshine.carreiro", "yolanda.rethman", "candance.mcgrory", "kathy.villeda", "tobie.mccafferty", "carroll.lynn", "arielle.mckellar", "reena.daniell", "shirlene.glantz", "lizeth.mazzotta", "dalton.reels", "veronique.likens", "elbert.andrzejewski", "annalee.drago", "bridgette.makuch", "renita.gebhard", "yanira.reed", "janene.dalessio", "nu.cardamone", "honey.thigpen", "florida.buzzard", "wynona.staple", "lindsy.fitzwater"];
    this.domains = ["luckymarmot.com", "apple.com", "google.com", "facebook.com", "fb.com", "gmail.com", "yahoo.com", "1033edge.com", "1coolplace.com", "1under.com", "321media.com", "4newyork.com", "aaronkwok.net", "accessgcc.com", "advalvas.be", "airforce.net", "alhilal.net", "altavista.net", "amuro.net", "animalwoman.net", "anytimenow.com", "arcor.de", "asheville.com", "assamesemail.com", "atlink.com", "australiamail.com", "bachelorgal.com", "barcelona.com", "beer.com", "bettergolf.net", "bigpond.com.au", "bimla.net", "bluehyppo.com", "bollywoodz.com", "bounce.net", "bresnan.net", "bumerang.ro", "byteme.com", "caltanet.it", "carioca.net", "celineclub.com", "chance2mail.com", "chek.com", "christianmail.net", "city-of-birmingham.com", "city-of-oxford.com", "classicmail.co.za", "cluemail.com", "collegemail.com", "computer-freak.com", "coolgoose.ca", "copacabana.com", "coxinet.net", "crwmail.com", "cyber-africa.net", "cybermail.net", "dallas.theboys.com", "deadlymob.org", "desilota.com", "diplomats.com", "doglover.com", "dotcom.fr", "dublin.ie", "e-mailanywhere.com", "eastmail.com", "edtnmail.com", "email.ee", "emailchoice.com", "emailx.net", "epix.net", "etrademail.com", "excite.it", "f1fans.net", "fastermail.com", "felicity.com", "financier.com", "fmail.co.uk", "for-president.com", "freeaccount.com", "freemail.de", "freeola.com", "freeweb.org", "from-asia.com", "from-holland.com", "fromalaska.com", "fromidaho.com", "frommassachusetts.com", "fromnewjersey.com", "fromru.com", "fromwashingtondc.com", "fullmail.com", "gamebox.net", "gci.net", "gh2000.com", "globalpagan.com", "go.ru", "gonavy.net", "gportal.hu", "guessmail.com", "hairdresser.net", "hawaii.rr.com", "helter-skelter.com", "hiphopfan.com", "home.se", "host-it.com.sg", "hotmail.com", "hsuchi.net", "i.am", "icrazy.com", "iinet.net.au", "imailbox.com", "incamail.com", "info66.com", "insidebaltimore.net", "internetbiz.com", "iowaemail.com", "isleuthmail.com", "iwon.com", "jetemail.net", "jpopmail.com", "kalpoint.com", "keg-party.com", "kinki-kids.com", "krunis.com", "lahoreoye.com", "lawyer.com", "lexis-nexis-mail.com", "list.ru", "loobie.com", "lover-boy.com", "lycos.ne.jp", "macmail.com", "mail-page.com", "mail.freetown.com", "mail.r-o-o-t.com", "mail2007.com", "mailboom.com", "mailcity.com", "mailingweb.com", "mailpost.zzn.com", "mailtag.com", "marchmail.com", "mauimail.com", "meetingmall.com", "message.hu", "millionaireintraining.com", "mobilbatam.com", "mortaza.com", "mscold.com", "mycabin.com", "mynetaddress.com", "mythirdage.com", "nakedgreens.com", "navy.org", "net4you.at", "netizen.com.ar", "netscape.net", "newmail.ru", "nikopage.com", "nyc.com", "officedomain.com", "oldies104mail.com", "onlinewiz.com", "orgmail.net", "outgun.com", "pakistanoye.com", "pconnections.net", "personal.ro", "pinoymail.com", "plasa.com", "polbox.com", "portugalmail.com", "postafree.com", "premiumservice.com", "programmer.net", "publicist.com", "quickwebmail.com", "racingmail.com", "rccgmail.org", "reggafan.com", "rin.ru", "roosh.com", "rvshop.com", "sale-sale-sale.com", "sayhi.net", "scientist.com", "seekstoyboy.com", "seznam.cz", "sialkotian.com", "skim.com", "snail-mail.ney", "socceramerica.net", "space-bank.com", "spamex.com", "sportsmail.com", "starmail.com", "stones.com", "suhabi.com", "surat.com", "swingfan.com", "t2mail.com", "teamtulsa.net", "teenagedirtbag.com", "tenchiclub.com", "thai.com", "the-beauty.com", "the-cowboy.com", "the-gentleman.com", "the-master.com", "the-russian.com", "thedoghousemail.com", "thepokerface.com", "thezhangs.net", "tiscali.lu", "torchmail.com", "truckerz.com", "u2club.com", "ukbuilder.com", "umpire.com", "unomail.com", "upf.org", "uswestmail.net", "verizonmail.com", "visitmail.com", "volcanomail.com", "wam.co.za", "web.de", "webmail.co.za", "wehshee.com", "wildmail.com", "wolf-web.com", "wouldilie.com", "wrongmail.com", "x5g.com", "yaho.com", "yahoo.com.br", "yahoo.fr", "yam.com", "yesbox.net", "yourlover.net", "youvegotmail.net", "zionweb.org"];
    this.evaluate = function() {
      var domain, domain_idx, name_idx;
      name_idx = Math.floor(Math.random() * this.names.length);
      if (this.safe) {
        domain = 'example.com';
      } else {
        domain_idx = Math.floor(Math.random() * this.domains.length);
        domain = this.domains[domain_idx];
      }
      return this.names[name_idx] + "@" + domain;
    };
    this.title = function() {
      return "Email Randomizer";
    };
  };

  EmailRandomizerDynamicValue.identifier = "com.luckymarmot.PawExtensions.EmailRandomizerDynamicValue";

  EmailRandomizerDynamicValue.title = "Email Randomizer Dynamic Value";

  EmailRandomizerDynamicValue.inputs = [
    new InputField('safe', 'Use safe emails (@example.com)', 'Checkbox', {
      defaultValue: false
    })
  ];

  registerDynamicValueClass(EmailRandomizerDynamicValue);

}).call(this);
