[![Build Status](https://travis-ci.org/luckymarmot/Paw-EmailRandomizerDynamicValue.svg?branch=master)](https://travis-ci.org/luckymarmot/Paw-EmailRandomizerDynamicValue)

#Email Randomizer Dynamic Value (Paw Extension)

A [Paw Extension](http://luckymarmot.com/paw/extensions/) that generates random email addresses.

## Installation

Easily install this Paw Extension: [Install Email Randomizer Dynamic Value](http://luckymarmot.com/paw/extensions/EmailRandomizerDynamicValue)

## Development

### Build & Install

```shell
npm install
cake build
cake install
```

### Watch

During development, watch for changes:

```shell
cake watch
```

##License

This Paw Extension is released under the [MIT License](LICENSE). Feel free to fork, and modify!

Copyright © 2014 Paw Inc.

##Contributors

See [Contributors](https://github.com/luckymarmot/Paw-EmailRandomizerDynamicValue/graphs/contributors).
