language: node_js
node_js:
- '0.10'
before_install:
- npm install coffee-script
before_script:
- ./node_modules/.bin/cake archive
script:
- ./node_modules/.bin/cake test
deploy:
  provider: releases
  api_key:
    secure: hdwXGQ5Fd0BjchR50kyUvLqUlGTQoaYxd+iNilsvdkQaL7b+pMi+1OQRyCjKBXcjNK5m1VbahvlIp8f1LemSBl7wgWVk8oO6JxPl2sWt2M6ZBoh7aWXMGdLG3tAUbBEARf1FPeJIuh3XnRqQTGv90KrZtCktW244uhQ/+Q0SjDI=
  file: build/RandomUuidValue.zip
  skip_cleanup: true
  on:
    tags: true
    all_branches: true
    repo: hskrasek/Paw-UUIDDynamicValue