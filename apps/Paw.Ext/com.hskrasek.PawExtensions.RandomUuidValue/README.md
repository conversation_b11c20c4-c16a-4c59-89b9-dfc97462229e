Paw UUID Dynamic Value
======================

[![Build Status](https://img.shields.io/travis/hskrasek/Paw-UUIDDynamicValue/master.svg?style=flat)](https://travis-ci.org/hskrasek/Paw-UUIDDynamicValue)

This extension allows for you to dynamically insert a UUID into your request. The generated UUID conforms to Version 4 of [RFC4122](http://en.wikipedia.org/wiki/Universally_unique_identifier#Version_4_.28random.29).

### License

MIT License. See the [LICENSE](LICENSE) file.