// {{{request.name}}} ({{{method}}} {{{url.base}}})

{{! ================ multipart body ================ }}
{{#body.has_multipart_body}}
var formData = new FormData();
{{#body.multipart_body}}
formData.append("{{{name}}}", "{{{value}}}");
{{/body.multipart_body}}

{{/body.has_multipart_body}}
jQuery.ajax({
    {{! ================ url + url params ================ }}
    {{#has_content_and_url_params}}
    url: "{{{url.base}}}?" + jQuery.param({
    {{#url.params}}
        "{{{name}}}": "{{{value}}}",
    {{/url.params}}
    }),
    {{/has_content_and_url_params}}
    {{! ================ url (base) ================ }}
    {{^has_content_and_url_params}}
    url: "{{{url.base}}}",
    {{/has_content_and_url_params}}
    {{! ================ method ================ }}
    type: "{{{method}}}",
    {{! ================ url parameters ================ }}
    {{^has_content_and_url_params}}
    {{#url.has_params}}
    data: {
    {{#url.params}}
        "{{{name}}}": "{{{value}}}",
    {{/url.params}}
    },
    {{/url.has_params}}
    {{/has_content_and_url_params}}
    {{! ================ headers ================ }}
    {{#headers.has_headers}}
    headers: {
        {{#headers.header_list}}
        "{{{header_name}}}": "{{{header_value}}}",
        {{/headers.header_list}}
    },
    {{/headers.has_headers}}
    {{! ================ raw body ================ }}
    {{#body.has_raw_body}}
    processData: false,
    data: "{{{body.raw_body}}}",
    {{/body.has_raw_body}}
    {{! ================ too long body ================ }}
    {{#body.has_long_body}}
    processData: false,
    data: "", // set your body string
    {{/body.has_long_body}}
    {{! ================ url-encoded body ================ }}
    {{#body.has_url_encoded_body}}
    contentType: "application/x-www-form-urlencoded",
    data: {
    {{#body.url_encoded_body}}
        "{{{name}}}": "{{{value}}}",
    {{/body.url_encoded_body}}
    },
    {{/body.has_url_encoded_body}}
    {{! ================ multipart body ================ }}
    {{#body.has_multipart_body}}
    processData: false,
    contentType: false,
    data: formData,
    {{/body.has_multipart_body}}
    {{! ================ json body ================ }}
    {{#body.has_json_body}}
    contentType: "application/json",
    data: JSON.stringify({{{body.json_body_object}}})
    {{/body.has_json_body}}
})
.done(function(data, textStatus, jqXHR) {
    console.log("HTTP Request Succeeded: " + jqXHR.status);
    console.log(data);
})
.fail(function(jqXHR, textStatus, errorThrown) {
    console.log("HTTP Request Failed");
})
.always(function() {
    /* ... */
});
