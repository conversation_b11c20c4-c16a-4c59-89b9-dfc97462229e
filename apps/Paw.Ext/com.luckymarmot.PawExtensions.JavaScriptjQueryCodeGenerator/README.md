[![Build Status](https://travis-ci.org/luckymarmot/Paw-JavaScriptjQueryCodeGenerator.svg?branch=master)](https://travis-ci.org/luckymarmot/Paw-JavaScriptjQueryCodeGenerator)

# JavaScript jQuery Code Generator (Paw Extension)

A [Paw Extension](http://luckymarmot.com/paw/extensions/) that generates JavaScript code for AJAX requests using [jQuery](http://jquery.com/).

## Installation

Easily install this Paw Extension: [Install JavaScript + jQuery Code Generator](http://luckymarmot.com/paw/extensions/JavaScriptjQueryCodeGenerator)

## Development

### Build & Install

```shell
npm install
cake build
cake install
```

### Watch

During development, watch for changes:

```shell
cake watch
```

## License

This Paw Extension is released under the [MIT License](LICENSE). Feel free to fork, and modify!

Copyright © 2014 Paw Inc.

## Contributors

See [Contributors](https://github.com/luckymarmot/Paw-JavaScriptjQueryCodeGenerator/graphs/contributors).

## Credits

* [Mustache.js](https://github.com/janl/mustache.js/), also released under the MIT License
* [URI.js](http://medialize.github.io/URI.js/), also released under the MIT License
