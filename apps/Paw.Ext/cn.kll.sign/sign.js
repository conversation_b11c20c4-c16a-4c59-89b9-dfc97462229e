//

loadScript('sha1.js');
// console.log(sha1('Message to hash'));


var KllSignVal = function() {

    // evaluate 定义执行后的动态变量数值
    this.evaluate = function(context) {

        var request = context.getCurrentRequest();
        var objSign = request.getUrlParameters();
        // console.log('request::::' + JSON.stringify(request));
        // console.log('bodyJson::::' + request.body);
        // console.log('bodySha1::::' + sha1(request.body).toLocaleUpperCase());

        objSign.secret = this.kllSecret;
        // console.log('thisSecret::::' + objSign.secret);

        objSign.body = request.body;
        objSign.sign = 'appid' + objSign.appid + 'timestamp' + objSign.timestamp;
        objSign.sign = objSign.sign + sha1(objSign.body).toLocaleUpperCase();
        objSign.sign = sha1((objSign.sign + objSign.secret).toLocaleUpperCase());

        // console.log('objSign::::' + JSON.stringify(objSign));
        return objSign.sign;
    };

    // title 显示在 value 选择列表中
    this.title = function(){
        return 'KelaileSign';
    };

    // text 显示在 value title 后面
    this.text = function(){
        return '2.0';
    };

};


// identifier 必须与扩展目录名相同
KllSignVal.identifier = "cn.kll.sign";
// title 会显示在扩展管理界面
KllSignVal.title = "Kelaile Sign API";
// inputs 动态变量扩展专用属性，界面中点击输入值
KllSignVal.inputs = [
    // DynamicValueInput("kllAppID", "AppID：", "String"),
    DynamicValueInput("kllSecret", "Secret：", "String")
];



// 声明方法
registerDynamicValueClass(KllSignVal);

