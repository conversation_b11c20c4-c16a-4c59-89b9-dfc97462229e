<?php

// http://dev.nan.im/apps/chk2push/?pushapi=http%3A%2F%2Fbwg.nan.im%3A8800&pushkey=PDU1T3J2IboyBolHg5l37NO3PsgfJRIvagN4D

// $_REQUEST = [
// 	'pushapi' => 'http://bwg.nan.im:8800',
// 	'pushkey' => 'PDU1T3J2IboyBolHg5l37NO3PsgfJRIvagN4D',
// 	'id'      => 'fdebdc8095aad491b9a9f4b4901642dd',       // 监测任务id，用于识别结果属于哪个任务
// 	'url'     => 'https://time.is/',                       // 原始监测URL，用于识别结果属于哪个任务
// 	'value'   => '123',                                    // 监测到的纯文本内容
// 	'html'    => '<time id=\"clock\">17:32:53</time>',     // 监测到的HTML内容
// 	'link'    => 'https://time.is/',                       // 监测内容对应的URL
// ];

if(7 > count($_REQUEST)) exit;

// ini_set("SMTP", "aspmx.l.google.com");
// ini_set("sendmail_from", "<EMAIL>");
// ;auth_username=
// ;auth_password=
// exit();

// file_put_contents('index.log',json_encode($_REQUEST).PHP_EOL,FILE_APPEND);
file_get_contents($_REQUEST['pushapi'].'/message/push', false, stream_context_create([
	'http' => array(
		'method' => 'POST',
		'header' => 'Content-type: application/x-www-form-urlencoded',
		'content' => http_build_query([
			'pushkey' => $_REQUEST['pushkey'],
			'type' => 'text',
			'text' => $_REQUEST['value'],
			'desp' => $_REQUEST['url'],
		])
	)
]));
