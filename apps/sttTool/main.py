#!/Users/<USER>/Sites/im.nan/apps/sttTool/.venv/bin/python

import json
import os
import sys
import glob
from http import HTTPStatus
from urllib.request import urlretrieve
from urllib.parse import unquote

import oss2
from oss2.credentials import EnvironmentVariableCredentialsProvider
from dashscope import Generation
from dashscope.audio.asr import Transcription


# 检查环境变量
for var in [
    'DASHSCOPE_API_KEY',
    'OSS_REGION',
    'OSS_ENDPOINT',
    'OSS_BUCKET_DEV',
    'OSS_ACCESS_KEY_ID',
    'OSS_ACCESS_KEY_SECRET',
]:
    if var not in os.environ:
        print(f"提醒: 环境变量 {var} 没有找到")
        exit()

for dir_name in ["ASR", "MP3", "TXT"]:
    if not os.path.exists(dir_name):
        os.mkdir(dir_name)


# 阿里云 OSS API：https://help.aliyun.com/zh/oss/developer-reference/
bucket = oss2.Bucket(
    oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider()),
    os.getenv("OSS_ENDPOINT"),
    os.getenv("OSS_BUCKET_DEV"),
    region=os.getenv("OSS_REGION"))

# 阿里云 OSS 上传
def handle_mp3_to_oss():
    mp3_list = glob.glob(f"MP3/*.mp3")
    print(f"\n读取：共 {len(mp3_list)} 个 MP3 音频")
    for mp3_file in mp3_list:
        with open(mp3_file, "rb") as f:
            bucket.put_object_from_file(mp3_file, mp3_file)
            print("上传：" + mp3_file)

# 阿里云 OSS MP3 to ASR
def handle_oss_to_asr():

    all_urls = []
    for obj in oss2.ObjectIteratorV2(bucket, prefix="MP3/"):
        if obj.key.endswith('.mp3'):
            all_urls.append(bucket.sign_url('GET', obj.key, 3600, slash_safe=True))

    print(f"\n读取：共 {len(all_urls)} 个 OSS 音频")
    if len(all_urls) == 0 or len(all_urls) > 100:
        print("结束: 无 MP3 或超 100 个")
        exit()

    # 阿里云 ASR API：https://help.aliyun.com/zh/model-studio/user-guide/automatic-speech-recognition
    asr_task = Transcription.async_call(
        model='paraformer-v2',
        file_urls=all_urls,
        language_hints=['zh','en'])

    asr_done = Transcription.wait(task=asr_task.output.task_id)

    if asr_done.status_code == HTTPStatus.OK:
        for result in asr_done.output['results']:
            mp3_link = result['file_url']
            asr_link = result['transcription_url']
            asr_name = mp3_link[mp3_link.index("/MP3/")+5:mp3_link.index(".mp3?")]
            asr_name = unquote(asr_name).replace("/", "_")
            urlretrieve(asr_link, "ASR/" + asr_name + ".json")
            print('完成: ' + asr_name)
    else:
        print('错误: ', asr_done.output.message)

# 阿里云 ASR to TXT
def handle_asr_to_txt():

    asr_list = glob.glob(f"ASR/*.json")
    print(f"\n读取：共 {len(asr_list)} 个 ASR 翻译")

    for asr_file in asr_list:
        txt_name = asr_file[asr_file.index('ASR/')+4:asr_file.index('.json')]
        print(f'读取：{txt_name}')
        with open(asr_file, 'r', encoding='utf-8') as f:
            asr_data = json.load(f)
            asr_text = asr_data['transcripts'][0]['text']
            asr_text = asr_text.replace("欢迎来到真明", "欢迎来到曾鸣")
            asr_text = asr_text.replace("我是真明", "我是曾鸣")
            asr_text = asr_text.replace("我是刘兰", "我是刘澜")
            asr_text = asr_text.replace("辽宁产品思维", "梁宁产品思维")
        if not asr_text:
            print(f"跳过：{txt_name}")
            continue

        # 阿里云模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
        result = Generation.call(
            model="qwen-turbo",
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            messages=[
                {'role': 'system', 'content': '您是录音稿文字校准专家，请在尽量保持原文的基础上进行必要的校准，要求如下：将繁体中文转换为简体中文，修正录音稿常见的错别字多音字，在适当的位置添加或修改标点符号，按照逻辑拆分为多个段落。只回复校准后的转录稿内容，不要附带其他任何说明或解释。'},
                {'role': 'user', 'content': asr_text },
            ],
        )
        # print(result)
        if result.status_code == 200:
            with open(f"TXT/{txt_name}.txt", 'w', encoding='utf-8') as f:
                f.write(result.output.text)
                print('完成: ' + txt_name)
        else:
            print(f"返回码：{result.status_code} 错误码：{result.code} 错误信息：{result.message}")
            # 错误码表：https://help.aliyun.com/zh/model-studio/developer-reference/error-code


def main():
    if "--all" in sys.argv:
        handle_mp3_to_oss()
        handle_oss_to_asr()
        handle_asr_to_txt()
    if "--mp3_to_oss" in sys.argv:
        handle_mp3_to_oss()
    if "--oss_to_asr" in sys.argv:
        handle_oss_to_asr()
    if "--asr_to_txt" in sys.argv:
        handle_asr_to_txt()
    if len(sys.argv) == 1:
        print("用法: ./OSS2TXT.py [选项]")
        print("选项:")
        print("\t--all\t\t上传MP3到OSS识别后校准成TXT文本")
        print("\t--mp3_to_oss\t上传MP3到OSS")
        print("\t--oss_to_asr\t读取OSS到ASR识别")
        print("\t--asr_to_txt\t读取ASR识别结果转换为校正后的文本")
        return

if __name__ == "__main__":
    main()
