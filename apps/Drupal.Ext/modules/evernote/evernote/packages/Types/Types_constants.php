<?php
namespace EDAM\Types;
/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
include_once $GLOBALS['THRIFT_ROOT'].'/packages/Types/Types_types.php';

$GLOBALS['EDAM_Types_Types_CONSTANTS'] = array();

$GLOBALS['EDAM_Types_Types_CONSTANTS']['CLASSIFICATION_RECIPE_USER_NON_RECIPE'] = "000";

$GLOBALS['EDAM_Types_Types_CONSTANTS']['CLASSIFICATION_RECIPE_USER_RECIPE'] = "001";

$GLOBALS['EDAM_Types_Types_CONSTANTS']['CLASSIFICATION_RECIPE_SERVICE_RECIPE'] = "002";

$GLOBALS['EDAM_Types_Types_CONSTANTS']['EDAM_NOTE_SOURCE_WEB_CLIP'] = "web.clip";

$GLOBALS['EDAM_Types_Types_CONSTANTS']['EDAM_NOTE_SOURCE_MAIL_CLIP'] = "mail.clip";

$GLOBALS['EDAM_Types_Types_CONSTANTS']['EDAM_NOTE_SOURCE_MAIL_SMTP_GATEWAY'] = "mail.smtp";

?>
