<?php
namespace EDAM\Types;
/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
include_once $GLOBALS['THRIFT_ROOT'].'/Thrift.php';

include_once $GLOBALS['THRIFT_ROOT'].'/packages/Limits/Limits_types.php';

$GLOBALS['\EDAM\Types\E_PrivilegeLevel'] = array(
  'NORMAL' => 1,
  'PREMIUM' => 3,
  'VIP' => 5,
  'MANAGER' => 7,
  'SUPPORT' => 8,
  'ADMIN' => 9,
);

final class PrivilegeLevel {
  const NORMAL = 1;
  const PREMIUM = 3;
  const VIP = 5;
  const MANAGER = 7;
  const SUPPORT = 8;
  const ADMIN = 9;
  static public $__names = array(
    1 => 'NORMAL',
    3 => 'PREMIUM',
    5 => 'VIP',
    7 => 'MANAGER',
    8 => 'SUPPORT',
    9 => 'ADMIN',
  );
}

$GLOBALS['\EDAM\Types\E_QueryFormat'] = array(
  'USER' => 1,
  'SEXP' => 2,
);

final class QueryFormat {
  const USER = 1;
  const SEXP = 2;
  static public $__names = array(
    1 => 'USER',
    2 => 'SEXP',
  );
}

$GLOBALS['\EDAM\Types\E_NoteSortOrder'] = array(
  'CREATED' => 1,
  'UPDATED' => 2,
  'RELEVANCE' => 3,
  'UPDATE_SEQUENCE_NUMBER' => 4,
  'TITLE' => 5,
);

final class NoteSortOrder {
  const CREATED = 1;
  const UPDATED = 2;
  const RELEVANCE = 3;
  const UPDATE_SEQUENCE_NUMBER = 4;
  const TITLE = 5;
  static public $__names = array(
    1 => 'CREATED',
    2 => 'UPDATED',
    3 => 'RELEVANCE',
    4 => 'UPDATE_SEQUENCE_NUMBER',
    5 => 'TITLE',
  );
}

$GLOBALS['\EDAM\Types\E_PremiumOrderStatus'] = array(
  'NONE' => 0,
  'PENDING' => 1,
  'ACTIVE' => 2,
  'FAILED' => 3,
  'CANCELLATION_PENDING' => 4,
  'CANCELED' => 5,
);

final class PremiumOrderStatus {
  const NONE = 0;
  const PENDING = 1;
  const ACTIVE = 2;
  const FAILED = 3;
  const CANCELLATION_PENDING = 4;
  const CANCELED = 5;
  static public $__names = array(
    0 => 'NONE',
    1 => 'PENDING',
    2 => 'ACTIVE',
    3 => 'FAILED',
    4 => 'CANCELLATION_PENDING',
    5 => 'CANCELED',
  );
}

$GLOBALS['\EDAM\Types\E_SharedNotebookPrivilegeLevel'] = array(
  'READ_NOTEBOOK' => 0,
  'MODIFY_NOTEBOOK_PLUS_ACTIVITY' => 1,
  'READ_NOTEBOOK_PLUS_ACTIVITY' => 2,
  'GROUP' => 3,
  'FULL_ACCESS' => 4,
  'BUSINESS_FULL_ACCESS' => 5,
);

final class SharedNotebookPrivilegeLevel {
  const READ_NOTEBOOK = 0;
  const MODIFY_NOTEBOOK_PLUS_ACTIVITY = 1;
  const READ_NOTEBOOK_PLUS_ACTIVITY = 2;
  const GROUP = 3;
  const FULL_ACCESS = 4;
  const BUSINESS_FULL_ACCESS = 5;
  static public $__names = array(
    0 => 'READ_NOTEBOOK',
    1 => 'MODIFY_NOTEBOOK_PLUS_ACTIVITY',
    2 => 'READ_NOTEBOOK_PLUS_ACTIVITY',
    3 => 'GROUP',
    4 => 'FULL_ACCESS',
    5 => 'BUSINESS_FULL_ACCESS',
  );
}

$GLOBALS['\EDAM\Types\E_SponsoredGroupRole'] = array(
  'GROUP_MEMBER' => 1,
  'GROUP_ADMIN' => 2,
  'GROUP_OWNER' => 3,
);

final class SponsoredGroupRole {
  const GROUP_MEMBER = 1;
  const GROUP_ADMIN = 2;
  const GROUP_OWNER = 3;
  static public $__names = array(
    1 => 'GROUP_MEMBER',
    2 => 'GROUP_ADMIN',
    3 => 'GROUP_OWNER',
  );
}

$GLOBALS['\EDAM\Types\E_BusinessUserRole'] = array(
  'ADMIN' => 1,
  'NORMAL' => 2,
);

final class BusinessUserRole {
  const ADMIN = 1;
  const NORMAL = 2;
  static public $__names = array(
    1 => 'ADMIN',
    2 => 'NORMAL',
  );
}

$GLOBALS['\EDAM\Types\E_SharedNotebookInstanceRestrictions'] = array(
  'ONLY_JOINED_OR_PREVIEW' => 1,
  'NO_SHARED_NOTEBOOKS' => 2,
);

final class SharedNotebookInstanceRestrictions {
  const ONLY_JOINED_OR_PREVIEW = 1;
  const NO_SHARED_NOTEBOOKS = 2;
  static public $__names = array(
    1 => 'ONLY_JOINED_OR_PREVIEW',
    2 => 'NO_SHARED_NOTEBOOKS',
  );
}

$GLOBALS['\EDAM\Types\E_ReminderEmailConfig'] = array(
  'DO_NOT_SEND' => 1,
  'SEND_DAILY_EMAIL' => 2,
);

final class ReminderEmailConfig {
  const DO_NOT_SEND = 1;
  const SEND_DAILY_EMAIL = 2;
  static public $__names = array(
    1 => 'DO_NOT_SEND',
    2 => 'SEND_DAILY_EMAIL',
  );
}

class Data {
  static $_TSPEC;

  public $bodyHash = null;
  public $size = null;
  public $body = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'bodyHash',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'size',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'body',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['bodyHash'])) {
        $this->bodyHash = $vals['bodyHash'];
      }
      if (isset($vals['size'])) {
        $this->size = $vals['size'];
      }
      if (isset($vals['body'])) {
        $this->body = $vals['body'];
      }
    }
  }

  public function getName() {
    return 'Data';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->bodyHash);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->size);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->body);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Data');
    if ($this->bodyHash !== null) {
      $xfer += $output->writeFieldBegin('bodyHash', \TType::STRING, 1);
      $xfer += $output->writeString($this->bodyHash);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->size !== null) {
      $xfer += $output->writeFieldBegin('size', \TType::I32, 2);
      $xfer += $output->writeI32($this->size);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->body !== null) {
      $xfer += $output->writeFieldBegin('body', \TType::STRING, 3);
      $xfer += $output->writeString($this->body);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserAttributes {
  static $_TSPEC;

  public $defaultLocationName = null;
  public $defaultLatitude = null;
  public $defaultLongitude = null;
  public $preactivation = null;
  public $viewedPromotions = null;
  public $incomingEmailAddress = null;
  public $recentMailedAddresses = null;
  public $comments = null;
  public $dateAgreedToTermsOfService = null;
  public $maxReferrals = null;
  public $referralCount = null;
  public $refererCode = null;
  public $sentEmailDate = null;
  public $sentEmailCount = null;
  public $dailyEmailLimit = null;
  public $emailOptOutDate = null;
  public $partnerEmailOptInDate = null;
  public $preferredLanguage = null;
  public $preferredCountry = null;
  public $clipFullPage = null;
  public $twitterUserName = null;
  public $twitterId = null;
  public $groupName = null;
  public $recognitionLanguage = null;
  public $referralProof = null;
  public $educationalDiscount = null;
  public $businessAddress = null;
  public $hideSponsorBilling = null;
  public $taxExempt = null;
  public $useEmailAutoFiling = null;
  public $reminderEmailConfig = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'defaultLocationName',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'defaultLatitude',
          'type' => \TType::DOUBLE,
          ),
        3 => array(
          'var' => 'defaultLongitude',
          'type' => \TType::DOUBLE,
          ),
        4 => array(
          'var' => 'preactivation',
          'type' => \TType::BOOL,
          ),
        5 => array(
          'var' => 'viewedPromotions',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        6 => array(
          'var' => 'incomingEmailAddress',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'recentMailedAddresses',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        9 => array(
          'var' => 'comments',
          'type' => \TType::STRING,
          ),
        11 => array(
          'var' => 'dateAgreedToTermsOfService',
          'type' => \TType::I64,
          ),
        12 => array(
          'var' => 'maxReferrals',
          'type' => \TType::I32,
          ),
        13 => array(
          'var' => 'referralCount',
          'type' => \TType::I32,
          ),
        14 => array(
          'var' => 'refererCode',
          'type' => \TType::STRING,
          ),
        15 => array(
          'var' => 'sentEmailDate',
          'type' => \TType::I64,
          ),
        16 => array(
          'var' => 'sentEmailCount',
          'type' => \TType::I32,
          ),
        17 => array(
          'var' => 'dailyEmailLimit',
          'type' => \TType::I32,
          ),
        18 => array(
          'var' => 'emailOptOutDate',
          'type' => \TType::I64,
          ),
        19 => array(
          'var' => 'partnerEmailOptInDate',
          'type' => \TType::I64,
          ),
        20 => array(
          'var' => 'preferredLanguage',
          'type' => \TType::STRING,
          ),
        21 => array(
          'var' => 'preferredCountry',
          'type' => \TType::STRING,
          ),
        22 => array(
          'var' => 'clipFullPage',
          'type' => \TType::BOOL,
          ),
        23 => array(
          'var' => 'twitterUserName',
          'type' => \TType::STRING,
          ),
        24 => array(
          'var' => 'twitterId',
          'type' => \TType::STRING,
          ),
        25 => array(
          'var' => 'groupName',
          'type' => \TType::STRING,
          ),
        26 => array(
          'var' => 'recognitionLanguage',
          'type' => \TType::STRING,
          ),
        28 => array(
          'var' => 'referralProof',
          'type' => \TType::STRING,
          ),
        29 => array(
          'var' => 'educationalDiscount',
          'type' => \TType::BOOL,
          ),
        30 => array(
          'var' => 'businessAddress',
          'type' => \TType::STRING,
          ),
        31 => array(
          'var' => 'hideSponsorBilling',
          'type' => \TType::BOOL,
          ),
        32 => array(
          'var' => 'taxExempt',
          'type' => \TType::BOOL,
          ),
        33 => array(
          'var' => 'useEmailAutoFiling',
          'type' => \TType::BOOL,
          ),
        34 => array(
          'var' => 'reminderEmailConfig',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['defaultLocationName'])) {
        $this->defaultLocationName = $vals['defaultLocationName'];
      }
      if (isset($vals['defaultLatitude'])) {
        $this->defaultLatitude = $vals['defaultLatitude'];
      }
      if (isset($vals['defaultLongitude'])) {
        $this->defaultLongitude = $vals['defaultLongitude'];
      }
      if (isset($vals['preactivation'])) {
        $this->preactivation = $vals['preactivation'];
      }
      if (isset($vals['viewedPromotions'])) {
        $this->viewedPromotions = $vals['viewedPromotions'];
      }
      if (isset($vals['incomingEmailAddress'])) {
        $this->incomingEmailAddress = $vals['incomingEmailAddress'];
      }
      if (isset($vals['recentMailedAddresses'])) {
        $this->recentMailedAddresses = $vals['recentMailedAddresses'];
      }
      if (isset($vals['comments'])) {
        $this->comments = $vals['comments'];
      }
      if (isset($vals['dateAgreedToTermsOfService'])) {
        $this->dateAgreedToTermsOfService = $vals['dateAgreedToTermsOfService'];
      }
      if (isset($vals['maxReferrals'])) {
        $this->maxReferrals = $vals['maxReferrals'];
      }
      if (isset($vals['referralCount'])) {
        $this->referralCount = $vals['referralCount'];
      }
      if (isset($vals['refererCode'])) {
        $this->refererCode = $vals['refererCode'];
      }
      if (isset($vals['sentEmailDate'])) {
        $this->sentEmailDate = $vals['sentEmailDate'];
      }
      if (isset($vals['sentEmailCount'])) {
        $this->sentEmailCount = $vals['sentEmailCount'];
      }
      if (isset($vals['dailyEmailLimit'])) {
        $this->dailyEmailLimit = $vals['dailyEmailLimit'];
      }
      if (isset($vals['emailOptOutDate'])) {
        $this->emailOptOutDate = $vals['emailOptOutDate'];
      }
      if (isset($vals['partnerEmailOptInDate'])) {
        $this->partnerEmailOptInDate = $vals['partnerEmailOptInDate'];
      }
      if (isset($vals['preferredLanguage'])) {
        $this->preferredLanguage = $vals['preferredLanguage'];
      }
      if (isset($vals['preferredCountry'])) {
        $this->preferredCountry = $vals['preferredCountry'];
      }
      if (isset($vals['clipFullPage'])) {
        $this->clipFullPage = $vals['clipFullPage'];
      }
      if (isset($vals['twitterUserName'])) {
        $this->twitterUserName = $vals['twitterUserName'];
      }
      if (isset($vals['twitterId'])) {
        $this->twitterId = $vals['twitterId'];
      }
      if (isset($vals['groupName'])) {
        $this->groupName = $vals['groupName'];
      }
      if (isset($vals['recognitionLanguage'])) {
        $this->recognitionLanguage = $vals['recognitionLanguage'];
      }
      if (isset($vals['referralProof'])) {
        $this->referralProof = $vals['referralProof'];
      }
      if (isset($vals['educationalDiscount'])) {
        $this->educationalDiscount = $vals['educationalDiscount'];
      }
      if (isset($vals['businessAddress'])) {
        $this->businessAddress = $vals['businessAddress'];
      }
      if (isset($vals['hideSponsorBilling'])) {
        $this->hideSponsorBilling = $vals['hideSponsorBilling'];
      }
      if (isset($vals['taxExempt'])) {
        $this->taxExempt = $vals['taxExempt'];
      }
      if (isset($vals['useEmailAutoFiling'])) {
        $this->useEmailAutoFiling = $vals['useEmailAutoFiling'];
      }
      if (isset($vals['reminderEmailConfig'])) {
        $this->reminderEmailConfig = $vals['reminderEmailConfig'];
      }
    }
  }

  public function getName() {
    return 'UserAttributes';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->defaultLocationName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->defaultLatitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->defaultLongitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->preactivation);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::LST) {
            $this->viewedPromotions = array();
            $_size0 = 0;
            $_etype3 = 0;
            $xfer += $input->readListBegin($_etype3, $_size0);
            for ($_i4 = 0; $_i4 < $_size0; ++$_i4)
            {
              $elem5 = null;
              $xfer += $input->readString($elem5);
              $this->viewedPromotions []= $elem5;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->incomingEmailAddress);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::LST) {
            $this->recentMailedAddresses = array();
            $_size6 = 0;
            $_etype9 = 0;
            $xfer += $input->readListBegin($_etype9, $_size6);
            for ($_i10 = 0; $_i10 < $_size6; ++$_i10)
            {
              $elem11 = null;
              $xfer += $input->readString($elem11);
              $this->recentMailedAddresses []= $elem11;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->comments);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->dateAgreedToTermsOfService);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->maxReferrals);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->referralCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->refererCode);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 15:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->sentEmailDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 16:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->sentEmailCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 17:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->dailyEmailLimit);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 18:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->emailOptOutDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 19:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->partnerEmailOptInDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 20:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->preferredLanguage);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 21:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->preferredCountry);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 22:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->clipFullPage);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 23:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->twitterUserName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 24:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->twitterId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 25:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->groupName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 26:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->recognitionLanguage);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 28:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->referralProof);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 29:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->educationalDiscount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 30:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->businessAddress);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 31:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->hideSponsorBilling);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 32:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->taxExempt);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 33:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->useEmailAutoFiling);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 34:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->reminderEmailConfig);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserAttributes');
    if ($this->defaultLocationName !== null) {
      $xfer += $output->writeFieldBegin('defaultLocationName', \TType::STRING, 1);
      $xfer += $output->writeString($this->defaultLocationName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->defaultLatitude !== null) {
      $xfer += $output->writeFieldBegin('defaultLatitude', \TType::DOUBLE, 2);
      $xfer += $output->writeDouble($this->defaultLatitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->defaultLongitude !== null) {
      $xfer += $output->writeFieldBegin('defaultLongitude', \TType::DOUBLE, 3);
      $xfer += $output->writeDouble($this->defaultLongitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->preactivation !== null) {
      $xfer += $output->writeFieldBegin('preactivation', \TType::BOOL, 4);
      $xfer += $output->writeBool($this->preactivation);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->viewedPromotions !== null) {
      if (!is_array($this->viewedPromotions)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('viewedPromotions', \TType::LST, 5);
      {
        $output->writeListBegin(\TType::STRING, count($this->viewedPromotions));
        {
          foreach ($this->viewedPromotions as $iter12)
          {
            $xfer += $output->writeString($iter12);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->incomingEmailAddress !== null) {
      $xfer += $output->writeFieldBegin('incomingEmailAddress', \TType::STRING, 6);
      $xfer += $output->writeString($this->incomingEmailAddress);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->recentMailedAddresses !== null) {
      if (!is_array($this->recentMailedAddresses)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('recentMailedAddresses', \TType::LST, 7);
      {
        $output->writeListBegin(\TType::STRING, count($this->recentMailedAddresses));
        {
          foreach ($this->recentMailedAddresses as $iter13)
          {
            $xfer += $output->writeString($iter13);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->comments !== null) {
      $xfer += $output->writeFieldBegin('comments', \TType::STRING, 9);
      $xfer += $output->writeString($this->comments);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->dateAgreedToTermsOfService !== null) {
      $xfer += $output->writeFieldBegin('dateAgreedToTermsOfService', \TType::I64, 11);
      $xfer += $output->writeI64($this->dateAgreedToTermsOfService);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->maxReferrals !== null) {
      $xfer += $output->writeFieldBegin('maxReferrals', \TType::I32, 12);
      $xfer += $output->writeI32($this->maxReferrals);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->referralCount !== null) {
      $xfer += $output->writeFieldBegin('referralCount', \TType::I32, 13);
      $xfer += $output->writeI32($this->referralCount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->refererCode !== null) {
      $xfer += $output->writeFieldBegin('refererCode', \TType::STRING, 14);
      $xfer += $output->writeString($this->refererCode);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sentEmailDate !== null) {
      $xfer += $output->writeFieldBegin('sentEmailDate', \TType::I64, 15);
      $xfer += $output->writeI64($this->sentEmailDate);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sentEmailCount !== null) {
      $xfer += $output->writeFieldBegin('sentEmailCount', \TType::I32, 16);
      $xfer += $output->writeI32($this->sentEmailCount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->dailyEmailLimit !== null) {
      $xfer += $output->writeFieldBegin('dailyEmailLimit', \TType::I32, 17);
      $xfer += $output->writeI32($this->dailyEmailLimit);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->emailOptOutDate !== null) {
      $xfer += $output->writeFieldBegin('emailOptOutDate', \TType::I64, 18);
      $xfer += $output->writeI64($this->emailOptOutDate);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->partnerEmailOptInDate !== null) {
      $xfer += $output->writeFieldBegin('partnerEmailOptInDate', \TType::I64, 19);
      $xfer += $output->writeI64($this->partnerEmailOptInDate);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->preferredLanguage !== null) {
      $xfer += $output->writeFieldBegin('preferredLanguage', \TType::STRING, 20);
      $xfer += $output->writeString($this->preferredLanguage);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->preferredCountry !== null) {
      $xfer += $output->writeFieldBegin('preferredCountry', \TType::STRING, 21);
      $xfer += $output->writeString($this->preferredCountry);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->clipFullPage !== null) {
      $xfer += $output->writeFieldBegin('clipFullPage', \TType::BOOL, 22);
      $xfer += $output->writeBool($this->clipFullPage);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->twitterUserName !== null) {
      $xfer += $output->writeFieldBegin('twitterUserName', \TType::STRING, 23);
      $xfer += $output->writeString($this->twitterUserName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->twitterId !== null) {
      $xfer += $output->writeFieldBegin('twitterId', \TType::STRING, 24);
      $xfer += $output->writeString($this->twitterId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->groupName !== null) {
      $xfer += $output->writeFieldBegin('groupName', \TType::STRING, 25);
      $xfer += $output->writeString($this->groupName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->recognitionLanguage !== null) {
      $xfer += $output->writeFieldBegin('recognitionLanguage', \TType::STRING, 26);
      $xfer += $output->writeString($this->recognitionLanguage);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->referralProof !== null) {
      $xfer += $output->writeFieldBegin('referralProof', \TType::STRING, 28);
      $xfer += $output->writeString($this->referralProof);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->educationalDiscount !== null) {
      $xfer += $output->writeFieldBegin('educationalDiscount', \TType::BOOL, 29);
      $xfer += $output->writeBool($this->educationalDiscount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessAddress !== null) {
      $xfer += $output->writeFieldBegin('businessAddress', \TType::STRING, 30);
      $xfer += $output->writeString($this->businessAddress);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->hideSponsorBilling !== null) {
      $xfer += $output->writeFieldBegin('hideSponsorBilling', \TType::BOOL, 31);
      $xfer += $output->writeBool($this->hideSponsorBilling);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->taxExempt !== null) {
      $xfer += $output->writeFieldBegin('taxExempt', \TType::BOOL, 32);
      $xfer += $output->writeBool($this->taxExempt);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->useEmailAutoFiling !== null) {
      $xfer += $output->writeFieldBegin('useEmailAutoFiling', \TType::BOOL, 33);
      $xfer += $output->writeBool($this->useEmailAutoFiling);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->reminderEmailConfig !== null) {
      $xfer += $output->writeFieldBegin('reminderEmailConfig', \TType::I32, 34);
      $xfer += $output->writeI32($this->reminderEmailConfig);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class Accounting {
  static $_TSPEC;

  public $uploadLimit = null;
  public $uploadLimitEnd = null;
  public $uploadLimitNextMonth = null;
  public $premiumServiceStatus = null;
  public $premiumOrderNumber = null;
  public $premiumCommerceService = null;
  public $premiumServiceStart = null;
  public $premiumServiceSKU = null;
  public $lastSuccessfulCharge = null;
  public $lastFailedCharge = null;
  public $lastFailedChargeReason = null;
  public $nextPaymentDue = null;
  public $premiumLockUntil = null;
  public $updated = null;
  public $premiumSubscriptionNumber = null;
  public $lastRequestedCharge = null;
  public $currency = null;
  public $unitPrice = null;
  public $businessId = null;
  public $businessName = null;
  public $businessRole = null;
  public $unitDiscount = null;
  public $nextChargeDate = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'uploadLimit',
          'type' => \TType::I64,
          ),
        2 => array(
          'var' => 'uploadLimitEnd',
          'type' => \TType::I64,
          ),
        3 => array(
          'var' => 'uploadLimitNextMonth',
          'type' => \TType::I64,
          ),
        4 => array(
          'var' => 'premiumServiceStatus',
          'type' => \TType::I32,
          ),
        5 => array(
          'var' => 'premiumOrderNumber',
          'type' => \TType::STRING,
          ),
        6 => array(
          'var' => 'premiumCommerceService',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'premiumServiceStart',
          'type' => \TType::I64,
          ),
        8 => array(
          'var' => 'premiumServiceSKU',
          'type' => \TType::STRING,
          ),
        9 => array(
          'var' => 'lastSuccessfulCharge',
          'type' => \TType::I64,
          ),
        10 => array(
          'var' => 'lastFailedCharge',
          'type' => \TType::I64,
          ),
        11 => array(
          'var' => 'lastFailedChargeReason',
          'type' => \TType::STRING,
          ),
        12 => array(
          'var' => 'nextPaymentDue',
          'type' => \TType::I64,
          ),
        13 => array(
          'var' => 'premiumLockUntil',
          'type' => \TType::I64,
          ),
        14 => array(
          'var' => 'updated',
          'type' => \TType::I64,
          ),
        16 => array(
          'var' => 'premiumSubscriptionNumber',
          'type' => \TType::STRING,
          ),
        17 => array(
          'var' => 'lastRequestedCharge',
          'type' => \TType::I64,
          ),
        18 => array(
          'var' => 'currency',
          'type' => \TType::STRING,
          ),
        19 => array(
          'var' => 'unitPrice',
          'type' => \TType::I32,
          ),
        20 => array(
          'var' => 'businessId',
          'type' => \TType::I32,
          ),
        21 => array(
          'var' => 'businessName',
          'type' => \TType::STRING,
          ),
        22 => array(
          'var' => 'businessRole',
          'type' => \TType::I32,
          ),
        23 => array(
          'var' => 'unitDiscount',
          'type' => \TType::I32,
          ),
        24 => array(
          'var' => 'nextChargeDate',
          'type' => \TType::I64,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['uploadLimit'])) {
        $this->uploadLimit = $vals['uploadLimit'];
      }
      if (isset($vals['uploadLimitEnd'])) {
        $this->uploadLimitEnd = $vals['uploadLimitEnd'];
      }
      if (isset($vals['uploadLimitNextMonth'])) {
        $this->uploadLimitNextMonth = $vals['uploadLimitNextMonth'];
      }
      if (isset($vals['premiumServiceStatus'])) {
        $this->premiumServiceStatus = $vals['premiumServiceStatus'];
      }
      if (isset($vals['premiumOrderNumber'])) {
        $this->premiumOrderNumber = $vals['premiumOrderNumber'];
      }
      if (isset($vals['premiumCommerceService'])) {
        $this->premiumCommerceService = $vals['premiumCommerceService'];
      }
      if (isset($vals['premiumServiceStart'])) {
        $this->premiumServiceStart = $vals['premiumServiceStart'];
      }
      if (isset($vals['premiumServiceSKU'])) {
        $this->premiumServiceSKU = $vals['premiumServiceSKU'];
      }
      if (isset($vals['lastSuccessfulCharge'])) {
        $this->lastSuccessfulCharge = $vals['lastSuccessfulCharge'];
      }
      if (isset($vals['lastFailedCharge'])) {
        $this->lastFailedCharge = $vals['lastFailedCharge'];
      }
      if (isset($vals['lastFailedChargeReason'])) {
        $this->lastFailedChargeReason = $vals['lastFailedChargeReason'];
      }
      if (isset($vals['nextPaymentDue'])) {
        $this->nextPaymentDue = $vals['nextPaymentDue'];
      }
      if (isset($vals['premiumLockUntil'])) {
        $this->premiumLockUntil = $vals['premiumLockUntil'];
      }
      if (isset($vals['updated'])) {
        $this->updated = $vals['updated'];
      }
      if (isset($vals['premiumSubscriptionNumber'])) {
        $this->premiumSubscriptionNumber = $vals['premiumSubscriptionNumber'];
      }
      if (isset($vals['lastRequestedCharge'])) {
        $this->lastRequestedCharge = $vals['lastRequestedCharge'];
      }
      if (isset($vals['currency'])) {
        $this->currency = $vals['currency'];
      }
      if (isset($vals['unitPrice'])) {
        $this->unitPrice = $vals['unitPrice'];
      }
      if (isset($vals['businessId'])) {
        $this->businessId = $vals['businessId'];
      }
      if (isset($vals['businessName'])) {
        $this->businessName = $vals['businessName'];
      }
      if (isset($vals['businessRole'])) {
        $this->businessRole = $vals['businessRole'];
      }
      if (isset($vals['unitDiscount'])) {
        $this->unitDiscount = $vals['unitDiscount'];
      }
      if (isset($vals['nextChargeDate'])) {
        $this->nextChargeDate = $vals['nextChargeDate'];
      }
    }
  }

  public function getName() {
    return 'Accounting';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->uploadLimit);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->uploadLimitEnd);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->uploadLimitNextMonth);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->premiumServiceStatus);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->premiumOrderNumber);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->premiumCommerceService);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->premiumServiceStart);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->premiumServiceSKU);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->lastSuccessfulCharge);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->lastFailedCharge);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->lastFailedChargeReason);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->nextPaymentDue);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->premiumLockUntil);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->updated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 16:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->premiumSubscriptionNumber);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 17:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->lastRequestedCharge);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 18:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->currency);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 19:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->unitPrice);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 20:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->businessId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 21:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->businessName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 22:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->businessRole);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 23:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->unitDiscount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 24:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->nextChargeDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Accounting');
    if ($this->uploadLimit !== null) {
      $xfer += $output->writeFieldBegin('uploadLimit', \TType::I64, 1);
      $xfer += $output->writeI64($this->uploadLimit);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->uploadLimitEnd !== null) {
      $xfer += $output->writeFieldBegin('uploadLimitEnd', \TType::I64, 2);
      $xfer += $output->writeI64($this->uploadLimitEnd);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->uploadLimitNextMonth !== null) {
      $xfer += $output->writeFieldBegin('uploadLimitNextMonth', \TType::I64, 3);
      $xfer += $output->writeI64($this->uploadLimitNextMonth);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumServiceStatus !== null) {
      $xfer += $output->writeFieldBegin('premiumServiceStatus', \TType::I32, 4);
      $xfer += $output->writeI32($this->premiumServiceStatus);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumOrderNumber !== null) {
      $xfer += $output->writeFieldBegin('premiumOrderNumber', \TType::STRING, 5);
      $xfer += $output->writeString($this->premiumOrderNumber);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumCommerceService !== null) {
      $xfer += $output->writeFieldBegin('premiumCommerceService', \TType::STRING, 6);
      $xfer += $output->writeString($this->premiumCommerceService);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumServiceStart !== null) {
      $xfer += $output->writeFieldBegin('premiumServiceStart', \TType::I64, 7);
      $xfer += $output->writeI64($this->premiumServiceStart);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumServiceSKU !== null) {
      $xfer += $output->writeFieldBegin('premiumServiceSKU', \TType::STRING, 8);
      $xfer += $output->writeString($this->premiumServiceSKU);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->lastSuccessfulCharge !== null) {
      $xfer += $output->writeFieldBegin('lastSuccessfulCharge', \TType::I64, 9);
      $xfer += $output->writeI64($this->lastSuccessfulCharge);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->lastFailedCharge !== null) {
      $xfer += $output->writeFieldBegin('lastFailedCharge', \TType::I64, 10);
      $xfer += $output->writeI64($this->lastFailedCharge);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->lastFailedChargeReason !== null) {
      $xfer += $output->writeFieldBegin('lastFailedChargeReason', \TType::STRING, 11);
      $xfer += $output->writeString($this->lastFailedChargeReason);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->nextPaymentDue !== null) {
      $xfer += $output->writeFieldBegin('nextPaymentDue', \TType::I64, 12);
      $xfer += $output->writeI64($this->nextPaymentDue);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumLockUntil !== null) {
      $xfer += $output->writeFieldBegin('premiumLockUntil', \TType::I64, 13);
      $xfer += $output->writeI64($this->premiumLockUntil);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updated !== null) {
      $xfer += $output->writeFieldBegin('updated', \TType::I64, 14);
      $xfer += $output->writeI64($this->updated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumSubscriptionNumber !== null) {
      $xfer += $output->writeFieldBegin('premiumSubscriptionNumber', \TType::STRING, 16);
      $xfer += $output->writeString($this->premiumSubscriptionNumber);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->lastRequestedCharge !== null) {
      $xfer += $output->writeFieldBegin('lastRequestedCharge', \TType::I64, 17);
      $xfer += $output->writeI64($this->lastRequestedCharge);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->currency !== null) {
      $xfer += $output->writeFieldBegin('currency', \TType::STRING, 18);
      $xfer += $output->writeString($this->currency);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->unitPrice !== null) {
      $xfer += $output->writeFieldBegin('unitPrice', \TType::I32, 19);
      $xfer += $output->writeI32($this->unitPrice);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessId !== null) {
      $xfer += $output->writeFieldBegin('businessId', \TType::I32, 20);
      $xfer += $output->writeI32($this->businessId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessName !== null) {
      $xfer += $output->writeFieldBegin('businessName', \TType::STRING, 21);
      $xfer += $output->writeString($this->businessName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessRole !== null) {
      $xfer += $output->writeFieldBegin('businessRole', \TType::I32, 22);
      $xfer += $output->writeI32($this->businessRole);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->unitDiscount !== null) {
      $xfer += $output->writeFieldBegin('unitDiscount', \TType::I32, 23);
      $xfer += $output->writeI32($this->unitDiscount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->nextChargeDate !== null) {
      $xfer += $output->writeFieldBegin('nextChargeDate', \TType::I64, 24);
      $xfer += $output->writeI64($this->nextChargeDate);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class BusinessUserInfo {
  static $_TSPEC;

  public $businessId = null;
  public $businessName = null;
  public $role = null;
  public $email = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'businessId',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'businessName',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'role',
          'type' => \TType::I32,
          ),
        4 => array(
          'var' => 'email',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['businessId'])) {
        $this->businessId = $vals['businessId'];
      }
      if (isset($vals['businessName'])) {
        $this->businessName = $vals['businessName'];
      }
      if (isset($vals['role'])) {
        $this->role = $vals['role'];
      }
      if (isset($vals['email'])) {
        $this->email = $vals['email'];
      }
    }
  }

  public function getName() {
    return 'BusinessUserInfo';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->businessId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->businessName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->role);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->email);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('BusinessUserInfo');
    if ($this->businessId !== null) {
      $xfer += $output->writeFieldBegin('businessId', \TType::I32, 1);
      $xfer += $output->writeI32($this->businessId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessName !== null) {
      $xfer += $output->writeFieldBegin('businessName', \TType::STRING, 2);
      $xfer += $output->writeString($this->businessName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->role !== null) {
      $xfer += $output->writeFieldBegin('role', \TType::I32, 3);
      $xfer += $output->writeI32($this->role);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->email !== null) {
      $xfer += $output->writeFieldBegin('email', \TType::STRING, 4);
      $xfer += $output->writeString($this->email);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class PremiumInfo {
  static $_TSPEC;

  public $currentTime = null;
  public $premium = null;
  public $premiumRecurring = null;
  public $premiumExpirationDate = null;
  public $premiumExtendable = null;
  public $premiumPending = null;
  public $premiumCancellationPending = null;
  public $canPurchaseUploadAllowance = null;
  public $sponsoredGroupName = null;
  public $sponsoredGroupRole = null;
  public $premiumUpgradable = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'currentTime',
          'type' => \TType::I64,
          ),
        2 => array(
          'var' => 'premium',
          'type' => \TType::BOOL,
          ),
        3 => array(
          'var' => 'premiumRecurring',
          'type' => \TType::BOOL,
          ),
        4 => array(
          'var' => 'premiumExpirationDate',
          'type' => \TType::I64,
          ),
        5 => array(
          'var' => 'premiumExtendable',
          'type' => \TType::BOOL,
          ),
        6 => array(
          'var' => 'premiumPending',
          'type' => \TType::BOOL,
          ),
        7 => array(
          'var' => 'premiumCancellationPending',
          'type' => \TType::BOOL,
          ),
        8 => array(
          'var' => 'canPurchaseUploadAllowance',
          'type' => \TType::BOOL,
          ),
        9 => array(
          'var' => 'sponsoredGroupName',
          'type' => \TType::STRING,
          ),
        10 => array(
          'var' => 'sponsoredGroupRole',
          'type' => \TType::I32,
          ),
        11 => array(
          'var' => 'premiumUpgradable',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['currentTime'])) {
        $this->currentTime = $vals['currentTime'];
      }
      if (isset($vals['premium'])) {
        $this->premium = $vals['premium'];
      }
      if (isset($vals['premiumRecurring'])) {
        $this->premiumRecurring = $vals['premiumRecurring'];
      }
      if (isset($vals['premiumExpirationDate'])) {
        $this->premiumExpirationDate = $vals['premiumExpirationDate'];
      }
      if (isset($vals['premiumExtendable'])) {
        $this->premiumExtendable = $vals['premiumExtendable'];
      }
      if (isset($vals['premiumPending'])) {
        $this->premiumPending = $vals['premiumPending'];
      }
      if (isset($vals['premiumCancellationPending'])) {
        $this->premiumCancellationPending = $vals['premiumCancellationPending'];
      }
      if (isset($vals['canPurchaseUploadAllowance'])) {
        $this->canPurchaseUploadAllowance = $vals['canPurchaseUploadAllowance'];
      }
      if (isset($vals['sponsoredGroupName'])) {
        $this->sponsoredGroupName = $vals['sponsoredGroupName'];
      }
      if (isset($vals['sponsoredGroupRole'])) {
        $this->sponsoredGroupRole = $vals['sponsoredGroupRole'];
      }
      if (isset($vals['premiumUpgradable'])) {
        $this->premiumUpgradable = $vals['premiumUpgradable'];
      }
    }
  }

  public function getName() {
    return 'PremiumInfo';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->currentTime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->premium);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->premiumRecurring);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->premiumExpirationDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->premiumExtendable);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->premiumPending);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->premiumCancellationPending);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->canPurchaseUploadAllowance);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->sponsoredGroupName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->sponsoredGroupRole);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->premiumUpgradable);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('PremiumInfo');
    if ($this->currentTime !== null) {
      $xfer += $output->writeFieldBegin('currentTime', \TType::I64, 1);
      $xfer += $output->writeI64($this->currentTime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premium !== null) {
      $xfer += $output->writeFieldBegin('premium', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->premium);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumRecurring !== null) {
      $xfer += $output->writeFieldBegin('premiumRecurring', \TType::BOOL, 3);
      $xfer += $output->writeBool($this->premiumRecurring);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumExpirationDate !== null) {
      $xfer += $output->writeFieldBegin('premiumExpirationDate', \TType::I64, 4);
      $xfer += $output->writeI64($this->premiumExpirationDate);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumExtendable !== null) {
      $xfer += $output->writeFieldBegin('premiumExtendable', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->premiumExtendable);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumPending !== null) {
      $xfer += $output->writeFieldBegin('premiumPending', \TType::BOOL, 6);
      $xfer += $output->writeBool($this->premiumPending);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumCancellationPending !== null) {
      $xfer += $output->writeFieldBegin('premiumCancellationPending', \TType::BOOL, 7);
      $xfer += $output->writeBool($this->premiumCancellationPending);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->canPurchaseUploadAllowance !== null) {
      $xfer += $output->writeFieldBegin('canPurchaseUploadAllowance', \TType::BOOL, 8);
      $xfer += $output->writeBool($this->canPurchaseUploadAllowance);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sponsoredGroupName !== null) {
      $xfer += $output->writeFieldBegin('sponsoredGroupName', \TType::STRING, 9);
      $xfer += $output->writeString($this->sponsoredGroupName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sponsoredGroupRole !== null) {
      $xfer += $output->writeFieldBegin('sponsoredGroupRole', \TType::I32, 10);
      $xfer += $output->writeI32($this->sponsoredGroupRole);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumUpgradable !== null) {
      $xfer += $output->writeFieldBegin('premiumUpgradable', \TType::BOOL, 11);
      $xfer += $output->writeBool($this->premiumUpgradable);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class User {
  static $_TSPEC;

  public $id = null;
  public $username = null;
  public $email = null;
  public $name = null;
  public $timezone = null;
  public $privilege = null;
  public $created = null;
  public $updated = null;
  public $deleted = null;
  public $active = null;
  public $shardId = null;
  public $attributes = null;
  public $accounting = null;
  public $premiumInfo = null;
  public $businessUserInfo = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'id',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'username',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'email',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'name',
          'type' => \TType::STRING,
          ),
        6 => array(
          'var' => 'timezone',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'privilege',
          'type' => \TType::I32,
          ),
        9 => array(
          'var' => 'created',
          'type' => \TType::I64,
          ),
        10 => array(
          'var' => 'updated',
          'type' => \TType::I64,
          ),
        11 => array(
          'var' => 'deleted',
          'type' => \TType::I64,
          ),
        13 => array(
          'var' => 'active',
          'type' => \TType::BOOL,
          ),
        14 => array(
          'var' => 'shardId',
          'type' => \TType::STRING,
          ),
        15 => array(
          'var' => 'attributes',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\UserAttributes',
          ),
        16 => array(
          'var' => 'accounting',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\Accounting',
          ),
        17 => array(
          'var' => 'premiumInfo',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\PremiumInfo',
          ),
        18 => array(
          'var' => 'businessUserInfo',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\BusinessUserInfo',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['id'])) {
        $this->id = $vals['id'];
      }
      if (isset($vals['username'])) {
        $this->username = $vals['username'];
      }
      if (isset($vals['email'])) {
        $this->email = $vals['email'];
      }
      if (isset($vals['name'])) {
        $this->name = $vals['name'];
      }
      if (isset($vals['timezone'])) {
        $this->timezone = $vals['timezone'];
      }
      if (isset($vals['privilege'])) {
        $this->privilege = $vals['privilege'];
      }
      if (isset($vals['created'])) {
        $this->created = $vals['created'];
      }
      if (isset($vals['updated'])) {
        $this->updated = $vals['updated'];
      }
      if (isset($vals['deleted'])) {
        $this->deleted = $vals['deleted'];
      }
      if (isset($vals['active'])) {
        $this->active = $vals['active'];
      }
      if (isset($vals['shardId'])) {
        $this->shardId = $vals['shardId'];
      }
      if (isset($vals['attributes'])) {
        $this->attributes = $vals['attributes'];
      }
      if (isset($vals['accounting'])) {
        $this->accounting = $vals['accounting'];
      }
      if (isset($vals['premiumInfo'])) {
        $this->premiumInfo = $vals['premiumInfo'];
      }
      if (isset($vals['businessUserInfo'])) {
        $this->businessUserInfo = $vals['businessUserInfo'];
      }
    }
  }

  public function getName() {
    return 'User';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->id);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->username);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->email);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->name);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->timezone);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->privilege);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->created);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->updated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->deleted);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->active);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->shardId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 15:
          if ($ftype == \TType::STRUCT) {
            $this->attributes = new \EDAM\Types\UserAttributes();
            $xfer += $this->attributes->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 16:
          if ($ftype == \TType::STRUCT) {
            $this->accounting = new \EDAM\Types\Accounting();
            $xfer += $this->accounting->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 17:
          if ($ftype == \TType::STRUCT) {
            $this->premiumInfo = new \EDAM\Types\PremiumInfo();
            $xfer += $this->premiumInfo->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 18:
          if ($ftype == \TType::STRUCT) {
            $this->businessUserInfo = new \EDAM\Types\BusinessUserInfo();
            $xfer += $this->businessUserInfo->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('User');
    if ($this->id !== null) {
      $xfer += $output->writeFieldBegin('id', \TType::I32, 1);
      $xfer += $output->writeI32($this->id);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->username !== null) {
      $xfer += $output->writeFieldBegin('username', \TType::STRING, 2);
      $xfer += $output->writeString($this->username);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->email !== null) {
      $xfer += $output->writeFieldBegin('email', \TType::STRING, 3);
      $xfer += $output->writeString($this->email);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->name !== null) {
      $xfer += $output->writeFieldBegin('name', \TType::STRING, 4);
      $xfer += $output->writeString($this->name);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->timezone !== null) {
      $xfer += $output->writeFieldBegin('timezone', \TType::STRING, 6);
      $xfer += $output->writeString($this->timezone);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->privilege !== null) {
      $xfer += $output->writeFieldBegin('privilege', \TType::I32, 7);
      $xfer += $output->writeI32($this->privilege);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->created !== null) {
      $xfer += $output->writeFieldBegin('created', \TType::I64, 9);
      $xfer += $output->writeI64($this->created);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updated !== null) {
      $xfer += $output->writeFieldBegin('updated', \TType::I64, 10);
      $xfer += $output->writeI64($this->updated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deleted !== null) {
      $xfer += $output->writeFieldBegin('deleted', \TType::I64, 11);
      $xfer += $output->writeI64($this->deleted);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->active !== null) {
      $xfer += $output->writeFieldBegin('active', \TType::BOOL, 13);
      $xfer += $output->writeBool($this->active);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->shardId !== null) {
      $xfer += $output->writeFieldBegin('shardId', \TType::STRING, 14);
      $xfer += $output->writeString($this->shardId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->attributes !== null) {
      if (!is_object($this->attributes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('attributes', \TType::STRUCT, 15);
      $xfer += $this->attributes->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->accounting !== null) {
      if (!is_object($this->accounting)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('accounting', \TType::STRUCT, 16);
      $xfer += $this->accounting->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->premiumInfo !== null) {
      if (!is_object($this->premiumInfo)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('premiumInfo', \TType::STRUCT, 17);
      $xfer += $this->premiumInfo->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessUserInfo !== null) {
      if (!is_object($this->businessUserInfo)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('businessUserInfo', \TType::STRUCT, 18);
      $xfer += $this->businessUserInfo->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class Tag {
  static $_TSPEC;

  public $guid = null;
  public $name = null;
  public $parentGuid = null;
  public $updateSequenceNum = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'name',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'parentGuid',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['name'])) {
        $this->name = $vals['name'];
      }
      if (isset($vals['parentGuid'])) {
        $this->parentGuid = $vals['parentGuid'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
    }
  }

  public function getName() {
    return 'Tag';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->name);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->parentGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Tag');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->name !== null) {
      $xfer += $output->writeFieldBegin('name', \TType::STRING, 2);
      $xfer += $output->writeString($this->name);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->parentGuid !== null) {
      $xfer += $output->writeFieldBegin('parentGuid', \TType::STRING, 3);
      $xfer += $output->writeString($this->parentGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 4);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class LazyMap {
  static $_TSPEC;

  public $keysOnly = null;
  public $fullMap = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'keysOnly',
          'type' => \TType::SET,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        2 => array(
          'var' => 'fullMap',
          'type' => \TType::MAP,
          'ktype' => \TType::STRING,
          'vtype' => \TType::STRING,
          'key' => array(
            'type' => \TType::STRING,
          ),
          'val' => array(
            'type' => \TType::STRING,
            ),
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['keysOnly'])) {
        $this->keysOnly = $vals['keysOnly'];
      }
      if (isset($vals['fullMap'])) {
        $this->fullMap = $vals['fullMap'];
      }
    }
  }

  public function getName() {
    return 'LazyMap';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::SET) {
            $this->keysOnly = array();
            $_size14 = 0;
            $_etype17 = 0;
            $xfer += $input->readSetBegin($_etype17, $_size14);
            for ($_i18 = 0; $_i18 < $_size14; ++$_i18)
            {
              $elem19 = null;
              $xfer += $input->readString($elem19);
              if (is_scalar($elem19)) {
                $this->keysOnly[$elem19] = true;
              } else {
                $this->keysOnly []= $elem19;
              }
            }
            $xfer += $input->readSetEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::MAP) {
            $this->fullMap = array();
            $_size20 = 0;
            $_ktype21 = 0;
            $_vtype22 = 0;
            $xfer += $input->readMapBegin($_ktype21, $_vtype22, $_size20);
            for ($_i24 = 0; $_i24 < $_size20; ++$_i24)
            {
              $key25 = '';
              $val26 = '';
              $xfer += $input->readString($key25);
              $xfer += $input->readString($val26);
              $this->fullMap[$key25] = $val26;
            }
            $xfer += $input->readMapEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('LazyMap');
    if ($this->keysOnly !== null) {
      if (!is_array($this->keysOnly)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('keysOnly', \TType::SET, 1);
      {
        $output->writeSetBegin(\TType::STRING, count($this->keysOnly));
        {
          foreach ($this->keysOnly as $iter27 => $iter28)
          {
            if (is_scalar($iter28)) {
            $xfer += $output->writeString($iter27);
            } else {
            $xfer += $output->writeString($iter28);
            }
          }
        }
        $output->writeSetEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->fullMap !== null) {
      if (!is_array($this->fullMap)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('fullMap', \TType::MAP, 2);
      {
        $output->writeMapBegin(\TType::STRING, \TType::STRING, count($this->fullMap));
        {
          foreach ($this->fullMap as $kiter29 => $viter30)
          {
            $xfer += $output->writeString($kiter29);
            $xfer += $output->writeString($viter30);
          }
        }
        $output->writeMapEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class ResourceAttributes {
  static $_TSPEC;

  public $sourceURL = null;
  public $timestamp = null;
  public $latitude = null;
  public $longitude = null;
  public $altitude = null;
  public $cameraMake = null;
  public $cameraModel = null;
  public $clientWillIndex = null;
  public $recoType = null;
  public $fileName = null;
  public $attachment = null;
  public $applicationData = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'sourceURL',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'timestamp',
          'type' => \TType::I64,
          ),
        3 => array(
          'var' => 'latitude',
          'type' => \TType::DOUBLE,
          ),
        4 => array(
          'var' => 'longitude',
          'type' => \TType::DOUBLE,
          ),
        5 => array(
          'var' => 'altitude',
          'type' => \TType::DOUBLE,
          ),
        6 => array(
          'var' => 'cameraMake',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'cameraModel',
          'type' => \TType::STRING,
          ),
        8 => array(
          'var' => 'clientWillIndex',
          'type' => \TType::BOOL,
          ),
        9 => array(
          'var' => 'recoType',
          'type' => \TType::STRING,
          ),
        10 => array(
          'var' => 'fileName',
          'type' => \TType::STRING,
          ),
        11 => array(
          'var' => 'attachment',
          'type' => \TType::BOOL,
          ),
        12 => array(
          'var' => 'applicationData',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\LazyMap',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['sourceURL'])) {
        $this->sourceURL = $vals['sourceURL'];
      }
      if (isset($vals['timestamp'])) {
        $this->timestamp = $vals['timestamp'];
      }
      if (isset($vals['latitude'])) {
        $this->latitude = $vals['latitude'];
      }
      if (isset($vals['longitude'])) {
        $this->longitude = $vals['longitude'];
      }
      if (isset($vals['altitude'])) {
        $this->altitude = $vals['altitude'];
      }
      if (isset($vals['cameraMake'])) {
        $this->cameraMake = $vals['cameraMake'];
      }
      if (isset($vals['cameraModel'])) {
        $this->cameraModel = $vals['cameraModel'];
      }
      if (isset($vals['clientWillIndex'])) {
        $this->clientWillIndex = $vals['clientWillIndex'];
      }
      if (isset($vals['recoType'])) {
        $this->recoType = $vals['recoType'];
      }
      if (isset($vals['fileName'])) {
        $this->fileName = $vals['fileName'];
      }
      if (isset($vals['attachment'])) {
        $this->attachment = $vals['attachment'];
      }
      if (isset($vals['applicationData'])) {
        $this->applicationData = $vals['applicationData'];
      }
    }
  }

  public function getName() {
    return 'ResourceAttributes';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->sourceURL);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->timestamp);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->latitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->longitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->altitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->cameraMake);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->cameraModel);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->clientWillIndex);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->recoType);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->fileName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->attachment);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::STRUCT) {
            $this->applicationData = new \EDAM\Types\LazyMap();
            $xfer += $this->applicationData->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('ResourceAttributes');
    if ($this->sourceURL !== null) {
      $xfer += $output->writeFieldBegin('sourceURL', \TType::STRING, 1);
      $xfer += $output->writeString($this->sourceURL);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->timestamp !== null) {
      $xfer += $output->writeFieldBegin('timestamp', \TType::I64, 2);
      $xfer += $output->writeI64($this->timestamp);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->latitude !== null) {
      $xfer += $output->writeFieldBegin('latitude', \TType::DOUBLE, 3);
      $xfer += $output->writeDouble($this->latitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->longitude !== null) {
      $xfer += $output->writeFieldBegin('longitude', \TType::DOUBLE, 4);
      $xfer += $output->writeDouble($this->longitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->altitude !== null) {
      $xfer += $output->writeFieldBegin('altitude', \TType::DOUBLE, 5);
      $xfer += $output->writeDouble($this->altitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->cameraMake !== null) {
      $xfer += $output->writeFieldBegin('cameraMake', \TType::STRING, 6);
      $xfer += $output->writeString($this->cameraMake);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->cameraModel !== null) {
      $xfer += $output->writeFieldBegin('cameraModel', \TType::STRING, 7);
      $xfer += $output->writeString($this->cameraModel);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->clientWillIndex !== null) {
      $xfer += $output->writeFieldBegin('clientWillIndex', \TType::BOOL, 8);
      $xfer += $output->writeBool($this->clientWillIndex);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->recoType !== null) {
      $xfer += $output->writeFieldBegin('recoType', \TType::STRING, 9);
      $xfer += $output->writeString($this->recoType);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->fileName !== null) {
      $xfer += $output->writeFieldBegin('fileName', \TType::STRING, 10);
      $xfer += $output->writeString($this->fileName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->attachment !== null) {
      $xfer += $output->writeFieldBegin('attachment', \TType::BOOL, 11);
      $xfer += $output->writeBool($this->attachment);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->applicationData !== null) {
      if (!is_object($this->applicationData)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('applicationData', \TType::STRUCT, 12);
      $xfer += $this->applicationData->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class Resource {
  static $_TSPEC;

  public $guid = null;
  public $noteGuid = null;
  public $data = null;
  public $mime = null;
  public $width = null;
  public $height = null;
  public $duration = null;
  public $active = null;
  public $recognition = null;
  public $attributes = null;
  public $updateSequenceNum = null;
  public $alternateData = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'noteGuid',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'data',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\Data',
          ),
        4 => array(
          'var' => 'mime',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'width',
          'type' => \TType::I16,
          ),
        6 => array(
          'var' => 'height',
          'type' => \TType::I16,
          ),
        7 => array(
          'var' => 'duration',
          'type' => \TType::I16,
          ),
        8 => array(
          'var' => 'active',
          'type' => \TType::BOOL,
          ),
        9 => array(
          'var' => 'recognition',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\Data',
          ),
        11 => array(
          'var' => 'attributes',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\ResourceAttributes',
          ),
        12 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        13 => array(
          'var' => 'alternateData',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\Data',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['noteGuid'])) {
        $this->noteGuid = $vals['noteGuid'];
      }
      if (isset($vals['data'])) {
        $this->data = $vals['data'];
      }
      if (isset($vals['mime'])) {
        $this->mime = $vals['mime'];
      }
      if (isset($vals['width'])) {
        $this->width = $vals['width'];
      }
      if (isset($vals['height'])) {
        $this->height = $vals['height'];
      }
      if (isset($vals['duration'])) {
        $this->duration = $vals['duration'];
      }
      if (isset($vals['active'])) {
        $this->active = $vals['active'];
      }
      if (isset($vals['recognition'])) {
        $this->recognition = $vals['recognition'];
      }
      if (isset($vals['attributes'])) {
        $this->attributes = $vals['attributes'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['alternateData'])) {
        $this->alternateData = $vals['alternateData'];
      }
    }
  }

  public function getName() {
    return 'Resource';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->noteGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRUCT) {
            $this->data = new \EDAM\Types\Data();
            $xfer += $this->data->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->mime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::I16) {
            $xfer += $input->readI16($this->width);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::I16) {
            $xfer += $input->readI16($this->height);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I16) {
            $xfer += $input->readI16($this->duration);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->active);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::STRUCT) {
            $this->recognition = new \EDAM\Types\Data();
            $xfer += $this->recognition->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::STRUCT) {
            $this->attributes = new \EDAM\Types\ResourceAttributes();
            $xfer += $this->attributes->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::STRUCT) {
            $this->alternateData = new \EDAM\Types\Data();
            $xfer += $this->alternateData->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Resource');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noteGuid !== null) {
      $xfer += $output->writeFieldBegin('noteGuid', \TType::STRING, 2);
      $xfer += $output->writeString($this->noteGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->data !== null) {
      if (!is_object($this->data)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('data', \TType::STRUCT, 3);
      $xfer += $this->data->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->mime !== null) {
      $xfer += $output->writeFieldBegin('mime', \TType::STRING, 4);
      $xfer += $output->writeString($this->mime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->width !== null) {
      $xfer += $output->writeFieldBegin('width', \TType::I16, 5);
      $xfer += $output->writeI16($this->width);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->height !== null) {
      $xfer += $output->writeFieldBegin('height', \TType::I16, 6);
      $xfer += $output->writeI16($this->height);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->duration !== null) {
      $xfer += $output->writeFieldBegin('duration', \TType::I16, 7);
      $xfer += $output->writeI16($this->duration);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->active !== null) {
      $xfer += $output->writeFieldBegin('active', \TType::BOOL, 8);
      $xfer += $output->writeBool($this->active);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->recognition !== null) {
      if (!is_object($this->recognition)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('recognition', \TType::STRUCT, 9);
      $xfer += $this->recognition->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->attributes !== null) {
      if (!is_object($this->attributes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('attributes', \TType::STRUCT, 11);
      $xfer += $this->attributes->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 12);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->alternateData !== null) {
      if (!is_object($this->alternateData)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('alternateData', \TType::STRUCT, 13);
      $xfer += $this->alternateData->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteAttributes {
  static $_TSPEC;

  public $subjectDate = null;
  public $latitude = null;
  public $longitude = null;
  public $altitude = null;
  public $author = null;
  public $source = null;
  public $sourceURL = null;
  public $sourceApplication = null;
  public $shareDate = null;
  public $reminderOrder = null;
  public $reminderDoneTime = null;
  public $reminderTime = null;
  public $placeName = null;
  public $contentClass = null;
  public $applicationData = null;
  public $lastEditedBy = null;
  public $classifications = null;
  public $creatorId = null;
  public $lastEditorId = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'subjectDate',
          'type' => \TType::I64,
          ),
        10 => array(
          'var' => 'latitude',
          'type' => \TType::DOUBLE,
          ),
        11 => array(
          'var' => 'longitude',
          'type' => \TType::DOUBLE,
          ),
        12 => array(
          'var' => 'altitude',
          'type' => \TType::DOUBLE,
          ),
        13 => array(
          'var' => 'author',
          'type' => \TType::STRING,
          ),
        14 => array(
          'var' => 'source',
          'type' => \TType::STRING,
          ),
        15 => array(
          'var' => 'sourceURL',
          'type' => \TType::STRING,
          ),
        16 => array(
          'var' => 'sourceApplication',
          'type' => \TType::STRING,
          ),
        17 => array(
          'var' => 'shareDate',
          'type' => \TType::I64,
          ),
        18 => array(
          'var' => 'reminderOrder',
          'type' => \TType::I64,
          ),
        19 => array(
          'var' => 'reminderDoneTime',
          'type' => \TType::I64,
          ),
        20 => array(
          'var' => 'reminderTime',
          'type' => \TType::I64,
          ),
        21 => array(
          'var' => 'placeName',
          'type' => \TType::STRING,
          ),
        22 => array(
          'var' => 'contentClass',
          'type' => \TType::STRING,
          ),
        23 => array(
          'var' => 'applicationData',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\LazyMap',
          ),
        24 => array(
          'var' => 'lastEditedBy',
          'type' => \TType::STRING,
          ),
        26 => array(
          'var' => 'classifications',
          'type' => \TType::MAP,
          'ktype' => \TType::STRING,
          'vtype' => \TType::STRING,
          'key' => array(
            'type' => \TType::STRING,
          ),
          'val' => array(
            'type' => \TType::STRING,
            ),
          ),
        27 => array(
          'var' => 'creatorId',
          'type' => \TType::I32,
          ),
        28 => array(
          'var' => 'lastEditorId',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['subjectDate'])) {
        $this->subjectDate = $vals['subjectDate'];
      }
      if (isset($vals['latitude'])) {
        $this->latitude = $vals['latitude'];
      }
      if (isset($vals['longitude'])) {
        $this->longitude = $vals['longitude'];
      }
      if (isset($vals['altitude'])) {
        $this->altitude = $vals['altitude'];
      }
      if (isset($vals['author'])) {
        $this->author = $vals['author'];
      }
      if (isset($vals['source'])) {
        $this->source = $vals['source'];
      }
      if (isset($vals['sourceURL'])) {
        $this->sourceURL = $vals['sourceURL'];
      }
      if (isset($vals['sourceApplication'])) {
        $this->sourceApplication = $vals['sourceApplication'];
      }
      if (isset($vals['shareDate'])) {
        $this->shareDate = $vals['shareDate'];
      }
      if (isset($vals['reminderOrder'])) {
        $this->reminderOrder = $vals['reminderOrder'];
      }
      if (isset($vals['reminderDoneTime'])) {
        $this->reminderDoneTime = $vals['reminderDoneTime'];
      }
      if (isset($vals['reminderTime'])) {
        $this->reminderTime = $vals['reminderTime'];
      }
      if (isset($vals['placeName'])) {
        $this->placeName = $vals['placeName'];
      }
      if (isset($vals['contentClass'])) {
        $this->contentClass = $vals['contentClass'];
      }
      if (isset($vals['applicationData'])) {
        $this->applicationData = $vals['applicationData'];
      }
      if (isset($vals['lastEditedBy'])) {
        $this->lastEditedBy = $vals['lastEditedBy'];
      }
      if (isset($vals['classifications'])) {
        $this->classifications = $vals['classifications'];
      }
      if (isset($vals['creatorId'])) {
        $this->creatorId = $vals['creatorId'];
      }
      if (isset($vals['lastEditorId'])) {
        $this->lastEditorId = $vals['lastEditorId'];
      }
    }
  }

  public function getName() {
    return 'NoteAttributes';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->subjectDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->latitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->longitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::DOUBLE) {
            $xfer += $input->readDouble($this->altitude);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->author);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->source);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 15:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->sourceURL);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 16:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->sourceApplication);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 17:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->shareDate);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 18:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->reminderOrder);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 19:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->reminderDoneTime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 20:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->reminderTime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 21:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->placeName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 22:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->contentClass);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 23:
          if ($ftype == \TType::STRUCT) {
            $this->applicationData = new \EDAM\Types\LazyMap();
            $xfer += $this->applicationData->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 24:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->lastEditedBy);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 26:
          if ($ftype == \TType::MAP) {
            $this->classifications = array();
            $_size31 = 0;
            $_ktype32 = 0;
            $_vtype33 = 0;
            $xfer += $input->readMapBegin($_ktype32, $_vtype33, $_size31);
            for ($_i35 = 0; $_i35 < $_size31; ++$_i35)
            {
              $key36 = '';
              $val37 = '';
              $xfer += $input->readString($key36);
              $xfer += $input->readString($val37);
              $this->classifications[$key36] = $val37;
            }
            $xfer += $input->readMapEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 27:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->creatorId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 28:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->lastEditorId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteAttributes');
    if ($this->subjectDate !== null) {
      $xfer += $output->writeFieldBegin('subjectDate', \TType::I64, 1);
      $xfer += $output->writeI64($this->subjectDate);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->latitude !== null) {
      $xfer += $output->writeFieldBegin('latitude', \TType::DOUBLE, 10);
      $xfer += $output->writeDouble($this->latitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->longitude !== null) {
      $xfer += $output->writeFieldBegin('longitude', \TType::DOUBLE, 11);
      $xfer += $output->writeDouble($this->longitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->altitude !== null) {
      $xfer += $output->writeFieldBegin('altitude', \TType::DOUBLE, 12);
      $xfer += $output->writeDouble($this->altitude);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->author !== null) {
      $xfer += $output->writeFieldBegin('author', \TType::STRING, 13);
      $xfer += $output->writeString($this->author);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->source !== null) {
      $xfer += $output->writeFieldBegin('source', \TType::STRING, 14);
      $xfer += $output->writeString($this->source);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sourceURL !== null) {
      $xfer += $output->writeFieldBegin('sourceURL', \TType::STRING, 15);
      $xfer += $output->writeString($this->sourceURL);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sourceApplication !== null) {
      $xfer += $output->writeFieldBegin('sourceApplication', \TType::STRING, 16);
      $xfer += $output->writeString($this->sourceApplication);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->shareDate !== null) {
      $xfer += $output->writeFieldBegin('shareDate', \TType::I64, 17);
      $xfer += $output->writeI64($this->shareDate);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->reminderOrder !== null) {
      $xfer += $output->writeFieldBegin('reminderOrder', \TType::I64, 18);
      $xfer += $output->writeI64($this->reminderOrder);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->reminderDoneTime !== null) {
      $xfer += $output->writeFieldBegin('reminderDoneTime', \TType::I64, 19);
      $xfer += $output->writeI64($this->reminderDoneTime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->reminderTime !== null) {
      $xfer += $output->writeFieldBegin('reminderTime', \TType::I64, 20);
      $xfer += $output->writeI64($this->reminderTime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->placeName !== null) {
      $xfer += $output->writeFieldBegin('placeName', \TType::STRING, 21);
      $xfer += $output->writeString($this->placeName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->contentClass !== null) {
      $xfer += $output->writeFieldBegin('contentClass', \TType::STRING, 22);
      $xfer += $output->writeString($this->contentClass);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->applicationData !== null) {
      if (!is_object($this->applicationData)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('applicationData', \TType::STRUCT, 23);
      $xfer += $this->applicationData->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->lastEditedBy !== null) {
      $xfer += $output->writeFieldBegin('lastEditedBy', \TType::STRING, 24);
      $xfer += $output->writeString($this->lastEditedBy);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->classifications !== null) {
      if (!is_array($this->classifications)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('classifications', \TType::MAP, 26);
      {
        $output->writeMapBegin(\TType::STRING, \TType::STRING, count($this->classifications));
        {
          foreach ($this->classifications as $kiter38 => $viter39)
          {
            $xfer += $output->writeString($kiter38);
            $xfer += $output->writeString($viter39);
          }
        }
        $output->writeMapEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->creatorId !== null) {
      $xfer += $output->writeFieldBegin('creatorId', \TType::I32, 27);
      $xfer += $output->writeI32($this->creatorId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->lastEditorId !== null) {
      $xfer += $output->writeFieldBegin('lastEditorId', \TType::I32, 28);
      $xfer += $output->writeI32($this->lastEditorId);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class Note {
  static $_TSPEC;

  public $guid = null;
  public $title = null;
  public $content = null;
  public $contentHash = null;
  public $contentLength = null;
  public $created = null;
  public $updated = null;
  public $deleted = null;
  public $active = null;
  public $updateSequenceNum = null;
  public $notebookGuid = null;
  public $tagGuids = null;
  public $resources = null;
  public $attributes = null;
  public $tagNames = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'title',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'content',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'contentHash',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'contentLength',
          'type' => \TType::I32,
          ),
        6 => array(
          'var' => 'created',
          'type' => \TType::I64,
          ),
        7 => array(
          'var' => 'updated',
          'type' => \TType::I64,
          ),
        8 => array(
          'var' => 'deleted',
          'type' => \TType::I64,
          ),
        9 => array(
          'var' => 'active',
          'type' => \TType::BOOL,
          ),
        10 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        11 => array(
          'var' => 'notebookGuid',
          'type' => \TType::STRING,
          ),
        12 => array(
          'var' => 'tagGuids',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        13 => array(
          'var' => 'resources',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Resource',
            ),
          ),
        14 => array(
          'var' => 'attributes',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\NoteAttributes',
          ),
        15 => array(
          'var' => 'tagNames',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['title'])) {
        $this->title = $vals['title'];
      }
      if (isset($vals['content'])) {
        $this->content = $vals['content'];
      }
      if (isset($vals['contentHash'])) {
        $this->contentHash = $vals['contentHash'];
      }
      if (isset($vals['contentLength'])) {
        $this->contentLength = $vals['contentLength'];
      }
      if (isset($vals['created'])) {
        $this->created = $vals['created'];
      }
      if (isset($vals['updated'])) {
        $this->updated = $vals['updated'];
      }
      if (isset($vals['deleted'])) {
        $this->deleted = $vals['deleted'];
      }
      if (isset($vals['active'])) {
        $this->active = $vals['active'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['notebookGuid'])) {
        $this->notebookGuid = $vals['notebookGuid'];
      }
      if (isset($vals['tagGuids'])) {
        $this->tagGuids = $vals['tagGuids'];
      }
      if (isset($vals['resources'])) {
        $this->resources = $vals['resources'];
      }
      if (isset($vals['attributes'])) {
        $this->attributes = $vals['attributes'];
      }
      if (isset($vals['tagNames'])) {
        $this->tagNames = $vals['tagNames'];
      }
    }
  }

  public function getName() {
    return 'Note';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->title);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->content);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->contentHash);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->contentLength);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->created);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->updated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->deleted);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->active);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->notebookGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::LST) {
            $this->tagGuids = array();
            $_size40 = 0;
            $_etype43 = 0;
            $xfer += $input->readListBegin($_etype43, $_size40);
            for ($_i44 = 0; $_i44 < $_size40; ++$_i44)
            {
              $elem45 = null;
              $xfer += $input->readString($elem45);
              $this->tagGuids []= $elem45;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::LST) {
            $this->resources = array();
            $_size46 = 0;
            $_etype49 = 0;
            $xfer += $input->readListBegin($_etype49, $_size46);
            for ($_i50 = 0; $_i50 < $_size46; ++$_i50)
            {
              $elem51 = null;
              $elem51 = new \EDAM\Types\Resource();
              $xfer += $elem51->read($input);
              $this->resources []= $elem51;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::STRUCT) {
            $this->attributes = new \EDAM\Types\NoteAttributes();
            $xfer += $this->attributes->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 15:
          if ($ftype == \TType::LST) {
            $this->tagNames = array();
            $_size52 = 0;
            $_etype55 = 0;
            $xfer += $input->readListBegin($_etype55, $_size52);
            for ($_i56 = 0; $_i56 < $_size52; ++$_i56)
            {
              $elem57 = null;
              $xfer += $input->readString($elem57);
              $this->tagNames []= $elem57;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Note');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->title !== null) {
      $xfer += $output->writeFieldBegin('title', \TType::STRING, 2);
      $xfer += $output->writeString($this->title);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->content !== null) {
      $xfer += $output->writeFieldBegin('content', \TType::STRING, 3);
      $xfer += $output->writeString($this->content);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->contentHash !== null) {
      $xfer += $output->writeFieldBegin('contentHash', \TType::STRING, 4);
      $xfer += $output->writeString($this->contentHash);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->contentLength !== null) {
      $xfer += $output->writeFieldBegin('contentLength', \TType::I32, 5);
      $xfer += $output->writeI32($this->contentLength);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->created !== null) {
      $xfer += $output->writeFieldBegin('created', \TType::I64, 6);
      $xfer += $output->writeI64($this->created);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updated !== null) {
      $xfer += $output->writeFieldBegin('updated', \TType::I64, 7);
      $xfer += $output->writeI64($this->updated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deleted !== null) {
      $xfer += $output->writeFieldBegin('deleted', \TType::I64, 8);
      $xfer += $output->writeI64($this->deleted);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->active !== null) {
      $xfer += $output->writeFieldBegin('active', \TType::BOOL, 9);
      $xfer += $output->writeBool($this->active);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 10);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebookGuid !== null) {
      $xfer += $output->writeFieldBegin('notebookGuid', \TType::STRING, 11);
      $xfer += $output->writeString($this->notebookGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tagGuids !== null) {
      if (!is_array($this->tagGuids)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tagGuids', \TType::LST, 12);
      {
        $output->writeListBegin(\TType::STRING, count($this->tagGuids));
        {
          foreach ($this->tagGuids as $iter58)
          {
            $xfer += $output->writeString($iter58);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->resources !== null) {
      if (!is_array($this->resources)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('resources', \TType::LST, 13);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->resources));
        {
          foreach ($this->resources as $iter59)
          {
            $xfer += $iter59->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->attributes !== null) {
      if (!is_object($this->attributes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('attributes', \TType::STRUCT, 14);
      $xfer += $this->attributes->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tagNames !== null) {
      if (!is_array($this->tagNames)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tagNames', \TType::LST, 15);
      {
        $output->writeListBegin(\TType::STRING, count($this->tagNames));
        {
          foreach ($this->tagNames as $iter60)
          {
            $xfer += $output->writeString($iter60);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class Publishing {
  static $_TSPEC;

  public $uri = null;
  public $order = null;
  public $ascending = null;
  public $publicDescription = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'uri',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'order',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'ascending',
          'type' => \TType::BOOL,
          ),
        4 => array(
          'var' => 'publicDescription',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['uri'])) {
        $this->uri = $vals['uri'];
      }
      if (isset($vals['order'])) {
        $this->order = $vals['order'];
      }
      if (isset($vals['ascending'])) {
        $this->ascending = $vals['ascending'];
      }
      if (isset($vals['publicDescription'])) {
        $this->publicDescription = $vals['publicDescription'];
      }
    }
  }

  public function getName() {
    return 'Publishing';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->uri);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->order);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->ascending);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->publicDescription);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Publishing');
    if ($this->uri !== null) {
      $xfer += $output->writeFieldBegin('uri', \TType::STRING, 1);
      $xfer += $output->writeString($this->uri);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->order !== null) {
      $xfer += $output->writeFieldBegin('order', \TType::I32, 2);
      $xfer += $output->writeI32($this->order);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->ascending !== null) {
      $xfer += $output->writeFieldBegin('ascending', \TType::BOOL, 3);
      $xfer += $output->writeBool($this->ascending);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->publicDescription !== null) {
      $xfer += $output->writeFieldBegin('publicDescription', \TType::STRING, 4);
      $xfer += $output->writeString($this->publicDescription);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class BusinessNotebook {
  static $_TSPEC;

  public $notebookDescription = null;
  public $privilege = null;
  public $recommended = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'notebookDescription',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'privilege',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'recommended',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['notebookDescription'])) {
        $this->notebookDescription = $vals['notebookDescription'];
      }
      if (isset($vals['privilege'])) {
        $this->privilege = $vals['privilege'];
      }
      if (isset($vals['recommended'])) {
        $this->recommended = $vals['recommended'];
      }
    }
  }

  public function getName() {
    return 'BusinessNotebook';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->notebookDescription);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->privilege);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->recommended);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('BusinessNotebook');
    if ($this->notebookDescription !== null) {
      $xfer += $output->writeFieldBegin('notebookDescription', \TType::STRING, 1);
      $xfer += $output->writeString($this->notebookDescription);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->privilege !== null) {
      $xfer += $output->writeFieldBegin('privilege', \TType::I32, 2);
      $xfer += $output->writeI32($this->privilege);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->recommended !== null) {
      $xfer += $output->writeFieldBegin('recommended', \TType::BOOL, 3);
      $xfer += $output->writeBool($this->recommended);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class SavedSearchScope {
  static $_TSPEC;

  public $includeAccount = null;
  public $includePersonalLinkedNotebooks = null;
  public $includeBusinessLinkedNotebooks = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'includeAccount',
          'type' => \TType::BOOL,
          ),
        2 => array(
          'var' => 'includePersonalLinkedNotebooks',
          'type' => \TType::BOOL,
          ),
        3 => array(
          'var' => 'includeBusinessLinkedNotebooks',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['includeAccount'])) {
        $this->includeAccount = $vals['includeAccount'];
      }
      if (isset($vals['includePersonalLinkedNotebooks'])) {
        $this->includePersonalLinkedNotebooks = $vals['includePersonalLinkedNotebooks'];
      }
      if (isset($vals['includeBusinessLinkedNotebooks'])) {
        $this->includeBusinessLinkedNotebooks = $vals['includeBusinessLinkedNotebooks'];
      }
    }
  }

  public function getName() {
    return 'SavedSearchScope';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeAccount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includePersonalLinkedNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeBusinessLinkedNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SavedSearchScope');
    if ($this->includeAccount !== null) {
      $xfer += $output->writeFieldBegin('includeAccount', \TType::BOOL, 1);
      $xfer += $output->writeBool($this->includeAccount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includePersonalLinkedNotebooks !== null) {
      $xfer += $output->writeFieldBegin('includePersonalLinkedNotebooks', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->includePersonalLinkedNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeBusinessLinkedNotebooks !== null) {
      $xfer += $output->writeFieldBegin('includeBusinessLinkedNotebooks', \TType::BOOL, 3);
      $xfer += $output->writeBool($this->includeBusinessLinkedNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class SavedSearch {
  static $_TSPEC;

  public $guid = null;
  public $name = null;
  public $query = null;
  public $format = null;
  public $updateSequenceNum = null;
  public $scope = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'name',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'query',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'format',
          'type' => \TType::I32,
          ),
        5 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        6 => array(
          'var' => 'scope',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\SavedSearchScope',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['name'])) {
        $this->name = $vals['name'];
      }
      if (isset($vals['query'])) {
        $this->query = $vals['query'];
      }
      if (isset($vals['format'])) {
        $this->format = $vals['format'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['scope'])) {
        $this->scope = $vals['scope'];
      }
    }
  }

  public function getName() {
    return 'SavedSearch';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->name);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->query);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->format);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRUCT) {
            $this->scope = new \EDAM\Types\SavedSearchScope();
            $xfer += $this->scope->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SavedSearch');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->name !== null) {
      $xfer += $output->writeFieldBegin('name', \TType::STRING, 2);
      $xfer += $output->writeString($this->name);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->query !== null) {
      $xfer += $output->writeFieldBegin('query', \TType::STRING, 3);
      $xfer += $output->writeString($this->query);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->format !== null) {
      $xfer += $output->writeFieldBegin('format', \TType::I32, 4);
      $xfer += $output->writeI32($this->format);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 5);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->scope !== null) {
      if (!is_object($this->scope)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('scope', \TType::STRUCT, 6);
      $xfer += $this->scope->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class SharedNotebookRecipientSettings {
  static $_TSPEC;

  public $reminderNotifyEmail = null;
  public $reminderNotifyInApp = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'reminderNotifyEmail',
          'type' => \TType::BOOL,
          ),
        2 => array(
          'var' => 'reminderNotifyInApp',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['reminderNotifyEmail'])) {
        $this->reminderNotifyEmail = $vals['reminderNotifyEmail'];
      }
      if (isset($vals['reminderNotifyInApp'])) {
        $this->reminderNotifyInApp = $vals['reminderNotifyInApp'];
      }
    }
  }

  public function getName() {
    return 'SharedNotebookRecipientSettings';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->reminderNotifyEmail);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->reminderNotifyInApp);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SharedNotebookRecipientSettings');
    if ($this->reminderNotifyEmail !== null) {
      $xfer += $output->writeFieldBegin('reminderNotifyEmail', \TType::BOOL, 1);
      $xfer += $output->writeBool($this->reminderNotifyEmail);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->reminderNotifyInApp !== null) {
      $xfer += $output->writeFieldBegin('reminderNotifyInApp', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->reminderNotifyInApp);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class SharedNotebook {
  static $_TSPEC;

  public $id = null;
  public $userId = null;
  public $notebookGuid = null;
  public $email = null;
  public $notebookModifiable = null;
  public $requireLogin = null;
  public $serviceCreated = null;
  public $serviceUpdated = null;
  public $shareKey = null;
  public $username = null;
  public $privilege = null;
  public $allowPreview = null;
  public $recipientSettings = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'id',
          'type' => \TType::I64,
          ),
        2 => array(
          'var' => 'userId',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'notebookGuid',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'email',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'notebookModifiable',
          'type' => \TType::BOOL,
          ),
        6 => array(
          'var' => 'requireLogin',
          'type' => \TType::BOOL,
          ),
        7 => array(
          'var' => 'serviceCreated',
          'type' => \TType::I64,
          ),
        10 => array(
          'var' => 'serviceUpdated',
          'type' => \TType::I64,
          ),
        8 => array(
          'var' => 'shareKey',
          'type' => \TType::STRING,
          ),
        9 => array(
          'var' => 'username',
          'type' => \TType::STRING,
          ),
        11 => array(
          'var' => 'privilege',
          'type' => \TType::I32,
          ),
        12 => array(
          'var' => 'allowPreview',
          'type' => \TType::BOOL,
          ),
        13 => array(
          'var' => 'recipientSettings',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\SharedNotebookRecipientSettings',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['id'])) {
        $this->id = $vals['id'];
      }
      if (isset($vals['userId'])) {
        $this->userId = $vals['userId'];
      }
      if (isset($vals['notebookGuid'])) {
        $this->notebookGuid = $vals['notebookGuid'];
      }
      if (isset($vals['email'])) {
        $this->email = $vals['email'];
      }
      if (isset($vals['notebookModifiable'])) {
        $this->notebookModifiable = $vals['notebookModifiable'];
      }
      if (isset($vals['requireLogin'])) {
        $this->requireLogin = $vals['requireLogin'];
      }
      if (isset($vals['serviceCreated'])) {
        $this->serviceCreated = $vals['serviceCreated'];
      }
      if (isset($vals['serviceUpdated'])) {
        $this->serviceUpdated = $vals['serviceUpdated'];
      }
      if (isset($vals['shareKey'])) {
        $this->shareKey = $vals['shareKey'];
      }
      if (isset($vals['username'])) {
        $this->username = $vals['username'];
      }
      if (isset($vals['privilege'])) {
        $this->privilege = $vals['privilege'];
      }
      if (isset($vals['allowPreview'])) {
        $this->allowPreview = $vals['allowPreview'];
      }
      if (isset($vals['recipientSettings'])) {
        $this->recipientSettings = $vals['recipientSettings'];
      }
    }
  }

  public function getName() {
    return 'SharedNotebook';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->id);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->userId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->notebookGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->email);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->notebookModifiable);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->requireLogin);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->serviceCreated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->serviceUpdated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->shareKey);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->username);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->privilege);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->allowPreview);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::STRUCT) {
            $this->recipientSettings = new \EDAM\Types\SharedNotebookRecipientSettings();
            $xfer += $this->recipientSettings->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SharedNotebook');
    if ($this->id !== null) {
      $xfer += $output->writeFieldBegin('id', \TType::I64, 1);
      $xfer += $output->writeI64($this->id);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userId !== null) {
      $xfer += $output->writeFieldBegin('userId', \TType::I32, 2);
      $xfer += $output->writeI32($this->userId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebookGuid !== null) {
      $xfer += $output->writeFieldBegin('notebookGuid', \TType::STRING, 3);
      $xfer += $output->writeString($this->notebookGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->email !== null) {
      $xfer += $output->writeFieldBegin('email', \TType::STRING, 4);
      $xfer += $output->writeString($this->email);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebookModifiable !== null) {
      $xfer += $output->writeFieldBegin('notebookModifiable', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->notebookModifiable);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->requireLogin !== null) {
      $xfer += $output->writeFieldBegin('requireLogin', \TType::BOOL, 6);
      $xfer += $output->writeBool($this->requireLogin);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->serviceCreated !== null) {
      $xfer += $output->writeFieldBegin('serviceCreated', \TType::I64, 7);
      $xfer += $output->writeI64($this->serviceCreated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->shareKey !== null) {
      $xfer += $output->writeFieldBegin('shareKey', \TType::STRING, 8);
      $xfer += $output->writeString($this->shareKey);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->username !== null) {
      $xfer += $output->writeFieldBegin('username', \TType::STRING, 9);
      $xfer += $output->writeString($this->username);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->serviceUpdated !== null) {
      $xfer += $output->writeFieldBegin('serviceUpdated', \TType::I64, 10);
      $xfer += $output->writeI64($this->serviceUpdated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->privilege !== null) {
      $xfer += $output->writeFieldBegin('privilege', \TType::I32, 11);
      $xfer += $output->writeI32($this->privilege);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->allowPreview !== null) {
      $xfer += $output->writeFieldBegin('allowPreview', \TType::BOOL, 12);
      $xfer += $output->writeBool($this->allowPreview);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->recipientSettings !== null) {
      if (!is_object($this->recipientSettings)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('recipientSettings', \TType::STRUCT, 13);
      $xfer += $this->recipientSettings->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NotebookRestrictions {
  static $_TSPEC;

  public $noReadNotes = null;
  public $noCreateNotes = null;
  public $noUpdateNotes = null;
  public $noExpungeNotes = null;
  public $noShareNotes = null;
  public $noEmailNotes = null;
  public $noSendMessageToRecipients = null;
  public $noUpdateNotebook = null;
  public $noExpungeNotebook = null;
  public $noSetDefaultNotebook = null;
  public $noSetNotebookStack = null;
  public $noPublishToPublic = null;
  public $noPublishToBusinessLibrary = null;
  public $noCreateTags = null;
  public $noUpdateTags = null;
  public $noExpungeTags = null;
  public $noSetParentTag = null;
  public $noCreateSharedNotebooks = null;
  public $updateWhichSharedNotebookRestrictions = null;
  public $expungeWhichSharedNotebookRestrictions = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'noReadNotes',
          'type' => \TType::BOOL,
          ),
        2 => array(
          'var' => 'noCreateNotes',
          'type' => \TType::BOOL,
          ),
        3 => array(
          'var' => 'noUpdateNotes',
          'type' => \TType::BOOL,
          ),
        4 => array(
          'var' => 'noExpungeNotes',
          'type' => \TType::BOOL,
          ),
        5 => array(
          'var' => 'noShareNotes',
          'type' => \TType::BOOL,
          ),
        6 => array(
          'var' => 'noEmailNotes',
          'type' => \TType::BOOL,
          ),
        7 => array(
          'var' => 'noSendMessageToRecipients',
          'type' => \TType::BOOL,
          ),
        8 => array(
          'var' => 'noUpdateNotebook',
          'type' => \TType::BOOL,
          ),
        9 => array(
          'var' => 'noExpungeNotebook',
          'type' => \TType::BOOL,
          ),
        10 => array(
          'var' => 'noSetDefaultNotebook',
          'type' => \TType::BOOL,
          ),
        11 => array(
          'var' => 'noSetNotebookStack',
          'type' => \TType::BOOL,
          ),
        12 => array(
          'var' => 'noPublishToPublic',
          'type' => \TType::BOOL,
          ),
        13 => array(
          'var' => 'noPublishToBusinessLibrary',
          'type' => \TType::BOOL,
          ),
        14 => array(
          'var' => 'noCreateTags',
          'type' => \TType::BOOL,
          ),
        15 => array(
          'var' => 'noUpdateTags',
          'type' => \TType::BOOL,
          ),
        16 => array(
          'var' => 'noExpungeTags',
          'type' => \TType::BOOL,
          ),
        17 => array(
          'var' => 'noSetParentTag',
          'type' => \TType::BOOL,
          ),
        18 => array(
          'var' => 'noCreateSharedNotebooks',
          'type' => \TType::BOOL,
          ),
        19 => array(
          'var' => 'updateWhichSharedNotebookRestrictions',
          'type' => \TType::I32,
          ),
        20 => array(
          'var' => 'expungeWhichSharedNotebookRestrictions',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['noReadNotes'])) {
        $this->noReadNotes = $vals['noReadNotes'];
      }
      if (isset($vals['noCreateNotes'])) {
        $this->noCreateNotes = $vals['noCreateNotes'];
      }
      if (isset($vals['noUpdateNotes'])) {
        $this->noUpdateNotes = $vals['noUpdateNotes'];
      }
      if (isset($vals['noExpungeNotes'])) {
        $this->noExpungeNotes = $vals['noExpungeNotes'];
      }
      if (isset($vals['noShareNotes'])) {
        $this->noShareNotes = $vals['noShareNotes'];
      }
      if (isset($vals['noEmailNotes'])) {
        $this->noEmailNotes = $vals['noEmailNotes'];
      }
      if (isset($vals['noSendMessageToRecipients'])) {
        $this->noSendMessageToRecipients = $vals['noSendMessageToRecipients'];
      }
      if (isset($vals['noUpdateNotebook'])) {
        $this->noUpdateNotebook = $vals['noUpdateNotebook'];
      }
      if (isset($vals['noExpungeNotebook'])) {
        $this->noExpungeNotebook = $vals['noExpungeNotebook'];
      }
      if (isset($vals['noSetDefaultNotebook'])) {
        $this->noSetDefaultNotebook = $vals['noSetDefaultNotebook'];
      }
      if (isset($vals['noSetNotebookStack'])) {
        $this->noSetNotebookStack = $vals['noSetNotebookStack'];
      }
      if (isset($vals['noPublishToPublic'])) {
        $this->noPublishToPublic = $vals['noPublishToPublic'];
      }
      if (isset($vals['noPublishToBusinessLibrary'])) {
        $this->noPublishToBusinessLibrary = $vals['noPublishToBusinessLibrary'];
      }
      if (isset($vals['noCreateTags'])) {
        $this->noCreateTags = $vals['noCreateTags'];
      }
      if (isset($vals['noUpdateTags'])) {
        $this->noUpdateTags = $vals['noUpdateTags'];
      }
      if (isset($vals['noExpungeTags'])) {
        $this->noExpungeTags = $vals['noExpungeTags'];
      }
      if (isset($vals['noSetParentTag'])) {
        $this->noSetParentTag = $vals['noSetParentTag'];
      }
      if (isset($vals['noCreateSharedNotebooks'])) {
        $this->noCreateSharedNotebooks = $vals['noCreateSharedNotebooks'];
      }
      if (isset($vals['updateWhichSharedNotebookRestrictions'])) {
        $this->updateWhichSharedNotebookRestrictions = $vals['updateWhichSharedNotebookRestrictions'];
      }
      if (isset($vals['expungeWhichSharedNotebookRestrictions'])) {
        $this->expungeWhichSharedNotebookRestrictions = $vals['expungeWhichSharedNotebookRestrictions'];
      }
    }
  }

  public function getName() {
    return 'NotebookRestrictions';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noReadNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noCreateNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noUpdateNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noExpungeNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noShareNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noEmailNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noSendMessageToRecipients);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noUpdateNotebook);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noExpungeNotebook);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noSetDefaultNotebook);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noSetNotebookStack);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noPublishToPublic);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noPublishToBusinessLibrary);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noCreateTags);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 15:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noUpdateTags);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 16:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noExpungeTags);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 17:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noSetParentTag);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 18:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->noCreateSharedNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 19:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateWhichSharedNotebookRestrictions);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 20:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->expungeWhichSharedNotebookRestrictions);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NotebookRestrictions');
    if ($this->noReadNotes !== null) {
      $xfer += $output->writeFieldBegin('noReadNotes', \TType::BOOL, 1);
      $xfer += $output->writeBool($this->noReadNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noCreateNotes !== null) {
      $xfer += $output->writeFieldBegin('noCreateNotes', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->noCreateNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noUpdateNotes !== null) {
      $xfer += $output->writeFieldBegin('noUpdateNotes', \TType::BOOL, 3);
      $xfer += $output->writeBool($this->noUpdateNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noExpungeNotes !== null) {
      $xfer += $output->writeFieldBegin('noExpungeNotes', \TType::BOOL, 4);
      $xfer += $output->writeBool($this->noExpungeNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noShareNotes !== null) {
      $xfer += $output->writeFieldBegin('noShareNotes', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->noShareNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noEmailNotes !== null) {
      $xfer += $output->writeFieldBegin('noEmailNotes', \TType::BOOL, 6);
      $xfer += $output->writeBool($this->noEmailNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noSendMessageToRecipients !== null) {
      $xfer += $output->writeFieldBegin('noSendMessageToRecipients', \TType::BOOL, 7);
      $xfer += $output->writeBool($this->noSendMessageToRecipients);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noUpdateNotebook !== null) {
      $xfer += $output->writeFieldBegin('noUpdateNotebook', \TType::BOOL, 8);
      $xfer += $output->writeBool($this->noUpdateNotebook);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noExpungeNotebook !== null) {
      $xfer += $output->writeFieldBegin('noExpungeNotebook', \TType::BOOL, 9);
      $xfer += $output->writeBool($this->noExpungeNotebook);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noSetDefaultNotebook !== null) {
      $xfer += $output->writeFieldBegin('noSetDefaultNotebook', \TType::BOOL, 10);
      $xfer += $output->writeBool($this->noSetDefaultNotebook);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noSetNotebookStack !== null) {
      $xfer += $output->writeFieldBegin('noSetNotebookStack', \TType::BOOL, 11);
      $xfer += $output->writeBool($this->noSetNotebookStack);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noPublishToPublic !== null) {
      $xfer += $output->writeFieldBegin('noPublishToPublic', \TType::BOOL, 12);
      $xfer += $output->writeBool($this->noPublishToPublic);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noPublishToBusinessLibrary !== null) {
      $xfer += $output->writeFieldBegin('noPublishToBusinessLibrary', \TType::BOOL, 13);
      $xfer += $output->writeBool($this->noPublishToBusinessLibrary);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noCreateTags !== null) {
      $xfer += $output->writeFieldBegin('noCreateTags', \TType::BOOL, 14);
      $xfer += $output->writeBool($this->noCreateTags);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noUpdateTags !== null) {
      $xfer += $output->writeFieldBegin('noUpdateTags', \TType::BOOL, 15);
      $xfer += $output->writeBool($this->noUpdateTags);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noExpungeTags !== null) {
      $xfer += $output->writeFieldBegin('noExpungeTags', \TType::BOOL, 16);
      $xfer += $output->writeBool($this->noExpungeTags);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noSetParentTag !== null) {
      $xfer += $output->writeFieldBegin('noSetParentTag', \TType::BOOL, 17);
      $xfer += $output->writeBool($this->noSetParentTag);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noCreateSharedNotebooks !== null) {
      $xfer += $output->writeFieldBegin('noCreateSharedNotebooks', \TType::BOOL, 18);
      $xfer += $output->writeBool($this->noCreateSharedNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateWhichSharedNotebookRestrictions !== null) {
      $xfer += $output->writeFieldBegin('updateWhichSharedNotebookRestrictions', \TType::I32, 19);
      $xfer += $output->writeI32($this->updateWhichSharedNotebookRestrictions);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->expungeWhichSharedNotebookRestrictions !== null) {
      $xfer += $output->writeFieldBegin('expungeWhichSharedNotebookRestrictions', \TType::I32, 20);
      $xfer += $output->writeI32($this->expungeWhichSharedNotebookRestrictions);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class Notebook {
  static $_TSPEC;

  public $guid = null;
  public $name = null;
  public $updateSequenceNum = null;
  public $defaultNotebook = null;
  public $serviceCreated = null;
  public $serviceUpdated = null;
  public $publishing = null;
  public $published = null;
  public $stack = null;
  public $sharedNotebookIds = null;
  public $sharedNotebooks = null;
  public $businessNotebook = null;
  public $contact = null;
  public $restrictions = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'name',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        6 => array(
          'var' => 'defaultNotebook',
          'type' => \TType::BOOL,
          ),
        7 => array(
          'var' => 'serviceCreated',
          'type' => \TType::I64,
          ),
        8 => array(
          'var' => 'serviceUpdated',
          'type' => \TType::I64,
          ),
        10 => array(
          'var' => 'publishing',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\Publishing',
          ),
        11 => array(
          'var' => 'published',
          'type' => \TType::BOOL,
          ),
        12 => array(
          'var' => 'stack',
          'type' => \TType::STRING,
          ),
        13 => array(
          'var' => 'sharedNotebookIds',
          'type' => \TType::LST,
          'etype' => \TType::I64,
          'elem' => array(
            'type' => \TType::I64,
            ),
          ),
        14 => array(
          'var' => 'sharedNotebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\SharedNotebook',
            ),
          ),
        15 => array(
          'var' => 'businessNotebook',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\BusinessNotebook',
          ),
        16 => array(
          'var' => 'contact',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\User',
          ),
        17 => array(
          'var' => 'restrictions',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\NotebookRestrictions',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['name'])) {
        $this->name = $vals['name'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['defaultNotebook'])) {
        $this->defaultNotebook = $vals['defaultNotebook'];
      }
      if (isset($vals['serviceCreated'])) {
        $this->serviceCreated = $vals['serviceCreated'];
      }
      if (isset($vals['serviceUpdated'])) {
        $this->serviceUpdated = $vals['serviceUpdated'];
      }
      if (isset($vals['publishing'])) {
        $this->publishing = $vals['publishing'];
      }
      if (isset($vals['published'])) {
        $this->published = $vals['published'];
      }
      if (isset($vals['stack'])) {
        $this->stack = $vals['stack'];
      }
      if (isset($vals['sharedNotebookIds'])) {
        $this->sharedNotebookIds = $vals['sharedNotebookIds'];
      }
      if (isset($vals['sharedNotebooks'])) {
        $this->sharedNotebooks = $vals['sharedNotebooks'];
      }
      if (isset($vals['businessNotebook'])) {
        $this->businessNotebook = $vals['businessNotebook'];
      }
      if (isset($vals['contact'])) {
        $this->contact = $vals['contact'];
      }
      if (isset($vals['restrictions'])) {
        $this->restrictions = $vals['restrictions'];
      }
    }
  }

  public function getName() {
    return 'Notebook';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->name);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->defaultNotebook);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->serviceCreated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->serviceUpdated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::STRUCT) {
            $this->publishing = new \EDAM\Types\Publishing();
            $xfer += $this->publishing->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->published);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->stack);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::LST) {
            $this->sharedNotebookIds = array();
            $_size61 = 0;
            $_etype64 = 0;
            $xfer += $input->readListBegin($_etype64, $_size61);
            for ($_i65 = 0; $_i65 < $_size61; ++$_i65)
            {
              $elem66 = null;
              $xfer += $input->readI64($elem66);
              $this->sharedNotebookIds []= $elem66;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::LST) {
            $this->sharedNotebooks = array();
            $_size67 = 0;
            $_etype70 = 0;
            $xfer += $input->readListBegin($_etype70, $_size67);
            for ($_i71 = 0; $_i71 < $_size67; ++$_i71)
            {
              $elem72 = null;
              $elem72 = new \EDAM\Types\SharedNotebook();
              $xfer += $elem72->read($input);
              $this->sharedNotebooks []= $elem72;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 15:
          if ($ftype == \TType::STRUCT) {
            $this->businessNotebook = new \EDAM\Types\BusinessNotebook();
            $xfer += $this->businessNotebook->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 16:
          if ($ftype == \TType::STRUCT) {
            $this->contact = new \EDAM\Types\User();
            $xfer += $this->contact->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 17:
          if ($ftype == \TType::STRUCT) {
            $this->restrictions = new \EDAM\Types\NotebookRestrictions();
            $xfer += $this->restrictions->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('Notebook');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->name !== null) {
      $xfer += $output->writeFieldBegin('name', \TType::STRING, 2);
      $xfer += $output->writeString($this->name);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 5);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->defaultNotebook !== null) {
      $xfer += $output->writeFieldBegin('defaultNotebook', \TType::BOOL, 6);
      $xfer += $output->writeBool($this->defaultNotebook);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->serviceCreated !== null) {
      $xfer += $output->writeFieldBegin('serviceCreated', \TType::I64, 7);
      $xfer += $output->writeI64($this->serviceCreated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->serviceUpdated !== null) {
      $xfer += $output->writeFieldBegin('serviceUpdated', \TType::I64, 8);
      $xfer += $output->writeI64($this->serviceUpdated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->publishing !== null) {
      if (!is_object($this->publishing)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('publishing', \TType::STRUCT, 10);
      $xfer += $this->publishing->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->published !== null) {
      $xfer += $output->writeFieldBegin('published', \TType::BOOL, 11);
      $xfer += $output->writeBool($this->published);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->stack !== null) {
      $xfer += $output->writeFieldBegin('stack', \TType::STRING, 12);
      $xfer += $output->writeString($this->stack);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sharedNotebookIds !== null) {
      if (!is_array($this->sharedNotebookIds)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('sharedNotebookIds', \TType::LST, 13);
      {
        $output->writeListBegin(\TType::I64, count($this->sharedNotebookIds));
        {
          foreach ($this->sharedNotebookIds as $iter73)
          {
            $xfer += $output->writeI64($iter73);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->sharedNotebooks !== null) {
      if (!is_array($this->sharedNotebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('sharedNotebooks', \TType::LST, 14);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->sharedNotebooks));
        {
          foreach ($this->sharedNotebooks as $iter74)
          {
            $xfer += $iter74->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessNotebook !== null) {
      if (!is_object($this->businessNotebook)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('businessNotebook', \TType::STRUCT, 15);
      $xfer += $this->businessNotebook->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->contact !== null) {
      if (!is_object($this->contact)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('contact', \TType::STRUCT, 16);
      $xfer += $this->contact->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->restrictions !== null) {
      if (!is_object($this->restrictions)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('restrictions', \TType::STRUCT, 17);
      $xfer += $this->restrictions->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class LinkedNotebook {
  static $_TSPEC;

  public $shareName = null;
  public $username = null;
  public $shardId = null;
  public $shareKey = null;
  public $uri = null;
  public $guid = null;
  public $updateSequenceNum = null;
  public $noteStoreUrl = null;
  public $webApiUrlPrefix = null;
  public $stack = null;
  public $businessId = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        2 => array(
          'var' => 'shareName',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'username',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'shardId',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'shareKey',
          'type' => \TType::STRING,
          ),
        6 => array(
          'var' => 'uri',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        8 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        9 => array(
          'var' => 'noteStoreUrl',
          'type' => \TType::STRING,
          ),
        10 => array(
          'var' => 'webApiUrlPrefix',
          'type' => \TType::STRING,
          ),
        11 => array(
          'var' => 'stack',
          'type' => \TType::STRING,
          ),
        12 => array(
          'var' => 'businessId',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['shareName'])) {
        $this->shareName = $vals['shareName'];
      }
      if (isset($vals['username'])) {
        $this->username = $vals['username'];
      }
      if (isset($vals['shardId'])) {
        $this->shardId = $vals['shardId'];
      }
      if (isset($vals['shareKey'])) {
        $this->shareKey = $vals['shareKey'];
      }
      if (isset($vals['uri'])) {
        $this->uri = $vals['uri'];
      }
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['noteStoreUrl'])) {
        $this->noteStoreUrl = $vals['noteStoreUrl'];
      }
      if (isset($vals['webApiUrlPrefix'])) {
        $this->webApiUrlPrefix = $vals['webApiUrlPrefix'];
      }
      if (isset($vals['stack'])) {
        $this->stack = $vals['stack'];
      }
      if (isset($vals['businessId'])) {
        $this->businessId = $vals['businessId'];
      }
    }
  }

  public function getName() {
    return 'LinkedNotebook';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->shareName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->username);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->shardId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->shareKey);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->uri);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->noteStoreUrl);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->webApiUrlPrefix);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->stack);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->businessId);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('LinkedNotebook');
    if ($this->shareName !== null) {
      $xfer += $output->writeFieldBegin('shareName', \TType::STRING, 2);
      $xfer += $output->writeString($this->shareName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->username !== null) {
      $xfer += $output->writeFieldBegin('username', \TType::STRING, 3);
      $xfer += $output->writeString($this->username);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->shardId !== null) {
      $xfer += $output->writeFieldBegin('shardId', \TType::STRING, 4);
      $xfer += $output->writeString($this->shardId);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->shareKey !== null) {
      $xfer += $output->writeFieldBegin('shareKey', \TType::STRING, 5);
      $xfer += $output->writeString($this->shareKey);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->uri !== null) {
      $xfer += $output->writeFieldBegin('uri', \TType::STRING, 6);
      $xfer += $output->writeString($this->uri);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 7);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 8);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->noteStoreUrl !== null) {
      $xfer += $output->writeFieldBegin('noteStoreUrl', \TType::STRING, 9);
      $xfer += $output->writeString($this->noteStoreUrl);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->webApiUrlPrefix !== null) {
      $xfer += $output->writeFieldBegin('webApiUrlPrefix', \TType::STRING, 10);
      $xfer += $output->writeString($this->webApiUrlPrefix);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->stack !== null) {
      $xfer += $output->writeFieldBegin('stack', \TType::STRING, 11);
      $xfer += $output->writeString($this->stack);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->businessId !== null) {
      $xfer += $output->writeFieldBegin('businessId', \TType::I32, 12);
      $xfer += $output->writeI32($this->businessId);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NotebookDescriptor {
  static $_TSPEC;

  public $guid = null;
  public $notebookDisplayName = null;
  public $contactName = null;
  public $hasSharedNotebook = null;
  public $joinedUserCount = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'notebookDisplayName',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'contactName',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'hasSharedNotebook',
          'type' => \TType::BOOL,
          ),
        5 => array(
          'var' => 'joinedUserCount',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['notebookDisplayName'])) {
        $this->notebookDisplayName = $vals['notebookDisplayName'];
      }
      if (isset($vals['contactName'])) {
        $this->contactName = $vals['contactName'];
      }
      if (isset($vals['hasSharedNotebook'])) {
        $this->hasSharedNotebook = $vals['hasSharedNotebook'];
      }
      if (isset($vals['joinedUserCount'])) {
        $this->joinedUserCount = $vals['joinedUserCount'];
      }
    }
  }

  public function getName() {
    return 'NotebookDescriptor';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->notebookDisplayName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->contactName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->hasSharedNotebook);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->joinedUserCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NotebookDescriptor');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebookDisplayName !== null) {
      $xfer += $output->writeFieldBegin('notebookDisplayName', \TType::STRING, 2);
      $xfer += $output->writeString($this->notebookDisplayName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->contactName !== null) {
      $xfer += $output->writeFieldBegin('contactName', \TType::STRING, 3);
      $xfer += $output->writeString($this->contactName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->hasSharedNotebook !== null) {
      $xfer += $output->writeFieldBegin('hasSharedNotebook', \TType::BOOL, 4);
      $xfer += $output->writeBool($this->hasSharedNotebook);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->joinedUserCount !== null) {
      $xfer += $output->writeFieldBegin('joinedUserCount', \TType::I32, 5);
      $xfer += $output->writeI32($this->joinedUserCount);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

?>
