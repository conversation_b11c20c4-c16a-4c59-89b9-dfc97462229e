<?php
namespace EDAM\UserStore;
/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
include_once $GLOBALS['THRIFT_ROOT'].'/Thrift.php';

include_once $GLOBALS['THRIFT_ROOT'].'/packages/UserStore/UserStore_types.php';

interface UserStoreIf {
  public function checkVersion($clientName, $edamVersionMajor, $edamVersionMinor);
  public function getBootstrapInfo($locale);
  public function authenticate($username, $password, $consumerKey, $consumerSecret, $supportsTwoFactor);
  public function authenticateLongSession($username, $password, $consumerKey, $consumerSecret, $deviceIdentifier, $deviceDescription, $supportsTwoFactor);
  public function completeTwoFactorAuthentication($authenticationToken, $oneTimeCode, $deviceIdentifier, $deviceDescription);
  public function revokeLongSession($authenticationToken);
  public function authenticateToBusiness($authenticationToken);
  public function refreshAuthentication($authenticationToken);
  public function getUser($authenticationToken);
  public function getPublicUserInfo($username);
  public function getPremiumInfo($authenticationToken);
  public function getNoteStoreUrl($authenticationToken);
}

class UserStoreClient implements \EDAM\UserStore\UserStoreIf {
  protected $input_ = null;
  protected $output_ = null;

  protected $seqid_ = 0;

  public function __construct($input, $output=null) {
    $this->input_ = $input;
    $this->output_ = $output ? $output : $input;
  }

  public function checkVersion($clientName, $edamVersionMajor, $edamVersionMinor)
  {
    $this->send_checkVersion($clientName, $edamVersionMajor, $edamVersionMinor);
    return $this->recv_checkVersion();
  }

  public function send_checkVersion($clientName, $edamVersionMajor, $edamVersionMinor)
  {
    $args = new \EDAM\UserStore\UserStore_checkVersion_args();
    $args->clientName = $clientName;
    $args->edamVersionMajor = $edamVersionMajor;
    $args->edamVersionMinor = $edamVersionMinor;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'checkVersion', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('checkVersion', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_checkVersion()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_checkVersion_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_checkVersion_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    throw new \Exception("checkVersion failed: unknown result");
  }

  public function getBootstrapInfo($locale)
  {
    $this->send_getBootstrapInfo($locale);
    return $this->recv_getBootstrapInfo();
  }

  public function send_getBootstrapInfo($locale)
  {
    $args = new \EDAM\UserStore\UserStore_getBootstrapInfo_args();
    $args->locale = $locale;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'getBootstrapInfo', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('getBootstrapInfo', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_getBootstrapInfo()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_getBootstrapInfo_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_getBootstrapInfo_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    throw new \Exception("getBootstrapInfo failed: unknown result");
  }

  public function authenticate($username, $password, $consumerKey, $consumerSecret, $supportsTwoFactor)
  {
    $this->send_authenticate($username, $password, $consumerKey, $consumerSecret, $supportsTwoFactor);
    return $this->recv_authenticate();
  }

  public function send_authenticate($username, $password, $consumerKey, $consumerSecret, $supportsTwoFactor)
  {
    $args = new \EDAM\UserStore\UserStore_authenticate_args();
    $args->username = $username;
    $args->password = $password;
    $args->consumerKey = $consumerKey;
    $args->consumerSecret = $consumerSecret;
    $args->supportsTwoFactor = $supportsTwoFactor;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'authenticate', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('authenticate', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_authenticate()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_authenticate_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_authenticate_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("authenticate failed: unknown result");
  }

  public function authenticateLongSession($username, $password, $consumerKey, $consumerSecret, $deviceIdentifier, $deviceDescription, $supportsTwoFactor)
  {
    $this->send_authenticateLongSession($username, $password, $consumerKey, $consumerSecret, $deviceIdentifier, $deviceDescription, $supportsTwoFactor);
    return $this->recv_authenticateLongSession();
  }

  public function send_authenticateLongSession($username, $password, $consumerKey, $consumerSecret, $deviceIdentifier, $deviceDescription, $supportsTwoFactor)
  {
    $args = new \EDAM\UserStore\UserStore_authenticateLongSession_args();
    $args->username = $username;
    $args->password = $password;
    $args->consumerKey = $consumerKey;
    $args->consumerSecret = $consumerSecret;
    $args->deviceIdentifier = $deviceIdentifier;
    $args->deviceDescription = $deviceDescription;
    $args->supportsTwoFactor = $supportsTwoFactor;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'authenticateLongSession', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('authenticateLongSession', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_authenticateLongSession()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_authenticateLongSession_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_authenticateLongSession_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("authenticateLongSession failed: unknown result");
  }

  public function completeTwoFactorAuthentication($authenticationToken, $oneTimeCode, $deviceIdentifier, $deviceDescription)
  {
    $this->send_completeTwoFactorAuthentication($authenticationToken, $oneTimeCode, $deviceIdentifier, $deviceDescription);
    return $this->recv_completeTwoFactorAuthentication();
  }

  public function send_completeTwoFactorAuthentication($authenticationToken, $oneTimeCode, $deviceIdentifier, $deviceDescription)
  {
    $args = new \EDAM\UserStore\UserStore_completeTwoFactorAuthentication_args();
    $args->authenticationToken = $authenticationToken;
    $args->oneTimeCode = $oneTimeCode;
    $args->deviceIdentifier = $deviceIdentifier;
    $args->deviceDescription = $deviceDescription;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'completeTwoFactorAuthentication', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('completeTwoFactorAuthentication', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_completeTwoFactorAuthentication()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_completeTwoFactorAuthentication_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_completeTwoFactorAuthentication_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("completeTwoFactorAuthentication failed: unknown result");
  }

  public function revokeLongSession($authenticationToken)
  {
    $this->send_revokeLongSession($authenticationToken);
    $this->recv_revokeLongSession();
  }

  public function send_revokeLongSession($authenticationToken)
  {
    $args = new \EDAM\UserStore\UserStore_revokeLongSession_args();
    $args->authenticationToken = $authenticationToken;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'revokeLongSession', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('revokeLongSession', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_revokeLongSession()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_revokeLongSession_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_revokeLongSession_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    return;
  }

  public function authenticateToBusiness($authenticationToken)
  {
    $this->send_authenticateToBusiness($authenticationToken);
    return $this->recv_authenticateToBusiness();
  }

  public function send_authenticateToBusiness($authenticationToken)
  {
    $args = new \EDAM\UserStore\UserStore_authenticateToBusiness_args();
    $args->authenticationToken = $authenticationToken;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'authenticateToBusiness', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('authenticateToBusiness', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_authenticateToBusiness()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_authenticateToBusiness_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_authenticateToBusiness_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("authenticateToBusiness failed: unknown result");
  }

  public function refreshAuthentication($authenticationToken)
  {
    $this->send_refreshAuthentication($authenticationToken);
    return $this->recv_refreshAuthentication();
  }

  public function send_refreshAuthentication($authenticationToken)
  {
    $args = new \EDAM\UserStore\UserStore_refreshAuthentication_args();
    $args->authenticationToken = $authenticationToken;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'refreshAuthentication', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('refreshAuthentication', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_refreshAuthentication()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_refreshAuthentication_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_refreshAuthentication_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("refreshAuthentication failed: unknown result");
  }

  public function getUser($authenticationToken)
  {
    $this->send_getUser($authenticationToken);
    return $this->recv_getUser();
  }

  public function send_getUser($authenticationToken)
  {
    $args = new \EDAM\UserStore\UserStore_getUser_args();
    $args->authenticationToken = $authenticationToken;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'getUser', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('getUser', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_getUser()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_getUser_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_getUser_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("getUser failed: unknown result");
  }

  public function getPublicUserInfo($username)
  {
    $this->send_getPublicUserInfo($username);
    return $this->recv_getPublicUserInfo();
  }

  public function send_getPublicUserInfo($username)
  {
    $args = new \EDAM\UserStore\UserStore_getPublicUserInfo_args();
    $args->username = $username;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'getPublicUserInfo', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('getPublicUserInfo', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_getPublicUserInfo()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_getPublicUserInfo_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_getPublicUserInfo_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->notFoundException !== null) {
      throw $result->notFoundException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    throw new \Exception("getPublicUserInfo failed: unknown result");
  }

  public function getPremiumInfo($authenticationToken)
  {
    $this->send_getPremiumInfo($authenticationToken);
    return $this->recv_getPremiumInfo();
  }

  public function send_getPremiumInfo($authenticationToken)
  {
    $args = new \EDAM\UserStore\UserStore_getPremiumInfo_args();
    $args->authenticationToken = $authenticationToken;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'getPremiumInfo', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('getPremiumInfo', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_getPremiumInfo()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_getPremiumInfo_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_getPremiumInfo_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("getPremiumInfo failed: unknown result");
  }

  public function getNoteStoreUrl($authenticationToken)
  {
    $this->send_getNoteStoreUrl($authenticationToken);
    return $this->recv_getNoteStoreUrl();
  }

  public function send_getNoteStoreUrl($authenticationToken)
  {
    $args = new \EDAM\UserStore\UserStore_getNoteStoreUrl_args();
    $args->authenticationToken = $authenticationToken;
    $bin_accel = ($this->output_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_write_binary');
    if ($bin_accel)
    {
      thrift_protocol_write_binary($this->output_, 'getNoteStoreUrl', \TMessageType::CALL, $args, $this->seqid_, $this->output_->isStrictWrite());
    }
    else
    {
      $this->output_->writeMessageBegin('getNoteStoreUrl', \TMessageType::CALL, $this->seqid_);
      $args->write($this->output_);
      $this->output_->writeMessageEnd();
      $this->output_->getTransport()->flush();
    }
  }

  public function recv_getNoteStoreUrl()
  {
    $bin_accel = ($this->input_ instanceof \TProtocol::$TBINARYPROTOCOLACCELERATED) && function_exists('thrift_protocol_read_binary');
    if ($bin_accel) $result = thrift_protocol_read_binary($this->input_, '\EDAM\UserStore\UserStore_getNoteStoreUrl_result', $this->input_->isStrictRead());
    else
    {
      $rseqid = 0;
      $fname = null;
      $mtype = 0;

      $this->input_->readMessageBegin($fname, $mtype, $rseqid);
      if ($mtype == \TMessageType::EXCEPTION) {
        $x = new \TApplicationException();
        $x->read($this->input_);
        $this->input_->readMessageEnd();
        throw $x;
      }
      $result = new \EDAM\UserStore\UserStore_getNoteStoreUrl_result();
      $result->read($this->input_);
      $this->input_->readMessageEnd();
    }
    if ($result->success !== null) {
      return $result->success;
    }
    if ($result->userException !== null) {
      throw $result->userException;
    }
    if ($result->systemException !== null) {
      throw $result->systemException;
    }
    throw new \Exception("getNoteStoreUrl failed: unknown result");
  }

}

// HELPER FUNCTIONS AND STRUCTURES

class UserStore_checkVersion_args {
  static $_TSPEC;

  public $clientName = null;
  public $edamVersionMajor = 1;
  public $edamVersionMinor = 25;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'clientName',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'edamVersionMajor',
          'type' => \TType::I16,
          ),
        3 => array(
          'var' => 'edamVersionMinor',
          'type' => \TType::I16,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['clientName'])) {
        $this->clientName = $vals['clientName'];
      }
      if (isset($vals['edamVersionMajor'])) {
        $this->edamVersionMajor = $vals['edamVersionMajor'];
      }
      if (isset($vals['edamVersionMinor'])) {
        $this->edamVersionMinor = $vals['edamVersionMinor'];
      }
    }
  }

  public function getName() {
    return 'UserStore_checkVersion_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->clientName);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I16) {
            $xfer += $input->readI16($this->edamVersionMajor);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I16) {
            $xfer += $input->readI16($this->edamVersionMinor);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_checkVersion_args');
    if ($this->clientName !== null) {
      $xfer += $output->writeFieldBegin('clientName', \TType::STRING, 1);
      $xfer += $output->writeString($this->clientName);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->edamVersionMajor !== null) {
      $xfer += $output->writeFieldBegin('edamVersionMajor', \TType::I16, 2);
      $xfer += $output->writeI16($this->edamVersionMajor);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->edamVersionMinor !== null) {
      $xfer += $output->writeFieldBegin('edamVersionMinor', \TType::I16, 3);
      $xfer += $output->writeI16($this->edamVersionMinor);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_checkVersion_result {
  static $_TSPEC;

  public $success = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
    }
  }

  public function getName() {
    return 'UserStore_checkVersion_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->success);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_checkVersion_result');
    if ($this->success !== null) {
      $xfer += $output->writeFieldBegin('success', \TType::BOOL, 0);
      $xfer += $output->writeBool($this->success);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getBootstrapInfo_args {
  static $_TSPEC;

  public $locale = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'locale',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['locale'])) {
        $this->locale = $vals['locale'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getBootstrapInfo_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->locale);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getBootstrapInfo_args');
    if ($this->locale !== null) {
      $xfer += $output->writeFieldBegin('locale', \TType::STRING, 1);
      $xfer += $output->writeString($this->locale);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getBootstrapInfo_result {
  static $_TSPEC;

  public $success = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\BootstrapInfo',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getBootstrapInfo_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\BootstrapInfo();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getBootstrapInfo_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_authenticate_args {
  static $_TSPEC;

  public $username = null;
  public $password = null;
  public $consumerKey = null;
  public $consumerSecret = null;
  public $supportsTwoFactor = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'username',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'password',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'consumerKey',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'consumerSecret',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'supportsTwoFactor',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['username'])) {
        $this->username = $vals['username'];
      }
      if (isset($vals['password'])) {
        $this->password = $vals['password'];
      }
      if (isset($vals['consumerKey'])) {
        $this->consumerKey = $vals['consumerKey'];
      }
      if (isset($vals['consumerSecret'])) {
        $this->consumerSecret = $vals['consumerSecret'];
      }
      if (isset($vals['supportsTwoFactor'])) {
        $this->supportsTwoFactor = $vals['supportsTwoFactor'];
      }
    }
  }

  public function getName() {
    return 'UserStore_authenticate_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->username);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->password);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->consumerKey);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->consumerSecret);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->supportsTwoFactor);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_authenticate_args');
    if ($this->username !== null) {
      $xfer += $output->writeFieldBegin('username', \TType::STRING, 1);
      $xfer += $output->writeString($this->username);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->password !== null) {
      $xfer += $output->writeFieldBegin('password', \TType::STRING, 2);
      $xfer += $output->writeString($this->password);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->consumerKey !== null) {
      $xfer += $output->writeFieldBegin('consumerKey', \TType::STRING, 3);
      $xfer += $output->writeString($this->consumerKey);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->consumerSecret !== null) {
      $xfer += $output->writeFieldBegin('consumerSecret', \TType::STRING, 4);
      $xfer += $output->writeString($this->consumerSecret);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->supportsTwoFactor !== null) {
      $xfer += $output->writeFieldBegin('supportsTwoFactor', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->supportsTwoFactor);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_authenticate_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\AuthenticationResult',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_authenticate_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\AuthenticationResult();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_authenticate_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_authenticateLongSession_args {
  static $_TSPEC;

  public $username = null;
  public $password = null;
  public $consumerKey = null;
  public $consumerSecret = null;
  public $deviceIdentifier = null;
  public $deviceDescription = null;
  public $supportsTwoFactor = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'username',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'password',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'consumerKey',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'consumerSecret',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'deviceIdentifier',
          'type' => \TType::STRING,
          ),
        6 => array(
          'var' => 'deviceDescription',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'supportsTwoFactor',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['username'])) {
        $this->username = $vals['username'];
      }
      if (isset($vals['password'])) {
        $this->password = $vals['password'];
      }
      if (isset($vals['consumerKey'])) {
        $this->consumerKey = $vals['consumerKey'];
      }
      if (isset($vals['consumerSecret'])) {
        $this->consumerSecret = $vals['consumerSecret'];
      }
      if (isset($vals['deviceIdentifier'])) {
        $this->deviceIdentifier = $vals['deviceIdentifier'];
      }
      if (isset($vals['deviceDescription'])) {
        $this->deviceDescription = $vals['deviceDescription'];
      }
      if (isset($vals['supportsTwoFactor'])) {
        $this->supportsTwoFactor = $vals['supportsTwoFactor'];
      }
    }
  }

  public function getName() {
    return 'UserStore_authenticateLongSession_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->username);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->password);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->consumerKey);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->consumerSecret);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->deviceIdentifier);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->deviceDescription);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->supportsTwoFactor);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_authenticateLongSession_args');
    if ($this->username !== null) {
      $xfer += $output->writeFieldBegin('username', \TType::STRING, 1);
      $xfer += $output->writeString($this->username);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->password !== null) {
      $xfer += $output->writeFieldBegin('password', \TType::STRING, 2);
      $xfer += $output->writeString($this->password);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->consumerKey !== null) {
      $xfer += $output->writeFieldBegin('consumerKey', \TType::STRING, 3);
      $xfer += $output->writeString($this->consumerKey);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->consumerSecret !== null) {
      $xfer += $output->writeFieldBegin('consumerSecret', \TType::STRING, 4);
      $xfer += $output->writeString($this->consumerSecret);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deviceIdentifier !== null) {
      $xfer += $output->writeFieldBegin('deviceIdentifier', \TType::STRING, 5);
      $xfer += $output->writeString($this->deviceIdentifier);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deviceDescription !== null) {
      $xfer += $output->writeFieldBegin('deviceDescription', \TType::STRING, 6);
      $xfer += $output->writeString($this->deviceDescription);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->supportsTwoFactor !== null) {
      $xfer += $output->writeFieldBegin('supportsTwoFactor', \TType::BOOL, 7);
      $xfer += $output->writeBool($this->supportsTwoFactor);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_authenticateLongSession_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\AuthenticationResult',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_authenticateLongSession_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\AuthenticationResult();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_authenticateLongSession_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_completeTwoFactorAuthentication_args {
  static $_TSPEC;

  public $authenticationToken = null;
  public $oneTimeCode = null;
  public $deviceIdentifier = null;
  public $deviceDescription = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'oneTimeCode',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'deviceIdentifier',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'deviceDescription',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
      if (isset($vals['oneTimeCode'])) {
        $this->oneTimeCode = $vals['oneTimeCode'];
      }
      if (isset($vals['deviceIdentifier'])) {
        $this->deviceIdentifier = $vals['deviceIdentifier'];
      }
      if (isset($vals['deviceDescription'])) {
        $this->deviceDescription = $vals['deviceDescription'];
      }
    }
  }

  public function getName() {
    return 'UserStore_completeTwoFactorAuthentication_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->oneTimeCode);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->deviceIdentifier);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->deviceDescription);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_completeTwoFactorAuthentication_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->oneTimeCode !== null) {
      $xfer += $output->writeFieldBegin('oneTimeCode', \TType::STRING, 2);
      $xfer += $output->writeString($this->oneTimeCode);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deviceIdentifier !== null) {
      $xfer += $output->writeFieldBegin('deviceIdentifier', \TType::STRING, 3);
      $xfer += $output->writeString($this->deviceIdentifier);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deviceDescription !== null) {
      $xfer += $output->writeFieldBegin('deviceDescription', \TType::STRING, 4);
      $xfer += $output->writeString($this->deviceDescription);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_completeTwoFactorAuthentication_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\AuthenticationResult',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_completeTwoFactorAuthentication_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\AuthenticationResult();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_completeTwoFactorAuthentication_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_revokeLongSession_args {
  static $_TSPEC;

  public $authenticationToken = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
    }
  }

  public function getName() {
    return 'UserStore_revokeLongSession_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_revokeLongSession_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_revokeLongSession_result {
  static $_TSPEC;

  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_revokeLongSession_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_revokeLongSession_result');
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_authenticateToBusiness_args {
  static $_TSPEC;

  public $authenticationToken = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
    }
  }

  public function getName() {
    return 'UserStore_authenticateToBusiness_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_authenticateToBusiness_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_authenticateToBusiness_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\AuthenticationResult',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_authenticateToBusiness_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\AuthenticationResult();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_authenticateToBusiness_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_refreshAuthentication_args {
  static $_TSPEC;

  public $authenticationToken = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
    }
  }

  public function getName() {
    return 'UserStore_refreshAuthentication_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_refreshAuthentication_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_refreshAuthentication_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\AuthenticationResult',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_refreshAuthentication_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\AuthenticationResult();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_refreshAuthentication_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getUser_args {
  static $_TSPEC;

  public $authenticationToken = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getUser_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getUser_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getUser_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\User',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getUser_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\Types\User();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getUser_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getPublicUserInfo_args {
  static $_TSPEC;

  public $username = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'username',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['username'])) {
        $this->username = $vals['username'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getPublicUserInfo_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->username);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getPublicUserInfo_args');
    if ($this->username !== null) {
      $xfer += $output->writeFieldBegin('username', \TType::STRING, 1);
      $xfer += $output->writeString($this->username);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getPublicUserInfo_result {
  static $_TSPEC;

  public $success = null;
  public $notFoundException = null;
  public $systemException = null;
  public $userException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\UserStore\PublicUserInfo',
          ),
        1 => array(
          'var' => 'notFoundException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMNotFoundException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        3 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['notFoundException'])) {
        $this->notFoundException = $vals['notFoundException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getPublicUserInfo_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\UserStore\PublicUserInfo();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->notFoundException = new \EDAM\Error\EDAMNotFoundException();
            $xfer += $this->notFoundException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getPublicUserInfo_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notFoundException !== null) {
      $xfer += $output->writeFieldBegin('notFoundException', \TType::STRUCT, 1);
      $xfer += $this->notFoundException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 3);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getPremiumInfo_args {
  static $_TSPEC;

  public $authenticationToken = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getPremiumInfo_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getPremiumInfo_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getPremiumInfo_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\PremiumInfo',
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getPremiumInfo_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRUCT) {
            $this->success = new \EDAM\Types\PremiumInfo();
            $xfer += $this->success->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getPremiumInfo_result');
    if ($this->success !== null) {
      if (!is_object($this->success)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('success', \TType::STRUCT, 0);
      $xfer += $this->success->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getNoteStoreUrl_args {
  static $_TSPEC;

  public $authenticationToken = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'authenticationToken',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['authenticationToken'])) {
        $this->authenticationToken = $vals['authenticationToken'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getNoteStoreUrl_args';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->authenticationToken);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getNoteStoreUrl_args');
    if ($this->authenticationToken !== null) {
      $xfer += $output->writeFieldBegin('authenticationToken', \TType::STRING, 1);
      $xfer += $output->writeString($this->authenticationToken);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class UserStore_getNoteStoreUrl_result {
  static $_TSPEC;

  public $success = null;
  public $userException = null;
  public $systemException = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        0 => array(
          'var' => 'success',
          'type' => \TType::STRING,
          ),
        1 => array(
          'var' => 'userException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMUserException',
          ),
        2 => array(
          'var' => 'systemException',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Error\EDAMSystemException',
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['success'])) {
        $this->success = $vals['success'];
      }
      if (isset($vals['userException'])) {
        $this->userException = $vals['userException'];
      }
      if (isset($vals['systemException'])) {
        $this->systemException = $vals['systemException'];
      }
    }
  }

  public function getName() {
    return 'UserStore_getNoteStoreUrl_result';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 0:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->success);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 1:
          if ($ftype == \TType::STRUCT) {
            $this->userException = new \EDAM\Error\EDAMUserException();
            $xfer += $this->userException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->systemException = new \EDAM\Error\EDAMSystemException();
            $xfer += $this->systemException->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('UserStore_getNoteStoreUrl_result');
    if ($this->success !== null) {
      $xfer += $output->writeFieldBegin('success', \TType::STRING, 0);
      $xfer += $output->writeString($this->success);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->userException !== null) {
      $xfer += $output->writeFieldBegin('userException', \TType::STRUCT, 1);
      $xfer += $this->userException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->systemException !== null) {
      $xfer += $output->writeFieldBegin('systemException', \TType::STRUCT, 2);
      $xfer += $this->systemException->write($output);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

?>
