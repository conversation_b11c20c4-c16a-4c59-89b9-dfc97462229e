<?php
namespace EDAM\Error;
/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
include_once $GLOBALS['THRIFT_ROOT'].'/Thrift.php';


$GLOBALS['\EDAM\Error\E_EDAMErrorCode'] = array(
  'UNKNOWN' => 1,
  'BAD_DATA_FORMAT' => 2,
  'PERMISSION_DENIED' => 3,
  'INTERNAL_ERROR' => 4,
  'DATA_REQUIRED' => 5,
  'LIMIT_REACHED' => 6,
  'QUOTA_REACHED' => 7,
  'INVALID_AUTH' => 8,
  'AUTH_EXPIRED' => 9,
  'DATA_CONFLICT' => 10,
  'ENML_VALIDATION' => 11,
  'SHARD_UNAVAILABLE' => 12,
  'LEN_TOO_SHORT' => 13,
  'LEN_TOO_LONG' => 14,
  'TOO_FEW' => 15,
  'TOO_MANY' => 16,
  'UNSUPPORTED_OPERATION' => 17,
  'TAKEN_DOWN' => 18,
  'RATE_LIMIT_REACHED' => 19,
);

final class EDAMErrorCode {
  const UNKNOWN = 1;
  const BAD_DATA_FORMAT = 2;
  const PERMISSION_DENIED = 3;
  const INTERNAL_ERROR = 4;
  const DATA_REQUIRED = 5;
  const LIMIT_REACHED = 6;
  const QUOTA_REACHED = 7;
  const INVALID_AUTH = 8;
  const AUTH_EXPIRED = 9;
  const DATA_CONFLICT = 10;
  const ENML_VALIDATION = 11;
  const SHARD_UNAVAILABLE = 12;
  const LEN_TOO_SHORT = 13;
  const LEN_TOO_LONG = 14;
  const TOO_FEW = 15;
  const TOO_MANY = 16;
  const UNSUPPORTED_OPERATION = 17;
  const TAKEN_DOWN = 18;
  const RATE_LIMIT_REACHED = 19;
  static public $__names = array(
    1 => 'UNKNOWN',
    2 => 'BAD_DATA_FORMAT',
    3 => 'PERMISSION_DENIED',
    4 => 'INTERNAL_ERROR',
    5 => 'DATA_REQUIRED',
    6 => 'LIMIT_REACHED',
    7 => 'QUOTA_REACHED',
    8 => 'INVALID_AUTH',
    9 => 'AUTH_EXPIRED',
    10 => 'DATA_CONFLICT',
    11 => 'ENML_VALIDATION',
    12 => 'SHARD_UNAVAILABLE',
    13 => 'LEN_TOO_SHORT',
    14 => 'LEN_TOO_LONG',
    15 => 'TOO_FEW',
    16 => 'TOO_MANY',
    17 => 'UNSUPPORTED_OPERATION',
    18 => 'TAKEN_DOWN',
    19 => 'RATE_LIMIT_REACHED',
  );
}

class EDAMUserException extends \TException {
  static $_TSPEC;

  public $errorCode = null;
  public $parameter = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'errorCode',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'parameter',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['errorCode'])) {
        $this->errorCode = $vals['errorCode'];
      }
      if (isset($vals['parameter'])) {
        $this->parameter = $vals['parameter'];
      }
    }
  }

  public function getName() {
    return 'EDAMUserException';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->errorCode);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->parameter);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('EDAMUserException');
    if ($this->errorCode !== null) {
      $xfer += $output->writeFieldBegin('errorCode', \TType::I32, 1);
      $xfer += $output->writeI32($this->errorCode);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->parameter !== null) {
      $xfer += $output->writeFieldBegin('parameter', \TType::STRING, 2);
      $xfer += $output->writeString($this->parameter);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class EDAMSystemException extends \TException {
  static $_TSPEC;

  public $errorCode = null;
  public $message = null;
  public $rateLimitDuration = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'errorCode',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'message',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'rateLimitDuration',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['errorCode'])) {
        $this->errorCode = $vals['errorCode'];
      }
      if (isset($vals['message'])) {
        $this->message = $vals['message'];
      }
      if (isset($vals['rateLimitDuration'])) {
        $this->rateLimitDuration = $vals['rateLimitDuration'];
      }
    }
  }

  public function getName() {
    return 'EDAMSystemException';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->errorCode);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->message);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->rateLimitDuration);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('EDAMSystemException');
    if ($this->errorCode !== null) {
      $xfer += $output->writeFieldBegin('errorCode', \TType::I32, 1);
      $xfer += $output->writeI32($this->errorCode);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->message !== null) {
      $xfer += $output->writeFieldBegin('message', \TType::STRING, 2);
      $xfer += $output->writeString($this->message);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->rateLimitDuration !== null) {
      $xfer += $output->writeFieldBegin('rateLimitDuration', \TType::I32, 3);
      $xfer += $output->writeI32($this->rateLimitDuration);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class EDAMNotFoundException extends \TException {
  static $_TSPEC;

  public $identifier = null;
  public $key = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'identifier',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'key',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['identifier'])) {
        $this->identifier = $vals['identifier'];
      }
      if (isset($vals['key'])) {
        $this->key = $vals['key'];
      }
    }
  }

  public function getName() {
    return 'EDAMNotFoundException';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->identifier);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->key);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('EDAMNotFoundException');
    if ($this->identifier !== null) {
      $xfer += $output->writeFieldBegin('identifier', \TType::STRING, 1);
      $xfer += $output->writeString($this->identifier);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->key !== null) {
      $xfer += $output->writeFieldBegin('key', \TType::STRING, 2);
      $xfer += $output->writeString($this->key);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

?>
