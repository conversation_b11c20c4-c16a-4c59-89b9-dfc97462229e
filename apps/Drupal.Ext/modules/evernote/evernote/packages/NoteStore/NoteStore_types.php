<?php
namespace EDAM\NoteStore;
/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
include_once $GLOBALS['THRIFT_ROOT'].'/Thrift.php';

include_once $GLOBALS['THRIFT_ROOT'].'/packages/UserStore/UserStore_types.php';
include_once $GLOBALS['THRIFT_ROOT'].'/packages/Types/Types_types.php';
include_once $GLOBALS['THRIFT_ROOT'].'/packages/Errors/Errors_types.php';
include_once $GLOBALS['THRIFT_ROOT'].'/packages/Limits/Limits_types.php';

class SyncState {
  static $_TSPEC;

  public $currentTime = null;
  public $fullSyncBefore = null;
  public $updateCount = null;
  public $uploaded = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'currentTime',
          'type' => \TType::I64,
          ),
        2 => array(
          'var' => 'fullSyncBefore',
          'type' => \TType::I64,
          ),
        3 => array(
          'var' => 'updateCount',
          'type' => \TType::I32,
          ),
        4 => array(
          'var' => 'uploaded',
          'type' => \TType::I64,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['currentTime'])) {
        $this->currentTime = $vals['currentTime'];
      }
      if (isset($vals['fullSyncBefore'])) {
        $this->fullSyncBefore = $vals['fullSyncBefore'];
      }
      if (isset($vals['updateCount'])) {
        $this->updateCount = $vals['updateCount'];
      }
      if (isset($vals['uploaded'])) {
        $this->uploaded = $vals['uploaded'];
      }
    }
  }

  public function getName() {
    return 'SyncState';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->currentTime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->fullSyncBefore);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->uploaded);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SyncState');
    if ($this->currentTime !== null) {
      $xfer += $output->writeFieldBegin('currentTime', \TType::I64, 1);
      $xfer += $output->writeI64($this->currentTime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->fullSyncBefore !== null) {
      $xfer += $output->writeFieldBegin('fullSyncBefore', \TType::I64, 2);
      $xfer += $output->writeI64($this->fullSyncBefore);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateCount !== null) {
      $xfer += $output->writeFieldBegin('updateCount', \TType::I32, 3);
      $xfer += $output->writeI32($this->updateCount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->uploaded !== null) {
      $xfer += $output->writeFieldBegin('uploaded', \TType::I64, 4);
      $xfer += $output->writeI64($this->uploaded);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class SyncChunk {
  static $_TSPEC;

  public $currentTime = null;
  public $chunkHighUSN = null;
  public $updateCount = null;
  public $notes = null;
  public $notebooks = null;
  public $tags = null;
  public $searches = null;
  public $resources = null;
  public $expungedNotes = null;
  public $expungedNotebooks = null;
  public $expungedTags = null;
  public $expungedSearches = null;
  public $linkedNotebooks = null;
  public $expungedLinkedNotebooks = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'currentTime',
          'type' => \TType::I64,
          ),
        2 => array(
          'var' => 'chunkHighUSN',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'updateCount',
          'type' => \TType::I32,
          ),
        4 => array(
          'var' => 'notes',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Note',
            ),
          ),
        5 => array(
          'var' => 'notebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Notebook',
            ),
          ),
        6 => array(
          'var' => 'tags',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Tag',
            ),
          ),
        7 => array(
          'var' => 'searches',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\SavedSearch',
            ),
          ),
        8 => array(
          'var' => 'resources',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Resource',
            ),
          ),
        9 => array(
          'var' => 'expungedNotes',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        10 => array(
          'var' => 'expungedNotebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        11 => array(
          'var' => 'expungedTags',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        12 => array(
          'var' => 'expungedSearches',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        13 => array(
          'var' => 'linkedNotebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\LinkedNotebook',
            ),
          ),
        14 => array(
          'var' => 'expungedLinkedNotebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['currentTime'])) {
        $this->currentTime = $vals['currentTime'];
      }
      if (isset($vals['chunkHighUSN'])) {
        $this->chunkHighUSN = $vals['chunkHighUSN'];
      }
      if (isset($vals['updateCount'])) {
        $this->updateCount = $vals['updateCount'];
      }
      if (isset($vals['notes'])) {
        $this->notes = $vals['notes'];
      }
      if (isset($vals['notebooks'])) {
        $this->notebooks = $vals['notebooks'];
      }
      if (isset($vals['tags'])) {
        $this->tags = $vals['tags'];
      }
      if (isset($vals['searches'])) {
        $this->searches = $vals['searches'];
      }
      if (isset($vals['resources'])) {
        $this->resources = $vals['resources'];
      }
      if (isset($vals['expungedNotes'])) {
        $this->expungedNotes = $vals['expungedNotes'];
      }
      if (isset($vals['expungedNotebooks'])) {
        $this->expungedNotebooks = $vals['expungedNotebooks'];
      }
      if (isset($vals['expungedTags'])) {
        $this->expungedTags = $vals['expungedTags'];
      }
      if (isset($vals['expungedSearches'])) {
        $this->expungedSearches = $vals['expungedSearches'];
      }
      if (isset($vals['linkedNotebooks'])) {
        $this->linkedNotebooks = $vals['linkedNotebooks'];
      }
      if (isset($vals['expungedLinkedNotebooks'])) {
        $this->expungedLinkedNotebooks = $vals['expungedLinkedNotebooks'];
      }
    }
  }

  public function getName() {
    return 'SyncChunk';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->currentTime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->chunkHighUSN);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::LST) {
            $this->notes = array();
            $_size0 = 0;
            $_etype3 = 0;
            $xfer += $input->readListBegin($_etype3, $_size0);
            for ($_i4 = 0; $_i4 < $_size0; ++$_i4)
            {
              $elem5 = null;
              $elem5 = new \EDAM\Types\Note();
              $xfer += $elem5->read($input);
              $this->notes []= $elem5;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::LST) {
            $this->notebooks = array();
            $_size6 = 0;
            $_etype9 = 0;
            $xfer += $input->readListBegin($_etype9, $_size6);
            for ($_i10 = 0; $_i10 < $_size6; ++$_i10)
            {
              $elem11 = null;
              $elem11 = new \EDAM\Types\Notebook();
              $xfer += $elem11->read($input);
              $this->notebooks []= $elem11;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::LST) {
            $this->tags = array();
            $_size12 = 0;
            $_etype15 = 0;
            $xfer += $input->readListBegin($_etype15, $_size12);
            for ($_i16 = 0; $_i16 < $_size12; ++$_i16)
            {
              $elem17 = null;
              $elem17 = new \EDAM\Types\Tag();
              $xfer += $elem17->read($input);
              $this->tags []= $elem17;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::LST) {
            $this->searches = array();
            $_size18 = 0;
            $_etype21 = 0;
            $xfer += $input->readListBegin($_etype21, $_size18);
            for ($_i22 = 0; $_i22 < $_size18; ++$_i22)
            {
              $elem23 = null;
              $elem23 = new \EDAM\Types\SavedSearch();
              $xfer += $elem23->read($input);
              $this->searches []= $elem23;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::LST) {
            $this->resources = array();
            $_size24 = 0;
            $_etype27 = 0;
            $xfer += $input->readListBegin($_etype27, $_size24);
            for ($_i28 = 0; $_i28 < $_size24; ++$_i28)
            {
              $elem29 = null;
              $elem29 = new \EDAM\Types\Resource();
              $xfer += $elem29->read($input);
              $this->resources []= $elem29;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::LST) {
            $this->expungedNotes = array();
            $_size30 = 0;
            $_etype33 = 0;
            $xfer += $input->readListBegin($_etype33, $_size30);
            for ($_i34 = 0; $_i34 < $_size30; ++$_i34)
            {
              $elem35 = null;
              $xfer += $input->readString($elem35);
              $this->expungedNotes []= $elem35;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::LST) {
            $this->expungedNotebooks = array();
            $_size36 = 0;
            $_etype39 = 0;
            $xfer += $input->readListBegin($_etype39, $_size36);
            for ($_i40 = 0; $_i40 < $_size36; ++$_i40)
            {
              $elem41 = null;
              $xfer += $input->readString($elem41);
              $this->expungedNotebooks []= $elem41;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::LST) {
            $this->expungedTags = array();
            $_size42 = 0;
            $_etype45 = 0;
            $xfer += $input->readListBegin($_etype45, $_size42);
            for ($_i46 = 0; $_i46 < $_size42; ++$_i46)
            {
              $elem47 = null;
              $xfer += $input->readString($elem47);
              $this->expungedTags []= $elem47;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::LST) {
            $this->expungedSearches = array();
            $_size48 = 0;
            $_etype51 = 0;
            $xfer += $input->readListBegin($_etype51, $_size48);
            for ($_i52 = 0; $_i52 < $_size48; ++$_i52)
            {
              $elem53 = null;
              $xfer += $input->readString($elem53);
              $this->expungedSearches []= $elem53;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::LST) {
            $this->linkedNotebooks = array();
            $_size54 = 0;
            $_etype57 = 0;
            $xfer += $input->readListBegin($_etype57, $_size54);
            for ($_i58 = 0; $_i58 < $_size54; ++$_i58)
            {
              $elem59 = null;
              $elem59 = new \EDAM\Types\LinkedNotebook();
              $xfer += $elem59->read($input);
              $this->linkedNotebooks []= $elem59;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::LST) {
            $this->expungedLinkedNotebooks = array();
            $_size60 = 0;
            $_etype63 = 0;
            $xfer += $input->readListBegin($_etype63, $_size60);
            for ($_i64 = 0; $_i64 < $_size60; ++$_i64)
            {
              $elem65 = null;
              $xfer += $input->readString($elem65);
              $this->expungedLinkedNotebooks []= $elem65;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SyncChunk');
    if ($this->currentTime !== null) {
      $xfer += $output->writeFieldBegin('currentTime', \TType::I64, 1);
      $xfer += $output->writeI64($this->currentTime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->chunkHighUSN !== null) {
      $xfer += $output->writeFieldBegin('chunkHighUSN', \TType::I32, 2);
      $xfer += $output->writeI32($this->chunkHighUSN);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateCount !== null) {
      $xfer += $output->writeFieldBegin('updateCount', \TType::I32, 3);
      $xfer += $output->writeI32($this->updateCount);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notes !== null) {
      if (!is_array($this->notes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notes', \TType::LST, 4);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->notes));
        {
          foreach ($this->notes as $iter66)
          {
            $xfer += $iter66->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebooks !== null) {
      if (!is_array($this->notebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notebooks', \TType::LST, 5);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->notebooks));
        {
          foreach ($this->notebooks as $iter67)
          {
            $xfer += $iter67->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tags !== null) {
      if (!is_array($this->tags)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tags', \TType::LST, 6);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->tags));
        {
          foreach ($this->tags as $iter68)
          {
            $xfer += $iter68->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->searches !== null) {
      if (!is_array($this->searches)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('searches', \TType::LST, 7);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->searches));
        {
          foreach ($this->searches as $iter69)
          {
            $xfer += $iter69->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->resources !== null) {
      if (!is_array($this->resources)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('resources', \TType::LST, 8);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->resources));
        {
          foreach ($this->resources as $iter70)
          {
            $xfer += $iter70->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->expungedNotes !== null) {
      if (!is_array($this->expungedNotes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('expungedNotes', \TType::LST, 9);
      {
        $output->writeListBegin(\TType::STRING, count($this->expungedNotes));
        {
          foreach ($this->expungedNotes as $iter71)
          {
            $xfer += $output->writeString($iter71);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->expungedNotebooks !== null) {
      if (!is_array($this->expungedNotebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('expungedNotebooks', \TType::LST, 10);
      {
        $output->writeListBegin(\TType::STRING, count($this->expungedNotebooks));
        {
          foreach ($this->expungedNotebooks as $iter72)
          {
            $xfer += $output->writeString($iter72);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->expungedTags !== null) {
      if (!is_array($this->expungedTags)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('expungedTags', \TType::LST, 11);
      {
        $output->writeListBegin(\TType::STRING, count($this->expungedTags));
        {
          foreach ($this->expungedTags as $iter73)
          {
            $xfer += $output->writeString($iter73);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->expungedSearches !== null) {
      if (!is_array($this->expungedSearches)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('expungedSearches', \TType::LST, 12);
      {
        $output->writeListBegin(\TType::STRING, count($this->expungedSearches));
        {
          foreach ($this->expungedSearches as $iter74)
          {
            $xfer += $output->writeString($iter74);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->linkedNotebooks !== null) {
      if (!is_array($this->linkedNotebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('linkedNotebooks', \TType::LST, 13);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->linkedNotebooks));
        {
          foreach ($this->linkedNotebooks as $iter75)
          {
            $xfer += $iter75->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->expungedLinkedNotebooks !== null) {
      if (!is_array($this->expungedLinkedNotebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('expungedLinkedNotebooks', \TType::LST, 14);
      {
        $output->writeListBegin(\TType::STRING, count($this->expungedLinkedNotebooks));
        {
          foreach ($this->expungedLinkedNotebooks as $iter76)
          {
            $xfer += $output->writeString($iter76);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class SyncChunkFilter {
  static $_TSPEC;

  public $includeNotes = null;
  public $includeNoteResources = null;
  public $includeNoteAttributes = null;
  public $includeNotebooks = null;
  public $includeTags = null;
  public $includeSearches = null;
  public $includeResources = null;
  public $includeLinkedNotebooks = null;
  public $includeExpunged = null;
  public $includeNoteApplicationDataFullMap = null;
  public $includeResourceApplicationDataFullMap = null;
  public $includeNoteResourceApplicationDataFullMap = null;
  public $requireNoteContentClass = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'includeNotes',
          'type' => \TType::BOOL,
          ),
        2 => array(
          'var' => 'includeNoteResources',
          'type' => \TType::BOOL,
          ),
        3 => array(
          'var' => 'includeNoteAttributes',
          'type' => \TType::BOOL,
          ),
        4 => array(
          'var' => 'includeNotebooks',
          'type' => \TType::BOOL,
          ),
        5 => array(
          'var' => 'includeTags',
          'type' => \TType::BOOL,
          ),
        6 => array(
          'var' => 'includeSearches',
          'type' => \TType::BOOL,
          ),
        7 => array(
          'var' => 'includeResources',
          'type' => \TType::BOOL,
          ),
        8 => array(
          'var' => 'includeLinkedNotebooks',
          'type' => \TType::BOOL,
          ),
        9 => array(
          'var' => 'includeExpunged',
          'type' => \TType::BOOL,
          ),
        10 => array(
          'var' => 'includeNoteApplicationDataFullMap',
          'type' => \TType::BOOL,
          ),
        12 => array(
          'var' => 'includeResourceApplicationDataFullMap',
          'type' => \TType::BOOL,
          ),
        13 => array(
          'var' => 'includeNoteResourceApplicationDataFullMap',
          'type' => \TType::BOOL,
          ),
        11 => array(
          'var' => 'requireNoteContentClass',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['includeNotes'])) {
        $this->includeNotes = $vals['includeNotes'];
      }
      if (isset($vals['includeNoteResources'])) {
        $this->includeNoteResources = $vals['includeNoteResources'];
      }
      if (isset($vals['includeNoteAttributes'])) {
        $this->includeNoteAttributes = $vals['includeNoteAttributes'];
      }
      if (isset($vals['includeNotebooks'])) {
        $this->includeNotebooks = $vals['includeNotebooks'];
      }
      if (isset($vals['includeTags'])) {
        $this->includeTags = $vals['includeTags'];
      }
      if (isset($vals['includeSearches'])) {
        $this->includeSearches = $vals['includeSearches'];
      }
      if (isset($vals['includeResources'])) {
        $this->includeResources = $vals['includeResources'];
      }
      if (isset($vals['includeLinkedNotebooks'])) {
        $this->includeLinkedNotebooks = $vals['includeLinkedNotebooks'];
      }
      if (isset($vals['includeExpunged'])) {
        $this->includeExpunged = $vals['includeExpunged'];
      }
      if (isset($vals['includeNoteApplicationDataFullMap'])) {
        $this->includeNoteApplicationDataFullMap = $vals['includeNoteApplicationDataFullMap'];
      }
      if (isset($vals['includeResourceApplicationDataFullMap'])) {
        $this->includeResourceApplicationDataFullMap = $vals['includeResourceApplicationDataFullMap'];
      }
      if (isset($vals['includeNoteResourceApplicationDataFullMap'])) {
        $this->includeNoteResourceApplicationDataFullMap = $vals['includeNoteResourceApplicationDataFullMap'];
      }
      if (isset($vals['requireNoteContentClass'])) {
        $this->requireNoteContentClass = $vals['requireNoteContentClass'];
      }
    }
  }

  public function getName() {
    return 'SyncChunkFilter';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNoteResources);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNoteAttributes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeTags);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeSearches);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeResources);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeLinkedNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 9:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeExpunged);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNoteApplicationDataFullMap);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeResourceApplicationDataFullMap);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 13:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNoteResourceApplicationDataFullMap);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->requireNoteContentClass);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('SyncChunkFilter');
    if ($this->includeNotes !== null) {
      $xfer += $output->writeFieldBegin('includeNotes', \TType::BOOL, 1);
      $xfer += $output->writeBool($this->includeNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeNoteResources !== null) {
      $xfer += $output->writeFieldBegin('includeNoteResources', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->includeNoteResources);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeNoteAttributes !== null) {
      $xfer += $output->writeFieldBegin('includeNoteAttributes', \TType::BOOL, 3);
      $xfer += $output->writeBool($this->includeNoteAttributes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeNotebooks !== null) {
      $xfer += $output->writeFieldBegin('includeNotebooks', \TType::BOOL, 4);
      $xfer += $output->writeBool($this->includeNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeTags !== null) {
      $xfer += $output->writeFieldBegin('includeTags', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->includeTags);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeSearches !== null) {
      $xfer += $output->writeFieldBegin('includeSearches', \TType::BOOL, 6);
      $xfer += $output->writeBool($this->includeSearches);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeResources !== null) {
      $xfer += $output->writeFieldBegin('includeResources', \TType::BOOL, 7);
      $xfer += $output->writeBool($this->includeResources);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeLinkedNotebooks !== null) {
      $xfer += $output->writeFieldBegin('includeLinkedNotebooks', \TType::BOOL, 8);
      $xfer += $output->writeBool($this->includeLinkedNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeExpunged !== null) {
      $xfer += $output->writeFieldBegin('includeExpunged', \TType::BOOL, 9);
      $xfer += $output->writeBool($this->includeExpunged);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeNoteApplicationDataFullMap !== null) {
      $xfer += $output->writeFieldBegin('includeNoteApplicationDataFullMap', \TType::BOOL, 10);
      $xfer += $output->writeBool($this->includeNoteApplicationDataFullMap);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->requireNoteContentClass !== null) {
      $xfer += $output->writeFieldBegin('requireNoteContentClass', \TType::STRING, 11);
      $xfer += $output->writeString($this->requireNoteContentClass);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeResourceApplicationDataFullMap !== null) {
      $xfer += $output->writeFieldBegin('includeResourceApplicationDataFullMap', \TType::BOOL, 12);
      $xfer += $output->writeBool($this->includeResourceApplicationDataFullMap);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeNoteResourceApplicationDataFullMap !== null) {
      $xfer += $output->writeFieldBegin('includeNoteResourceApplicationDataFullMap', \TType::BOOL, 13);
      $xfer += $output->writeBool($this->includeNoteResourceApplicationDataFullMap);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteFilter {
  static $_TSPEC;

  public $order = null;
  public $ascending = null;
  public $words = null;
  public $notebookGuid = null;
  public $tagGuids = null;
  public $timeZone = null;
  public $inactive = null;
  public $emphasized = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'order',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'ascending',
          'type' => \TType::BOOL,
          ),
        3 => array(
          'var' => 'words',
          'type' => \TType::STRING,
          ),
        4 => array(
          'var' => 'notebookGuid',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'tagGuids',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        6 => array(
          'var' => 'timeZone',
          'type' => \TType::STRING,
          ),
        7 => array(
          'var' => 'inactive',
          'type' => \TType::BOOL,
          ),
        8 => array(
          'var' => 'emphasized',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['order'])) {
        $this->order = $vals['order'];
      }
      if (isset($vals['ascending'])) {
        $this->ascending = $vals['ascending'];
      }
      if (isset($vals['words'])) {
        $this->words = $vals['words'];
      }
      if (isset($vals['notebookGuid'])) {
        $this->notebookGuid = $vals['notebookGuid'];
      }
      if (isset($vals['tagGuids'])) {
        $this->tagGuids = $vals['tagGuids'];
      }
      if (isset($vals['timeZone'])) {
        $this->timeZone = $vals['timeZone'];
      }
      if (isset($vals['inactive'])) {
        $this->inactive = $vals['inactive'];
      }
      if (isset($vals['emphasized'])) {
        $this->emphasized = $vals['emphasized'];
      }
    }
  }

  public function getName() {
    return 'NoteFilter';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->order);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->ascending);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->words);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->notebookGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::LST) {
            $this->tagGuids = array();
            $_size77 = 0;
            $_etype80 = 0;
            $xfer += $input->readListBegin($_etype80, $_size77);
            for ($_i81 = 0; $_i81 < $_size77; ++$_i81)
            {
              $elem82 = null;
              $xfer += $input->readString($elem82);
              $this->tagGuids []= $elem82;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->timeZone);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->inactive);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->emphasized);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteFilter');
    if ($this->order !== null) {
      $xfer += $output->writeFieldBegin('order', \TType::I32, 1);
      $xfer += $output->writeI32($this->order);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->ascending !== null) {
      $xfer += $output->writeFieldBegin('ascending', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->ascending);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->words !== null) {
      $xfer += $output->writeFieldBegin('words', \TType::STRING, 3);
      $xfer += $output->writeString($this->words);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebookGuid !== null) {
      $xfer += $output->writeFieldBegin('notebookGuid', \TType::STRING, 4);
      $xfer += $output->writeString($this->notebookGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tagGuids !== null) {
      if (!is_array($this->tagGuids)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tagGuids', \TType::LST, 5);
      {
        $output->writeListBegin(\TType::STRING, count($this->tagGuids));
        {
          foreach ($this->tagGuids as $iter83)
          {
            $xfer += $output->writeString($iter83);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->timeZone !== null) {
      $xfer += $output->writeFieldBegin('timeZone', \TType::STRING, 6);
      $xfer += $output->writeString($this->timeZone);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->inactive !== null) {
      $xfer += $output->writeFieldBegin('inactive', \TType::BOOL, 7);
      $xfer += $output->writeBool($this->inactive);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->emphasized !== null) {
      $xfer += $output->writeFieldBegin('emphasized', \TType::STRING, 8);
      $xfer += $output->writeString($this->emphasized);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteList {
  static $_TSPEC;

  public $startIndex = null;
  public $totalNotes = null;
  public $notes = null;
  public $stoppedWords = null;
  public $searchedWords = null;
  public $updateCount = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'startIndex',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'totalNotes',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'notes',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Note',
            ),
          ),
        4 => array(
          'var' => 'stoppedWords',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        5 => array(
          'var' => 'searchedWords',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        6 => array(
          'var' => 'updateCount',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['startIndex'])) {
        $this->startIndex = $vals['startIndex'];
      }
      if (isset($vals['totalNotes'])) {
        $this->totalNotes = $vals['totalNotes'];
      }
      if (isset($vals['notes'])) {
        $this->notes = $vals['notes'];
      }
      if (isset($vals['stoppedWords'])) {
        $this->stoppedWords = $vals['stoppedWords'];
      }
      if (isset($vals['searchedWords'])) {
        $this->searchedWords = $vals['searchedWords'];
      }
      if (isset($vals['updateCount'])) {
        $this->updateCount = $vals['updateCount'];
      }
    }
  }

  public function getName() {
    return 'NoteList';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->startIndex);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->totalNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::LST) {
            $this->notes = array();
            $_size84 = 0;
            $_etype87 = 0;
            $xfer += $input->readListBegin($_etype87, $_size84);
            for ($_i88 = 0; $_i88 < $_size84; ++$_i88)
            {
              $elem89 = null;
              $elem89 = new \EDAM\Types\Note();
              $xfer += $elem89->read($input);
              $this->notes []= $elem89;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::LST) {
            $this->stoppedWords = array();
            $_size90 = 0;
            $_etype93 = 0;
            $xfer += $input->readListBegin($_etype93, $_size90);
            for ($_i94 = 0; $_i94 < $_size90; ++$_i94)
            {
              $elem95 = null;
              $xfer += $input->readString($elem95);
              $this->stoppedWords []= $elem95;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::LST) {
            $this->searchedWords = array();
            $_size96 = 0;
            $_etype99 = 0;
            $xfer += $input->readListBegin($_etype99, $_size96);
            for ($_i100 = 0; $_i100 < $_size96; ++$_i100)
            {
              $elem101 = null;
              $xfer += $input->readString($elem101);
              $this->searchedWords []= $elem101;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteList');
    if ($this->startIndex !== null) {
      $xfer += $output->writeFieldBegin('startIndex', \TType::I32, 1);
      $xfer += $output->writeI32($this->startIndex);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->totalNotes !== null) {
      $xfer += $output->writeFieldBegin('totalNotes', \TType::I32, 2);
      $xfer += $output->writeI32($this->totalNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notes !== null) {
      if (!is_array($this->notes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notes', \TType::LST, 3);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->notes));
        {
          foreach ($this->notes as $iter102)
          {
            $xfer += $iter102->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->stoppedWords !== null) {
      if (!is_array($this->stoppedWords)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('stoppedWords', \TType::LST, 4);
      {
        $output->writeListBegin(\TType::STRING, count($this->stoppedWords));
        {
          foreach ($this->stoppedWords as $iter103)
          {
            $xfer += $output->writeString($iter103);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->searchedWords !== null) {
      if (!is_array($this->searchedWords)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('searchedWords', \TType::LST, 5);
      {
        $output->writeListBegin(\TType::STRING, count($this->searchedWords));
        {
          foreach ($this->searchedWords as $iter104)
          {
            $xfer += $output->writeString($iter104);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateCount !== null) {
      $xfer += $output->writeFieldBegin('updateCount', \TType::I32, 6);
      $xfer += $output->writeI32($this->updateCount);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteMetadata {
  static $_TSPEC;

  public $guid = null;
  public $title = null;
  public $contentLength = null;
  public $created = null;
  public $updated = null;
  public $deleted = null;
  public $updateSequenceNum = null;
  public $notebookGuid = null;
  public $tagGuids = null;
  public $attributes = null;
  public $largestResourceMime = null;
  public $largestResourceSize = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'title',
          'type' => \TType::STRING,
          ),
        5 => array(
          'var' => 'contentLength',
          'type' => \TType::I32,
          ),
        6 => array(
          'var' => 'created',
          'type' => \TType::I64,
          ),
        7 => array(
          'var' => 'updated',
          'type' => \TType::I64,
          ),
        8 => array(
          'var' => 'deleted',
          'type' => \TType::I64,
          ),
        10 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        11 => array(
          'var' => 'notebookGuid',
          'type' => \TType::STRING,
          ),
        12 => array(
          'var' => 'tagGuids',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        14 => array(
          'var' => 'attributes',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\NoteAttributes',
          ),
        20 => array(
          'var' => 'largestResourceMime',
          'type' => \TType::STRING,
          ),
        21 => array(
          'var' => 'largestResourceSize',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['title'])) {
        $this->title = $vals['title'];
      }
      if (isset($vals['contentLength'])) {
        $this->contentLength = $vals['contentLength'];
      }
      if (isset($vals['created'])) {
        $this->created = $vals['created'];
      }
      if (isset($vals['updated'])) {
        $this->updated = $vals['updated'];
      }
      if (isset($vals['deleted'])) {
        $this->deleted = $vals['deleted'];
      }
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['notebookGuid'])) {
        $this->notebookGuid = $vals['notebookGuid'];
      }
      if (isset($vals['tagGuids'])) {
        $this->tagGuids = $vals['tagGuids'];
      }
      if (isset($vals['attributes'])) {
        $this->attributes = $vals['attributes'];
      }
      if (isset($vals['largestResourceMime'])) {
        $this->largestResourceMime = $vals['largestResourceMime'];
      }
      if (isset($vals['largestResourceSize'])) {
        $this->largestResourceSize = $vals['largestResourceSize'];
      }
    }
  }

  public function getName() {
    return 'NoteMetadata';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->title);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->contentLength);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->created);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->updated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->deleted);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->notebookGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::LST) {
            $this->tagGuids = array();
            $_size105 = 0;
            $_etype108 = 0;
            $xfer += $input->readListBegin($_etype108, $_size105);
            for ($_i109 = 0; $_i109 < $_size105; ++$_i109)
            {
              $elem110 = null;
              $xfer += $input->readString($elem110);
              $this->tagGuids []= $elem110;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::STRUCT) {
            $this->attributes = new \EDAM\Types\NoteAttributes();
            $xfer += $this->attributes->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 20:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->largestResourceMime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 21:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->largestResourceSize);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteMetadata');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->title !== null) {
      $xfer += $output->writeFieldBegin('title', \TType::STRING, 2);
      $xfer += $output->writeString($this->title);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->contentLength !== null) {
      $xfer += $output->writeFieldBegin('contentLength', \TType::I32, 5);
      $xfer += $output->writeI32($this->contentLength);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->created !== null) {
      $xfer += $output->writeFieldBegin('created', \TType::I64, 6);
      $xfer += $output->writeI64($this->created);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updated !== null) {
      $xfer += $output->writeFieldBegin('updated', \TType::I64, 7);
      $xfer += $output->writeI64($this->updated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->deleted !== null) {
      $xfer += $output->writeFieldBegin('deleted', \TType::I64, 8);
      $xfer += $output->writeI64($this->deleted);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 10);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebookGuid !== null) {
      $xfer += $output->writeFieldBegin('notebookGuid', \TType::STRING, 11);
      $xfer += $output->writeString($this->notebookGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tagGuids !== null) {
      if (!is_array($this->tagGuids)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tagGuids', \TType::LST, 12);
      {
        $output->writeListBegin(\TType::STRING, count($this->tagGuids));
        {
          foreach ($this->tagGuids as $iter111)
          {
            $xfer += $output->writeString($iter111);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->attributes !== null) {
      if (!is_object($this->attributes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('attributes', \TType::STRUCT, 14);
      $xfer += $this->attributes->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->largestResourceMime !== null) {
      $xfer += $output->writeFieldBegin('largestResourceMime', \TType::STRING, 20);
      $xfer += $output->writeString($this->largestResourceMime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->largestResourceSize !== null) {
      $xfer += $output->writeFieldBegin('largestResourceSize', \TType::I32, 21);
      $xfer += $output->writeI32($this->largestResourceSize);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NotesMetadataList {
  static $_TSPEC;

  public $startIndex = null;
  public $totalNotes = null;
  public $notes = null;
  public $stoppedWords = null;
  public $searchedWords = null;
  public $updateCount = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'startIndex',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'totalNotes',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'notes',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\NoteStore\NoteMetadata',
            ),
          ),
        4 => array(
          'var' => 'stoppedWords',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        5 => array(
          'var' => 'searchedWords',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        6 => array(
          'var' => 'updateCount',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['startIndex'])) {
        $this->startIndex = $vals['startIndex'];
      }
      if (isset($vals['totalNotes'])) {
        $this->totalNotes = $vals['totalNotes'];
      }
      if (isset($vals['notes'])) {
        $this->notes = $vals['notes'];
      }
      if (isset($vals['stoppedWords'])) {
        $this->stoppedWords = $vals['stoppedWords'];
      }
      if (isset($vals['searchedWords'])) {
        $this->searchedWords = $vals['searchedWords'];
      }
      if (isset($vals['updateCount'])) {
        $this->updateCount = $vals['updateCount'];
      }
    }
  }

  public function getName() {
    return 'NotesMetadataList';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->startIndex);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->totalNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::LST) {
            $this->notes = array();
            $_size112 = 0;
            $_etype115 = 0;
            $xfer += $input->readListBegin($_etype115, $_size112);
            for ($_i116 = 0; $_i116 < $_size112; ++$_i116)
            {
              $elem117 = null;
              $elem117 = new \EDAM\NoteStore\NoteMetadata();
              $xfer += $elem117->read($input);
              $this->notes []= $elem117;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::LST) {
            $this->stoppedWords = array();
            $_size118 = 0;
            $_etype121 = 0;
            $xfer += $input->readListBegin($_etype121, $_size118);
            for ($_i122 = 0; $_i122 < $_size118; ++$_i122)
            {
              $elem123 = null;
              $xfer += $input->readString($elem123);
              $this->stoppedWords []= $elem123;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::LST) {
            $this->searchedWords = array();
            $_size124 = 0;
            $_etype127 = 0;
            $xfer += $input->readListBegin($_etype127, $_size124);
            for ($_i128 = 0; $_i128 < $_size124; ++$_i128)
            {
              $elem129 = null;
              $xfer += $input->readString($elem129);
              $this->searchedWords []= $elem129;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NotesMetadataList');
    if ($this->startIndex !== null) {
      $xfer += $output->writeFieldBegin('startIndex', \TType::I32, 1);
      $xfer += $output->writeI32($this->startIndex);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->totalNotes !== null) {
      $xfer += $output->writeFieldBegin('totalNotes', \TType::I32, 2);
      $xfer += $output->writeI32($this->totalNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notes !== null) {
      if (!is_array($this->notes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notes', \TType::LST, 3);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->notes));
        {
          foreach ($this->notes as $iter130)
          {
            $xfer += $iter130->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->stoppedWords !== null) {
      if (!is_array($this->stoppedWords)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('stoppedWords', \TType::LST, 4);
      {
        $output->writeListBegin(\TType::STRING, count($this->stoppedWords));
        {
          foreach ($this->stoppedWords as $iter131)
          {
            $xfer += $output->writeString($iter131);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->searchedWords !== null) {
      if (!is_array($this->searchedWords)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('searchedWords', \TType::LST, 5);
      {
        $output->writeListBegin(\TType::STRING, count($this->searchedWords));
        {
          foreach ($this->searchedWords as $iter132)
          {
            $xfer += $output->writeString($iter132);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updateCount !== null) {
      $xfer += $output->writeFieldBegin('updateCount', \TType::I32, 6);
      $xfer += $output->writeI32($this->updateCount);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NotesMetadataResultSpec {
  static $_TSPEC;

  public $includeTitle = null;
  public $includeContentLength = null;
  public $includeCreated = null;
  public $includeUpdated = null;
  public $includeDeleted = null;
  public $includeUpdateSequenceNum = null;
  public $includeNotebookGuid = null;
  public $includeTagGuids = null;
  public $includeAttributes = null;
  public $includeLargestResourceMime = null;
  public $includeLargestResourceSize = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        2 => array(
          'var' => 'includeTitle',
          'type' => \TType::BOOL,
          ),
        5 => array(
          'var' => 'includeContentLength',
          'type' => \TType::BOOL,
          ),
        6 => array(
          'var' => 'includeCreated',
          'type' => \TType::BOOL,
          ),
        7 => array(
          'var' => 'includeUpdated',
          'type' => \TType::BOOL,
          ),
        8 => array(
          'var' => 'includeDeleted',
          'type' => \TType::BOOL,
          ),
        10 => array(
          'var' => 'includeUpdateSequenceNum',
          'type' => \TType::BOOL,
          ),
        11 => array(
          'var' => 'includeNotebookGuid',
          'type' => \TType::BOOL,
          ),
        12 => array(
          'var' => 'includeTagGuids',
          'type' => \TType::BOOL,
          ),
        14 => array(
          'var' => 'includeAttributes',
          'type' => \TType::BOOL,
          ),
        20 => array(
          'var' => 'includeLargestResourceMime',
          'type' => \TType::BOOL,
          ),
        21 => array(
          'var' => 'includeLargestResourceSize',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['includeTitle'])) {
        $this->includeTitle = $vals['includeTitle'];
      }
      if (isset($vals['includeContentLength'])) {
        $this->includeContentLength = $vals['includeContentLength'];
      }
      if (isset($vals['includeCreated'])) {
        $this->includeCreated = $vals['includeCreated'];
      }
      if (isset($vals['includeUpdated'])) {
        $this->includeUpdated = $vals['includeUpdated'];
      }
      if (isset($vals['includeDeleted'])) {
        $this->includeDeleted = $vals['includeDeleted'];
      }
      if (isset($vals['includeUpdateSequenceNum'])) {
        $this->includeUpdateSequenceNum = $vals['includeUpdateSequenceNum'];
      }
      if (isset($vals['includeNotebookGuid'])) {
        $this->includeNotebookGuid = $vals['includeNotebookGuid'];
      }
      if (isset($vals['includeTagGuids'])) {
        $this->includeTagGuids = $vals['includeTagGuids'];
      }
      if (isset($vals['includeAttributes'])) {
        $this->includeAttributes = $vals['includeAttributes'];
      }
      if (isset($vals['includeLargestResourceMime'])) {
        $this->includeLargestResourceMime = $vals['includeLargestResourceMime'];
      }
      if (isset($vals['includeLargestResourceSize'])) {
        $this->includeLargestResourceSize = $vals['includeLargestResourceSize'];
      }
    }
  }

  public function getName() {
    return 'NotesMetadataResultSpec';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 2:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeTitle);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeContentLength);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeCreated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 7:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeUpdated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 8:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeDeleted);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 10:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeUpdateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 11:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeNotebookGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 12:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeTagGuids);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 14:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeAttributes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 20:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeLargestResourceMime);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 21:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeLargestResourceSize);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NotesMetadataResultSpec');
    if ($this->includeTitle !== null) {
      $xfer += $output->writeFieldBegin('includeTitle', \TType::BOOL, 2);
      $xfer += $output->writeBool($this->includeTitle);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeContentLength !== null) {
      $xfer += $output->writeFieldBegin('includeContentLength', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->includeContentLength);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeCreated !== null) {
      $xfer += $output->writeFieldBegin('includeCreated', \TType::BOOL, 6);
      $xfer += $output->writeBool($this->includeCreated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeUpdated !== null) {
      $xfer += $output->writeFieldBegin('includeUpdated', \TType::BOOL, 7);
      $xfer += $output->writeBool($this->includeUpdated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeDeleted !== null) {
      $xfer += $output->writeFieldBegin('includeDeleted', \TType::BOOL, 8);
      $xfer += $output->writeBool($this->includeDeleted);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeUpdateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('includeUpdateSequenceNum', \TType::BOOL, 10);
      $xfer += $output->writeBool($this->includeUpdateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeNotebookGuid !== null) {
      $xfer += $output->writeFieldBegin('includeNotebookGuid', \TType::BOOL, 11);
      $xfer += $output->writeBool($this->includeNotebookGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeTagGuids !== null) {
      $xfer += $output->writeFieldBegin('includeTagGuids', \TType::BOOL, 12);
      $xfer += $output->writeBool($this->includeTagGuids);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeAttributes !== null) {
      $xfer += $output->writeFieldBegin('includeAttributes', \TType::BOOL, 14);
      $xfer += $output->writeBool($this->includeAttributes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeLargestResourceMime !== null) {
      $xfer += $output->writeFieldBegin('includeLargestResourceMime', \TType::BOOL, 20);
      $xfer += $output->writeBool($this->includeLargestResourceMime);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeLargestResourceSize !== null) {
      $xfer += $output->writeFieldBegin('includeLargestResourceSize', \TType::BOOL, 21);
      $xfer += $output->writeBool($this->includeLargestResourceSize);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteCollectionCounts {
  static $_TSPEC;

  public $notebookCounts = null;
  public $tagCounts = null;
  public $trashCount = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'notebookCounts',
          'type' => \TType::MAP,
          'ktype' => \TType::STRING,
          'vtype' => \TType::I32,
          'key' => array(
            'type' => \TType::STRING,
          ),
          'val' => array(
            'type' => \TType::I32,
            ),
          ),
        2 => array(
          'var' => 'tagCounts',
          'type' => \TType::MAP,
          'ktype' => \TType::STRING,
          'vtype' => \TType::I32,
          'key' => array(
            'type' => \TType::STRING,
          ),
          'val' => array(
            'type' => \TType::I32,
            ),
          ),
        3 => array(
          'var' => 'trashCount',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['notebookCounts'])) {
        $this->notebookCounts = $vals['notebookCounts'];
      }
      if (isset($vals['tagCounts'])) {
        $this->tagCounts = $vals['tagCounts'];
      }
      if (isset($vals['trashCount'])) {
        $this->trashCount = $vals['trashCount'];
      }
    }
  }

  public function getName() {
    return 'NoteCollectionCounts';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::MAP) {
            $this->notebookCounts = array();
            $_size133 = 0;
            $_ktype134 = 0;
            $_vtype135 = 0;
            $xfer += $input->readMapBegin($_ktype134, $_vtype135, $_size133);
            for ($_i137 = 0; $_i137 < $_size133; ++$_i137)
            {
              $key138 = '';
              $val139 = 0;
              $xfer += $input->readString($key138);
              $xfer += $input->readI32($val139);
              $this->notebookCounts[$key138] = $val139;
            }
            $xfer += $input->readMapEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::MAP) {
            $this->tagCounts = array();
            $_size140 = 0;
            $_ktype141 = 0;
            $_vtype142 = 0;
            $xfer += $input->readMapBegin($_ktype141, $_vtype142, $_size140);
            for ($_i144 = 0; $_i144 < $_size140; ++$_i144)
            {
              $key145 = '';
              $val146 = 0;
              $xfer += $input->readString($key145);
              $xfer += $input->readI32($val146);
              $this->tagCounts[$key145] = $val146;
            }
            $xfer += $input->readMapEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->trashCount);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteCollectionCounts');
    if ($this->notebookCounts !== null) {
      if (!is_array($this->notebookCounts)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notebookCounts', \TType::MAP, 1);
      {
        $output->writeMapBegin(\TType::STRING, \TType::I32, count($this->notebookCounts));
        {
          foreach ($this->notebookCounts as $kiter147 => $viter148)
          {
            $xfer += $output->writeString($kiter147);
            $xfer += $output->writeI32($viter148);
          }
        }
        $output->writeMapEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tagCounts !== null) {
      if (!is_array($this->tagCounts)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tagCounts', \TType::MAP, 2);
      {
        $output->writeMapBegin(\TType::STRING, \TType::I32, count($this->tagCounts));
        {
          foreach ($this->tagCounts as $kiter149 => $viter150)
          {
            $xfer += $output->writeString($kiter149);
            $xfer += $output->writeI32($viter150);
          }
        }
        $output->writeMapEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->trashCount !== null) {
      $xfer += $output->writeFieldBegin('trashCount', \TType::I32, 3);
      $xfer += $output->writeI32($this->trashCount);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteEmailParameters {
  static $_TSPEC;

  public $guid = null;
  public $note = null;
  public $toAddresses = null;
  public $ccAddresses = null;
  public $subject = null;
  public $message = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'guid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'note',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\Types\Note',
          ),
        3 => array(
          'var' => 'toAddresses',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        4 => array(
          'var' => 'ccAddresses',
          'type' => \TType::LST,
          'etype' => \TType::STRING,
          'elem' => array(
            'type' => \TType::STRING,
            ),
          ),
        5 => array(
          'var' => 'subject',
          'type' => \TType::STRING,
          ),
        6 => array(
          'var' => 'message',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['guid'])) {
        $this->guid = $vals['guid'];
      }
      if (isset($vals['note'])) {
        $this->note = $vals['note'];
      }
      if (isset($vals['toAddresses'])) {
        $this->toAddresses = $vals['toAddresses'];
      }
      if (isset($vals['ccAddresses'])) {
        $this->ccAddresses = $vals['ccAddresses'];
      }
      if (isset($vals['subject'])) {
        $this->subject = $vals['subject'];
      }
      if (isset($vals['message'])) {
        $this->message = $vals['message'];
      }
    }
  }

  public function getName() {
    return 'NoteEmailParameters';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->guid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRUCT) {
            $this->note = new \EDAM\Types\Note();
            $xfer += $this->note->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::LST) {
            $this->toAddresses = array();
            $_size151 = 0;
            $_etype154 = 0;
            $xfer += $input->readListBegin($_etype154, $_size151);
            for ($_i155 = 0; $_i155 < $_size151; ++$_i155)
            {
              $elem156 = null;
              $xfer += $input->readString($elem156);
              $this->toAddresses []= $elem156;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::LST) {
            $this->ccAddresses = array();
            $_size157 = 0;
            $_etype160 = 0;
            $xfer += $input->readListBegin($_etype160, $_size157);
            for ($_i161 = 0; $_i161 < $_size157; ++$_i161)
            {
              $elem162 = null;
              $xfer += $input->readString($elem162);
              $this->ccAddresses []= $elem162;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->subject);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 6:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->message);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteEmailParameters');
    if ($this->guid !== null) {
      $xfer += $output->writeFieldBegin('guid', \TType::STRING, 1);
      $xfer += $output->writeString($this->guid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->note !== null) {
      if (!is_object($this->note)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('note', \TType::STRUCT, 2);
      $xfer += $this->note->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->toAddresses !== null) {
      if (!is_array($this->toAddresses)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('toAddresses', \TType::LST, 3);
      {
        $output->writeListBegin(\TType::STRING, count($this->toAddresses));
        {
          foreach ($this->toAddresses as $iter163)
          {
            $xfer += $output->writeString($iter163);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->ccAddresses !== null) {
      if (!is_array($this->ccAddresses)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('ccAddresses', \TType::LST, 4);
      {
        $output->writeListBegin(\TType::STRING, count($this->ccAddresses));
        {
          foreach ($this->ccAddresses as $iter164)
          {
            $xfer += $output->writeString($iter164);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->subject !== null) {
      $xfer += $output->writeFieldBegin('subject', \TType::STRING, 5);
      $xfer += $output->writeString($this->subject);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->message !== null) {
      $xfer += $output->writeFieldBegin('message', \TType::STRING, 6);
      $xfer += $output->writeString($this->message);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class NoteVersionId {
  static $_TSPEC;

  public $updateSequenceNum = null;
  public $updated = null;
  public $saved = null;
  public $title = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'updateSequenceNum',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'updated',
          'type' => \TType::I64,
          ),
        3 => array(
          'var' => 'saved',
          'type' => \TType::I64,
          ),
        4 => array(
          'var' => 'title',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['updateSequenceNum'])) {
        $this->updateSequenceNum = $vals['updateSequenceNum'];
      }
      if (isset($vals['updated'])) {
        $this->updated = $vals['updated'];
      }
      if (isset($vals['saved'])) {
        $this->saved = $vals['saved'];
      }
      if (isset($vals['title'])) {
        $this->title = $vals['title'];
      }
    }
  }

  public function getName() {
    return 'NoteVersionId';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->updateSequenceNum);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->updated);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I64) {
            $xfer += $input->readI64($this->saved);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->title);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('NoteVersionId');
    if ($this->updateSequenceNum !== null) {
      $xfer += $output->writeFieldBegin('updateSequenceNum', \TType::I32, 1);
      $xfer += $output->writeI32($this->updateSequenceNum);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->updated !== null) {
      $xfer += $output->writeFieldBegin('updated', \TType::I64, 2);
      $xfer += $output->writeI64($this->updated);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->saved !== null) {
      $xfer += $output->writeFieldBegin('saved', \TType::I64, 3);
      $xfer += $output->writeI64($this->saved);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->title !== null) {
      $xfer += $output->writeFieldBegin('title', \TType::STRING, 4);
      $xfer += $output->writeString($this->title);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class ClientUsageMetrics {
  static $_TSPEC;

  public $sessions = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'sessions',
          'type' => \TType::I32,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['sessions'])) {
        $this->sessions = $vals['sessions'];
      }
    }
  }

  public function getName() {
    return 'ClientUsageMetrics';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->sessions);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('ClientUsageMetrics');
    if ($this->sessions !== null) {
      $xfer += $output->writeFieldBegin('sessions', \TType::I32, 1);
      $xfer += $output->writeI32($this->sessions);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class RelatedQuery {
  static $_TSPEC;

  public $noteGuid = null;
  public $plainText = null;
  public $filter = null;
  public $referenceUri = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'noteGuid',
          'type' => \TType::STRING,
          ),
        2 => array(
          'var' => 'plainText',
          'type' => \TType::STRING,
          ),
        3 => array(
          'var' => 'filter',
          'type' => \TType::STRUCT,
          'class' => '\EDAM\NoteStore\NoteFilter',
          ),
        4 => array(
          'var' => 'referenceUri',
          'type' => \TType::STRING,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['noteGuid'])) {
        $this->noteGuid = $vals['noteGuid'];
      }
      if (isset($vals['plainText'])) {
        $this->plainText = $vals['plainText'];
      }
      if (isset($vals['filter'])) {
        $this->filter = $vals['filter'];
      }
      if (isset($vals['referenceUri'])) {
        $this->referenceUri = $vals['referenceUri'];
      }
    }
  }

  public function getName() {
    return 'RelatedQuery';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->noteGuid);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->plainText);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::STRUCT) {
            $this->filter = new \EDAM\NoteStore\NoteFilter();
            $xfer += $this->filter->read($input);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::STRING) {
            $xfer += $input->readString($this->referenceUri);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('RelatedQuery');
    if ($this->noteGuid !== null) {
      $xfer += $output->writeFieldBegin('noteGuid', \TType::STRING, 1);
      $xfer += $output->writeString($this->noteGuid);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->plainText !== null) {
      $xfer += $output->writeFieldBegin('plainText', \TType::STRING, 2);
      $xfer += $output->writeString($this->plainText);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->filter !== null) {
      if (!is_object($this->filter)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('filter', \TType::STRUCT, 3);
      $xfer += $this->filter->write($output);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->referenceUri !== null) {
      $xfer += $output->writeFieldBegin('referenceUri', \TType::STRING, 4);
      $xfer += $output->writeString($this->referenceUri);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class RelatedResult {
  static $_TSPEC;

  public $notes = null;
  public $notebooks = null;
  public $tags = null;
  public $containingNotebooks = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'notes',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Note',
            ),
          ),
        2 => array(
          'var' => 'notebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Notebook',
            ),
          ),
        3 => array(
          'var' => 'tags',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\Tag',
            ),
          ),
        4 => array(
          'var' => 'containingNotebooks',
          'type' => \TType::LST,
          'etype' => \TType::STRUCT,
          'elem' => array(
            'type' => \TType::STRUCT,
            'class' => '\EDAM\Types\NotebookDescriptor',
            ),
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['notes'])) {
        $this->notes = $vals['notes'];
      }
      if (isset($vals['notebooks'])) {
        $this->notebooks = $vals['notebooks'];
      }
      if (isset($vals['tags'])) {
        $this->tags = $vals['tags'];
      }
      if (isset($vals['containingNotebooks'])) {
        $this->containingNotebooks = $vals['containingNotebooks'];
      }
    }
  }

  public function getName() {
    return 'RelatedResult';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::LST) {
            $this->notes = array();
            $_size165 = 0;
            $_etype168 = 0;
            $xfer += $input->readListBegin($_etype168, $_size165);
            for ($_i169 = 0; $_i169 < $_size165; ++$_i169)
            {
              $elem170 = null;
              $elem170 = new \EDAM\Types\Note();
              $xfer += $elem170->read($input);
              $this->notes []= $elem170;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::LST) {
            $this->notebooks = array();
            $_size171 = 0;
            $_etype174 = 0;
            $xfer += $input->readListBegin($_etype174, $_size171);
            for ($_i175 = 0; $_i175 < $_size171; ++$_i175)
            {
              $elem176 = null;
              $elem176 = new \EDAM\Types\Notebook();
              $xfer += $elem176->read($input);
              $this->notebooks []= $elem176;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::LST) {
            $this->tags = array();
            $_size177 = 0;
            $_etype180 = 0;
            $xfer += $input->readListBegin($_etype180, $_size177);
            for ($_i181 = 0; $_i181 < $_size177; ++$_i181)
            {
              $elem182 = null;
              $elem182 = new \EDAM\Types\Tag();
              $xfer += $elem182->read($input);
              $this->tags []= $elem182;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::LST) {
            $this->containingNotebooks = array();
            $_size183 = 0;
            $_etype186 = 0;
            $xfer += $input->readListBegin($_etype186, $_size183);
            for ($_i187 = 0; $_i187 < $_size183; ++$_i187)
            {
              $elem188 = null;
              $elem188 = new \EDAM\Types\NotebookDescriptor();
              $xfer += $elem188->read($input);
              $this->containingNotebooks []= $elem188;
            }
            $xfer += $input->readListEnd();
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('RelatedResult');
    if ($this->notes !== null) {
      if (!is_array($this->notes)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notes', \TType::LST, 1);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->notes));
        {
          foreach ($this->notes as $iter189)
          {
            $xfer += $iter189->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->notebooks !== null) {
      if (!is_array($this->notebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('notebooks', \TType::LST, 2);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->notebooks));
        {
          foreach ($this->notebooks as $iter190)
          {
            $xfer += $iter190->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->tags !== null) {
      if (!is_array($this->tags)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('tags', \TType::LST, 3);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->tags));
        {
          foreach ($this->tags as $iter191)
          {
            $xfer += $iter191->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    if ($this->containingNotebooks !== null) {
      if (!is_array($this->containingNotebooks)) {
        throw new \TProtocolException('Bad type in structure.', \TProtocolException::INVALID_DATA);
      }
      $xfer += $output->writeFieldBegin('containingNotebooks', \TType::LST, 4);
      {
        $output->writeListBegin(\TType::STRUCT, count($this->containingNotebooks));
        {
          foreach ($this->containingNotebooks as $iter192)
          {
            $xfer += $iter192->write($output);
          }
        }
        $output->writeListEnd();
      }
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

class RelatedResultSpec {
  static $_TSPEC;

  public $maxNotes = null;
  public $maxNotebooks = null;
  public $maxTags = null;
  public $writableNotebooksOnly = null;
  public $includeContainingNotebooks = null;

  public function __construct($vals=null) {
    if (!isset(self::$_TSPEC)) {
      self::$_TSPEC = array(
        1 => array(
          'var' => 'maxNotes',
          'type' => \TType::I32,
          ),
        2 => array(
          'var' => 'maxNotebooks',
          'type' => \TType::I32,
          ),
        3 => array(
          'var' => 'maxTags',
          'type' => \TType::I32,
          ),
        4 => array(
          'var' => 'writableNotebooksOnly',
          'type' => \TType::BOOL,
          ),
        5 => array(
          'var' => 'includeContainingNotebooks',
          'type' => \TType::BOOL,
          ),
        );
    }
    if (is_array($vals)) {
      if (isset($vals['maxNotes'])) {
        $this->maxNotes = $vals['maxNotes'];
      }
      if (isset($vals['maxNotebooks'])) {
        $this->maxNotebooks = $vals['maxNotebooks'];
      }
      if (isset($vals['maxTags'])) {
        $this->maxTags = $vals['maxTags'];
      }
      if (isset($vals['writableNotebooksOnly'])) {
        $this->writableNotebooksOnly = $vals['writableNotebooksOnly'];
      }
      if (isset($vals['includeContainingNotebooks'])) {
        $this->includeContainingNotebooks = $vals['includeContainingNotebooks'];
      }
    }
  }

  public function getName() {
    return 'RelatedResultSpec';
  }

  public function read($input)
  {
    $xfer = 0;
    $fname = null;
    $ftype = 0;
    $fid = 0;
    $xfer += $input->readStructBegin($fname);
    while (true)
    {
      $xfer += $input->readFieldBegin($fname, $ftype, $fid);
      if ($ftype == \TType::STOP) {
        break;
      }
      switch ($fid)
      {
        case 1:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->maxNotes);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 2:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->maxNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 3:
          if ($ftype == \TType::I32) {
            $xfer += $input->readI32($this->maxTags);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 4:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->writableNotebooksOnly);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        case 5:
          if ($ftype == \TType::BOOL) {
            $xfer += $input->readBool($this->includeContainingNotebooks);
          } else {
            $xfer += $input->skip($ftype);
          }
          break;
        default:
          $xfer += $input->skip($ftype);
          break;
      }
      $xfer += $input->readFieldEnd();
    }
    $xfer += $input->readStructEnd();
    return $xfer;
  }

  public function write($output) {
    $xfer = 0;
    $xfer += $output->writeStructBegin('RelatedResultSpec');
    if ($this->maxNotes !== null) {
      $xfer += $output->writeFieldBegin('maxNotes', \TType::I32, 1);
      $xfer += $output->writeI32($this->maxNotes);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->maxNotebooks !== null) {
      $xfer += $output->writeFieldBegin('maxNotebooks', \TType::I32, 2);
      $xfer += $output->writeI32($this->maxNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->maxTags !== null) {
      $xfer += $output->writeFieldBegin('maxTags', \TType::I32, 3);
      $xfer += $output->writeI32($this->maxTags);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->writableNotebooksOnly !== null) {
      $xfer += $output->writeFieldBegin('writableNotebooksOnly', \TType::BOOL, 4);
      $xfer += $output->writeBool($this->writableNotebooksOnly);
      $xfer += $output->writeFieldEnd();
    }
    if ($this->includeContainingNotebooks !== null) {
      $xfer += $output->writeFieldBegin('includeContainingNotebooks', \TType::BOOL, 5);
      $xfer += $output->writeBool($this->includeContainingNotebooks);
      $xfer += $output->writeFieldEnd();
    }
    $xfer += $output->writeFieldStop();
    $xfer += $output->writeStructEnd();
    return $xfer;
  }

}

?>
