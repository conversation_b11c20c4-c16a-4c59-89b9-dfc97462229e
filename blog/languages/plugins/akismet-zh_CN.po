# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-11-18 21:25:45+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: views/notice.php:70
msgid "Upgrade plan"
msgstr "升级套餐"

#. translators: The placeholder is a URL to the contact form.
#: views/notice.php:64
msgid "If you believe your site should not be classified as commercial, <a href=\"%s\">please get in touch</a>."
msgstr "如果您认为您的站点不应归类为商业站点，<a href=\"%s\">请联系我们</a>。"

#. translators: The placeholder is a URL.
#: views/notice.php:58
msgid "Your current subscription is for <a href=\"%s\">personal, non-commercial use</a>. Please upgrade your plan to continue using Akismet."
msgstr "您当前的订阅仅适用于<a href=\"%s\">个人非商业用途</a>。 请升级您的套餐以继续使用 Akismet。"

#: views/notice.php:54
msgid "We detected commercial activity on your site"
msgstr "我们在您的站点上检测到了商业活动"

#: views/notice.php:27
msgid "Almost done! Configure Akismet and say goodbye to spam"
msgstr "马上就好！ 配置 Akismet，彻底告别垃圾邮件"

#: views/setup.php:7
msgid "Choose an Akismet plan"
msgstr "选择 Akismet 计划"

#: class.akismet-admin.php:770
msgid "This comment was not sent to Akismet when it was submitted because it was caught by the comment disallowed list."
msgstr "此评论提交后未发送至 Akismet，因为它被不允许的评论列表所捕获。"

#: class.akismet-admin.php:767
msgid "This comment was not sent to Akismet when it was submitted because it was caught by something else."
msgstr "此评论提交后未发送至 Akismet，因为它被其他软件所捕获。"

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "请<a href=\"%s\" target=\"_blank\">选择一款套餐</a>，开始使用 Akismet。"

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "API 密钥必须搭配 Akismet 套餐，才能保护您的站点免遭垃圾邮件侵扰。"

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "多条评论符合要求。"

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "无法找到符合要求的评论。"

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "‘评论’参数必须为数组。"

#: class.akismet-admin.php:764
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet 在重新检查时清除了此评论。 评论已被其他用户或插件修改，因此它并未更新评论状态。"

#: class.akismet-admin.php:761
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet 在重新检查时将此评论确定为垃圾评论。 评论已被其他用户或插件修改，因此并未更新评论状态。"

#: class.akismet-admin.php:758
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "Akismet 清除了此评论，并通过 Webhook 更新了其状态。"

#: class.akismet-admin.php:755
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "Akismet 将此评论视为垃圾评论，并通过 Webhook 更新了其状态。"

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "Akismet 正在保护您的站点免遭垃圾邮件侵扰。"

#: views/config.php:300
msgid "Account overview"
msgstr "账户概览"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:188
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "%1$s 中的垃圾评论将在 %2$d 天后自动删除。"

#: views/config.php:183
msgid "spam folder"
msgstr "垃圾评论文件夹"

#: views/stats.php:11
msgid "Akismet detailed stats"
msgstr "Akismet 详细统计"

#: views/stats.php:6
msgid "Back to settings"
msgstr "返回设置"

#: views/config.php:264
msgid "Subscription type"
msgstr "订阅类型"

#: views/config.php:228
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "为了帮助您的网站在隐私法（如 GDPR）下保持透明度，Akismet 可以在评论表单下向用户显示通知。"

#: views/config.php:150
msgid "Spam filtering"
msgstr "垃圾评论过滤"

#: views/config.php:90 views/enter.php:9
msgid "API key"
msgstr "API密钥"

#: views/config.php:44
msgid "Akismet stats"
msgstr "Akismet 统计数据"

#. Author of the plugin
#: akismet.php
msgid "Automattic - Anti-spam Team"
msgstr "Automattic - 反垃圾评论团队"

#. Plugin Name of the plugin
#: akismet.php
msgid "Akismet Anti-spam: Spam Protection"
msgstr "Akismet 反垃圾评论：垃圾评论保护"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "WP-Cron 已被 DISABLE_WP_CRON 常量禁用，评论检查可能无法正常运作。"

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:802
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "（在新标签页中打开）"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "升级至 %s"

#: views/notice.php:336
msgid "Upgrade your subscription level"
msgstr "升级订阅级别"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "了解有关用量限制的详细信息。"

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "自 %1$s 以来，您的账户 API 已经调用 %2$s 次，而您的账户计划限制为 %3$s 次。"

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "您的 Akismet 使用量已连续三个月超出计划限制。我们已在本月剩余时间内限制您的帐户。请升级您的计划以便 Akismet 能够继续阻止垃圾评论。"

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "您的 Akismet 使用量连续第三个月接近计划限制。在您达到限制后，我们将限制您的帐户。请升级您的计划以便 Akismet 能够继续阻止垃圾评论。"

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "您的 Akismet 使用量已连续两个月超出计划限制。下个月，我们将在您达到限制后限制您的帐户。请考虑升级您的计划。"

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "您的帐户已被限制"

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "您的 Akismet 帐户使用量已接近订阅计划的限制"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "您的 Akismet 帐户使用量超出了订阅计划的限制"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "请输入一个新的密钥或 <a href=\"%s\" target=\"_blank\">联系 Akismet 支持</a>。"

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "您的API密钥已失效。"

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:490
msgid "Checking for Spam (%1$s%)"
msgstr "正在检查垃圾评论（%1$s％）"

#: class.akismet-admin.php:818
msgid "No comment history."
msgstr "没有评论历史记录。"

#: class.akismet-admin.php:751
msgid "Akismet was unable to recheck this comment."
msgstr "Akismet 无法重新检查该评论。"

#: class.akismet-admin.php:743
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "Akismet 无法检查该评论，稍后将自动重试。"

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:712
msgid "Comment was caught by %s."
msgstr "评论已被 %s 捕获。"

#: class.akismet.php:807
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet 尚未完成配置。请输入 API 密钥。"

#: views/enter.php:7
msgid "Enter your API key"
msgstr "输入您的 API 密钥"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "设置其他帐户"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "设置您的Akismet帐户以在此站点上启用垃圾评论过滤。"

#: class.akismet-admin.php:1330
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet 无法重新检查队列中的垃圾评论。"

#: class.akismet-admin.php:523
msgid "You don&#8217;t have permission to do that."
msgstr "您没有权限执行此操作。"

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "未能解码状态响应。"

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "当前未能载入统计，请重试。"

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "要载入统计，必须设置API密钥。"

#: views/config.php:221
msgid "Do not display privacy notice."
msgstr "不展示隐私通知。"

#: views/config.php:213
msgid "Display a privacy notice under your comment forms."
msgstr "在您的评论表单下展示隐私通知。"

#: views/config.php:207
msgid "Akismet privacy notice"
msgstr "Akismet 隐私声明"

#: views/config.php:202
msgid "Privacy"
msgstr "隐私"

#. translators: %s: Akismet privacy URL
#: class.akismet.php:1915
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed</a>."
msgstr "此站点使用Akismet来减少垃圾评论。<a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">了解我们如何处理您的评论数据</a>。"

#: class.akismet-admin.php:106
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "我们收集在使用 Akismet 反垃圾邮件服务的网站上发表评论的访客信息。我们收集的信息取决于用户如何为网站设置 Akismet，但通常包括评论者的 IP 地址、用户代理、引荐来源网址和网站 URL（以及评论者直接提供的其他信息，如姓名、用户名、电子邮件地址，以及评论本身）。"

#: class.akismet.php:409
msgid "Comment discarded."
msgstr "评论已丢弃。"

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "这个站点的API密钥是硬编码的，不能被删除。"

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "提供的键值不是有效且已注册的API密钥。"

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "本站点的API密钥是硬编码的，不能通过API进行更改。"

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "检索统计信息的时间段。 选项：60天，6个月，全部"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "如果为true，则在评论列表页面中显示评论作者旁边已批准评论的数量。"

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "如果为true，Akismet会自动丢弃最糟糕的垃圾评论，而不是将其放入垃圾评论文件夹。"

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "12个字符的Akismet API密钥。 可从akismet.com/get/获得"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "您的站点无法连接到Akismet服务器。"

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "在本站点的%s文件中已经定义了一个Akismet API密钥。"

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "手工配置"

#: class.akismet-admin.php:284
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "在此页面上，您可以更新您的Akismet设置并查看垃圾评论统计资料。"

#. Description of the plugin
#: akismet.php
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Akismet Anti-spam keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Akismet 已被数百万人使用，可能是世界上<strong>保护您的博客免受垃圾邮件侵害</strong>的最佳方法。即使在您睡觉时，Akismet 反垃圾邮件功能也能保护您的网站。开始使用：激活 Akismet 插件，然后进入 Akismet 设置页面设置 API 密钥。"

#: class.akismet-admin.php:133 class.akismet-admin.php:135
msgid "Akismet Anti-spam"
msgstr "Akismet 反垃圾评论"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "使用 API 密钥连接"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "您已以 %s 身份连接。"

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "与 Jetpack 连接"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "使用Jetpack连接激活Akismet。"

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "消除您站点的垃圾评论"

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "您要<a href=\"%s\">查看待审评论</a>吗？"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "设置您的 Akismet 帐户"

#: views/config.php:36
msgid "Detailed stats"
msgstr "详细统计"

#: views/config.php:31
msgid "Statistics"
msgstr "统计数据"

#: class.akismet-admin.php:1431
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "已被数百万人使用，Akismet 很可能是世界上<strong>保护您的博客免受垃圾评论侵扰</strong>的最佳方式。即使在您睡觉时，它也能保护您的网站。 要开始使用，只需转至<a href=\"admin.php?page=akismet-key-config\">Akismet 设置页面</a>设置您的 API 密钥。"

#: class.akismet-admin.php:1429
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "由千百万人使用，Akismet可能是保护您的站点免受垃圾评论<strong>的世界上最好的方式</strong>。 您的站点已完全配置并获得不间断的保护。"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1324
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "已捕获 %s 则垃圾评论。"

#: class.akismet-admin.php:1321
msgid "No comments were caught as spam."
msgstr "没有评论被判定为垃圾评论。"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1317
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet 已检查 %s 则评论。"

#: class.akismet-admin.php:1314
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "没有可供检查的评论。 Akismet 只会检查等待审核的评论。"

#: class.akismet.php:813
msgid "Comment not found."
msgstr "未找到评论。"

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d条评论无法被检查。"

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "已将%d条评论移动到垃圾评论。"

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "处理了%d条评论。"

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "评论#%d无法被检查。"

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "连接Akismet失败。"

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "#%d不是垃圾评论。"

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "#%d是垃圾评论。"

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s假阳性"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s错过的垃圾"

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "您没有Akismet方案。"

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "您的Akismet订阅已被暂停。"

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "您的 Akismet 计划已被取消。"

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "我们未能处理您的付款。请<a href=\"%s\" target=\"_blank\">更新您的付款信息</a>。"

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "请更新您的付款信息。"

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1224
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet为您节省了%d分钟！"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1221
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet为您节省了%d小时！"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1218
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet为您节省了%s天！"

#: class.akismet-admin.php:233 class.akismet-admin.php:271
#: class.akismet-admin.php:283
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet为您过滤垃圾，让您可以专注于更重要的事情。"

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "无法连接到akismet.com。请参见<a href=\"%s\" target=\"_blank\">我们关于防火墙的指南</a>并检查您的服务器设置。"

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "您输入的API密钥无法被验证。"

#: views/config.php:117
msgid "All systems functional."
msgstr "所有系统均正常运行。"

#: views/config.php:116
msgid "Enabled."
msgstr "启用。"

#: views/config.php:114
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet在早前进行SSL请求时遇到了错误并已暂时将其禁用。Akismet将随即恢复使用SSL作出请求。"

#: views/config.php:113
msgid "Temporarily disabled."
msgstr "暂时禁用。"

#: views/config.php:108
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "您的网站服务器不能作出SSL请求；请联系您的主机提供商并让他们加入SSL请求支持。"

#: views/config.php:107
msgid "Disabled."
msgstr "已禁用。"

#: views/config.php:104
msgid "SSL status"
msgstr "SSL 状态"

#: class.akismet-admin.php:729
msgid "This comment was reported as not spam."
msgstr "此评论被报告为非垃圾评论。"

#: class.akismet-admin.php:721
msgid "This comment was reported as spam."
msgstr "这条评论被报告为垃圾。"

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "手动输入 API 密钥"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "联系 Akismet 支持"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "不用担心！请与我们联系，我们将解决这个问题。"

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "您的订阅 %s 已暂停。"

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "您的订阅 %s 已取消。"

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "您输入的密钥无效。请仔细检查。"

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "您输入的API密钥有问题。"

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "您可以通过<a href=\"%s\" target=\"_blank\">捐献一定金额</a>来升级您的账号，并帮助我们战胜垃圾评论。"

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "请联系<a href=\"%s\" target=\"_blank\">Akismet支持</a>来获取帮助。"

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "请访问您的<a href=\"%s\" target=\"_blank\">Akismet账户页</a>来重新激活您的订阅。"

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "您的防火墙可能阻止了 Akismet 连接到其 API。 请联系您的主机商，并参考<a href=\"%s\" target=\"_blank\">我们关于防火墙的指南</a>。"

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "您的主机或服务器管理员禁用了PHP的<code>gethostbynamel</code>函数。<strong>在这被修复前，Akismet将不能正常工作。</strong>请联系您的主机或防火墙管理员并请他们查阅<a href=\"%s\" target=\"_blank\">Akismet的系统需求</a>。"

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "网络功能已被禁用。"

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "需要更多信息：%s"

#. translators: The placeholder is an error code returned by Akismet.
#: views/notice.php:78
msgid "Akismet error code: %s"
msgstr "Akismet 错误代码：%s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "部分评论尚未经过 Akismet 检查是否为垃圾评论。它们已被暂时搁置以待审核，稍后将自动重新检查。"

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "Akismet发现了问题。"

#: views/config.php:308
msgid "Change"
msgstr "更改"

#: views/config.php:308
msgid "Upgrade"
msgstr "升级"

#: views/config.php:289
msgid "Next billing date"
msgstr "下个付款日"

#: views/config.php:282
msgid "Active"
msgstr "活跃"

#: views/config.php:280
msgid "No subscription found"
msgstr "未找到订阅"

#: views/config.php:278
msgid "Missing"
msgstr "缺失"

#: views/config.php:276
msgid "Suspended"
msgstr "暂停"

#: views/config.php:274
msgid "Cancelled"
msgstr "取消"

#: views/config.php:245
msgid "Save changes"
msgstr "保存更改"

#: views/config.php:237
msgid "Disconnect this account"
msgstr "断开此帐户的连接"

#: views/config.php:176
msgid "Note:"
msgstr "注意："

#: views/config.php:169
msgid "Always put spam in the Spam folder for review."
msgstr "总是将垃圾评论放入垃圾评论目录以便审阅。"

#: views/config.php:161
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "自动丢弃显而易见的广告，来个眼不见为净。"

#: views/config.php:155
msgid "Akismet Anti-spam strictness"
msgstr "Akismet 反垃圾评论严密度"

#: views/config.php:142
msgid "Show the number of approved comments beside each comment author."
msgstr "在每个评论作者旁边显示已批准评论的数量。"

#: views/config.php:59
msgid "Accuracy"
msgstr "精确度"

#: views/config.php:54
msgid "All time"
msgstr "所有时间"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "已阻挡的垃圾评论"

#: views/config.php:49
msgid "Past six months"
msgstr "过去六个月"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1737
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "请<a href=\"%1$s\">升级WordPress</a>，或<a href=\"%2$s\">将Akismet插件降级至2.4版本</a>。"

#. translators: 1: Current Akismet version number, 2: Minimum WordPress version
#. number required.
#: class.akismet.php:1735
msgid "Akismet %1$s requires WordPress %2$s or higher."
msgstr "Akismet %1$s 需要 WordPress %2$s 或更高版本。"

#: class.akismet-admin.php:736
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet 在自动重试期间清除了此评论。"

#: class.akismet-admin.php:733
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "经过自动重新检查，Akismet认定这是垃圾评论。"

#. translators: The placeholder is a username.
#: class.akismet-admin.php:727
msgid "%s reported this comment as not spam."
msgstr "%s报告此评论不是垃圾。"

#. translators: The placeholder is a username.
#: class.akismet-admin.php:719
msgid "%s reported this comment as spam."
msgstr "%s报告此评论为垃圾。"

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:784
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s将评论状态修改为%2$s。"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:741
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet未能检查此评论（响应：%s）但将会稍后自动重试。"

#: class.akismet-admin.php:706
msgid "Akismet cleared this comment."
msgstr "Akismet认为这不是垃圾评论。"

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:778
msgid "Comment status was changed to %s"
msgstr "评论状态已更改为 %s"

#: class.akismet-admin.php:700
msgid "Akismet caught this comment as spam."
msgstr "Akismet认为这是垃圾评论。"

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:112
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s条垃圾评论</strong>已被<strong>Akismet</strong>阻挡"

#: class.akismet-widget.php:77
msgid "Title:"
msgstr "标题："

#: class.akismet-widget.php:72 class.akismet-widget.php:94
msgid "Spam Blocked"
msgstr "已阻挡的垃圾评论"

#: class.akismet-widget.php:17
msgid "Display the number of spam comments Akismet has caught"
msgstr "显示 Akismet 捕获的垃圾评论数量"

#: class.akismet-widget.php:16
msgid "Akismet Widget"
msgstr "Akismet 小工具"

#: class.akismet-admin.php:1214
msgid "Cleaning up spam takes time."
msgstr "清除垃圾评论需要时间。"

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1097
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "请检查您的<a href=\"%s\">Akismet配置</a>，如果问题持续存在，请联系您的主机服务提供商。"

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:798
msgid "%s ago"
msgstr "%s前"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:673
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s获准"

#: class.akismet-admin.php:647
msgid "History"
msgstr "历史"

#: class.akismet-admin.php:647 class.akismet-admin.php:655
msgid "View comment history"
msgstr "查看评论历史"

#. translators: %s: Username.
#: class.akismet-admin.php:634
msgid "Un-spammed by %s"
msgstr "由%s取消标记为垃圾"

#. translators: %s: Username.
#: class.akismet-admin.php:631
msgid "Flagged as spam by %s"
msgstr "由%s标记为垃圾"

#: class.akismet-admin.php:625
msgid "Cleared by Akismet"
msgstr "由Akismet核查通过"

#: class.akismet-admin.php:623
msgid "Flagged as spam by Akismet"
msgstr "由Akismet标记为垃圾评论"

#: class.akismet-admin.php:619
msgid "Awaiting spam check"
msgstr "等待垃圾检查"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:749
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Akismet未能重新检查此评论（响应：%s）。"

#: class.akismet-admin.php:703
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet重新检查了该评论，并认为这不是垃圾评论。"

#: class.akismet-admin.php:697
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet重新检查了该评论，并认为这是垃圾评论。"

#: class.akismet-admin.php:507
msgid "Check for Spam"
msgstr "检查垃圾评论"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:449
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "目前，您的 <a href='%s'> 垃圾评论列表 </a> 为空。"

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:438
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "垃圾评论审核队列中有<a href=\"%2$s\">%1$s条评论</a>待审。"

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:430
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a>保护您的博客，使其免受垃圾评论的困扰。"

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:419
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a>已经为您的站点阻挡了%2$s条垃圾评论。"

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:402
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">Akismet</a>为您的站点阻挡了<a href=\"%2$s\">%3$s条垃圾评论</a>。"

#: class.akismet-admin.php:398
msgctxt "comments"
msgid "Spam"
msgstr "垃圾评论"

#: class.akismet-admin.php:325
msgid "Cheatin&#8217; uh?"
msgstr "开玩笑，呵？"

#: class.akismet-admin.php:319
msgid "Akismet Support"
msgstr "Akismet支持"

#: class.akismet-admin.php:318
msgid "Akismet FAQ"
msgstr "Akismet常见问题解答"

#: class.akismet-admin.php:317
msgid "For more information:"
msgstr "需要更多信息："

#: class.akismet-admin.php:308
msgid "The subscription status - active, cancelled or suspended"
msgstr "订阅状态——活跃、取消或暂停"

#: class.akismet-admin.php:308 views/config.php:270
msgid "Status"
msgstr "状态"

#: class.akismet-admin.php:307
msgid "The Akismet subscription plan"
msgstr "Akismet订阅方案"

#: class.akismet-admin.php:307
msgid "Subscription Type"
msgstr "订阅类型"

#: class.akismet-admin.php:304 views/config.php:256
msgid "Account"
msgstr "帐户"

#: class.akismet-admin.php:296
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "选择自动舍弃最严重的垃圾评论，或是始终将所有垃圾评论移至垃圾文件夹。"

#: class.akismet-admin.php:296
msgid "Strictness"
msgstr "严格度"

#: class.akismet-admin.php:295
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "在评论列表页在评论作者名旁显示已获准的评论数量。"

#: class.akismet-admin.php:295 views/config.php:127
msgid "Comments"
msgstr "评论"

#: class.akismet-admin.php:294
msgid "Enter/remove an API key."
msgstr "输入/移除API密钥。"

#: class.akismet-admin.php:294
msgid "API Key"
msgstr "API密钥"

#: class.akismet-admin.php:282 class.akismet-admin.php:293
#: class.akismet-admin.php:306
msgid "Akismet Configuration"
msgstr "Akismet配置"

#: class.akismet-admin.php:272
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "在这页上，您将能够查看您的站点上的垃圾统计。"

#: class.akismet-admin.php:270
msgid "Akismet Stats"
msgstr "Akismet统计"

#: class.akismet-admin.php:259
msgid "Click the Use this Key button."
msgstr "点击“使用此密钥”按钮。"

#: class.akismet-admin.php:258
msgid "Copy and paste the API key into the text field."
msgstr "将API密钥复制粘贴进文本框。"

#: class.akismet-admin.php:256
msgid "If you already have an API key"
msgstr "如果您已经有一个API密钥"

#: class.akismet-admin.php:253
msgid "Enter an API Key"
msgstr "请输入API密钥"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:246
msgid "Sign up for an account on %s to get an API Key."
msgstr "在%s注册账户来得到一个API密钥。"

#: class.akismet-admin.php:244
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "您需要输入API密钥来在您的站点上激活Akismet功能。"

#: class.akismet-admin.php:241
msgid "New to Akismet"
msgstr "首次接触Akismet"

#: class.akismet-admin.php:234
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "在这页上，您将能够设置您的Akismet插件。"

#: class.akismet-admin.php:232 class.akismet-admin.php:243
#: class.akismet-admin.php:255
msgid "Akismet Setup"
msgstr "Akismet设置"

#: class.akismet-admin.php:230 class.akismet-admin.php:268
#: class.akismet-admin.php:280
msgid "Overview"
msgstr "概述"

#: class.akismet-admin.php:199
msgid "Re-adding..."
msgstr "重新加入…"

#: class.akismet-admin.php:198
msgid "(undo)"
msgstr "（撤销）"

#: class.akismet-admin.php:197
msgid "URL removed"
msgstr "URL已移除"

#: class.akismet-admin.php:196
msgid "Removing..."
msgstr "正在移除…"

#: class.akismet-admin.php:195
msgid "Remove this URL"
msgstr "移除此URL"

#: class.akismet-admin.php:105 class.akismet-admin.php:1446
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:126 class.akismet-admin.php:291
#: class.akismet-admin.php:825 views/config.php:79
msgid "Settings"
msgstr "设置"

#: class.akismet-admin.php:101
msgid "Comment History"
msgstr "评论历史"
