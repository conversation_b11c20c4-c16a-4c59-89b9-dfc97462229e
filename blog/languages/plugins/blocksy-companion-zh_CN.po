# Translation of Plugins - Blocksy Companion - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - Blocksy Companion - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-12-18 04:02:42+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - Blocksy Companion - Stable (latest release)\n"

#: framework/extensions/trending/customizer.php:153
msgid "Module Title Icon Source"
msgstr "模块标题图标源"

#: framework/helpers/exts.php:270
msgid "Advanced Reviews"
msgstr "高级评论"

#: framework/helpers/exts.php:262
msgid "Create a customized order “Thank You” page for your customers, giving them a personalized experience."
msgstr "为您的客户创建定制的订单“谢谢”页面，为他们提供个性化的体验。"

#: framework/helpers/exts.php:271
msgid "Enhance your WooCommerce reviews with rich content, images and a thumbs up system that help your shoppers find the perfect product."
msgstr "通过丰富的内容、图像和点赞系统来增强您的 WooCommerce 评论，帮助您的购物者找到完美的产品。"

#: framework/features/conditions/rules/basic.php:6
msgid "All Products"
msgstr "所有产品"

#: framework/helpers/exts.php:243
msgid "Custom Tabs"
msgstr "自定义选项卡"

#: framework/extensions/newsletter-subscribe/providers/demo.php:16
msgid "Demo List"
msgstr "演示列表"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:31
msgctxt "Extension Brand Name"
msgid "Color Mode Switch"
msgstr "颜色模式开关"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:44
msgctxt "Extension Brand Name"
msgid "Custom Fonts"
msgstr "自定义字体"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:56
msgctxt "Extension Brand Name"
msgid "Local Google Fonts"
msgstr "本地谷歌字体"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:142
msgctxt "Extension Brand Name"
msgid "Multiple Sidebars"
msgstr "多个侧边栏"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:162
msgctxt "Extension Brand Name"
msgid "White Label"
msgstr "白色标签"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:87
#: framework/features/blocks/search/options.php:70
msgid "Input Height"
msgstr "输入高度"

#: framework/features/header/items/account/options.php:619
msgid "Link"
msgstr "链接"

#: framework/features/header/items/account/options.php:1939
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/breadcrumbs/Edit.js:66
#: static/js/editor/blocks/contact-info/Edit.js:123
msgid "Link Hover"
msgstr "链接悬停"

#: framework/helpers/exts.php:195
msgid "Preview the available products and let your users make quick and informative decisions about their purchase."
msgstr "预览可用产品，让您的用户快速做出明智的购买决定。"

#: framework/features/conditions/rules/woo.php:42
#: framework/helpers/exts.php:234
msgid "Product Brands"
msgstr "产品品牌"

#: framework/features/conditions/rules/woo.php:75
msgid "Product ID"
msgstr "产品ID"

#: framework/features/conditions/rules/date-time.php:14
#: static/bundle/options.js:15
#: static/js/options/ConditionsManager/ScheduleDate.js:143
msgid "Recurring Days"
msgstr "重复天数"

#: framework/helpers/exts.php:252
msgid "Size Guide"
msgstr "尺寸指南"

#: framework/features/header/items/account/options.php:1927
msgid "Text Initial"
msgstr "文本首字母"

#: framework/features/header/items/account/options.php:12
msgid "User Info"
msgstr "用户信息"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:15
#: framework/extensions/newsletter-subscribe/customizer.php:28
#: framework/extensions/newsletter-subscribe/helpers.php:24
msgid "Enter your email address below and subscribe to our newsletter"
msgstr "请输入您的电子邮件地址进行订阅"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:60
msgid "Form Style"
msgstr "表单样式"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:68
msgid "Stacked"
msgstr "堆叠"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:98
msgid "Fields Gap"
msgstr "字段间距"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:137
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:152
msgid "Your email *"
msgstr "邮箱 *"

#: framework/features/conditions/rules/basic.php:28
msgid "All Singulars"
msgstr "所有详情页"

#: framework/features/conditions/rules/basic.php:35
msgid "All Archives"
msgstr "所有归档"

#: framework/features/conditions/rules/date-time.php:5
msgid "Date & Time"
msgstr "日期＆时间"

#: framework/features/conditions/rules/date-time.php:9
msgid "Range Date/Time"
msgstr "日期/时间范围"

#: framework/features/conditions/rules/requests.php:5
msgid "Requests"
msgstr "请求"

#: framework/features/conditions/rules/requests.php:9
msgid "Request Referer"
msgstr "请求来源"

#: framework/features/conditions/rules/requests.php:14
msgid "Request Cookie"
msgstr "请求 Cookie"

#: framework/features/conditions/rules/requests.php:19
msgid "Request URL"
msgstr "请求网址"

#: framework/features/conditions/rules/specific.php:31
msgid "Post with Author ID"
msgstr "作者文章"

#: framework/features/conditions/rules/woo.php:80
msgid "Product with Taxonomy ID"
msgstr "分类法产品"

#: framework/features/header/items/account/options.php:60
msgid "User Avatar"
msgstr "用户头像"

#: framework/features/header/items/account/options.php:68
#: framework/features/header/items/account/options.php:1999
msgid "Divider"
msgstr "分隔线"

#: framework/features/header/items/account/options.php:91
#: framework/features/header/items/account/options.php:658
#: framework/features/header/items/account/views/login.php:164
#: framework/features/header/items/account/views/login.php:364
msgid "Edit Profile"
msgstr "编辑个人资料"

#: framework/features/header/items/account/options.php:101
#: framework/features/header/items/account/options.php:105
#: framework/features/header/items/account/options.php:667
#: framework/features/header/items/account/views/login.php:170
#: framework/features/header/items/account/views/login.php:385
msgid "Log Out"
msgstr "登出"

#: framework/features/header/items/account/options.php:185
#: framework/features/header/items/account/options.php:189
#: framework/features/header/items/account/views/login.php:553
msgid "Dokan Dashboard"
msgstr "Dokan 仪表盘"

#: framework/features/header/items/account/options.php:199
#: framework/features/header/items/account/options.php:203
#: framework/features/header/items/account/views/login.php:587
msgid "Dokan Shop"
msgstr "Dokan 商店"

#: framework/features/header/items/account/options.php:215
#: framework/features/header/items/account/options.php:219
#: framework/features/header/items/account/views/login.php:619
msgid "Tutor LMS Dashboard"
msgstr "Tutor LMS 仪表板"

#: framework/features/header/items/account/options.php:235
#: framework/features/header/items/account/views/login.php:646
msgid "bbPress Dashboard"
msgstr "bbPress 仪表盘"

#: framework/features/header/items/account/options.php:247
msgid "Content Block"
msgstr "内容块"

#: framework/features/header/items/account/options.php:254
#: framework/features/header/items/account/options.php:262
msgid "Select Content Block"
msgstr "选择内容块"

#: framework/features/header/items/account/options.php:257
msgid "Create a new content Block/Hook"
msgstr "创建新内容块/挂钩"

#: framework/features/header/items/account/options.php:618
msgid "Dropdown"
msgstr "下拉菜单"

#: framework/features/header/items/account/options.php:630
msgid "Dropdown Items"
msgstr "下拉项"

#: framework/features/header/items/account/options.php:687
msgid "Link To"
msgstr "链接到"

#: framework/features/header/items/account/options.php:1890
msgid "Dropdown Options"
msgstr "下拉选项"

#: framework/features/header/items/account/options.php:1933
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/breadcrumbs/Edit.js:60
#: static/js/editor/blocks/contact-info/Edit.js:114
msgid "Link Initial"
msgstr "链接首字母"

#: framework/features/header/items/account/options.php:2034
msgid "Border Radius"
msgstr "边框半径"

#: framework/features/header/items/account/views/login.php:516
#: framework/helpers/exts.php:210
msgid "Wishlist"
msgstr "收藏夹"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:7
msgctxt "Extension Brand Name"
msgid "Adobe Fonts"
msgstr "Adobe 字体"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:18
msgctxt "Extension Brand Name"
msgid "Custom Code Snippets"
msgstr "自定义代码段"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:19
msgid "Inject custom code snippets throughout your website. The extension works globally or on a per post/page basis."
msgstr "在整个网站中注入自定义代码片段。该扩展在全局范围内或在每个文章/页面的基础上工作。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:32
msgid "Add a dark colour scheme and switch to your website, which will make it pleasant to look at in low light environments."
msgstr "添加深色配色方案并切换到您的网站，这将使您在弱光环境下看起来赏心悦目。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:57
msgid "Serve your chosen Google Fonts from your own web server. This will increase the loading speed and makes sure your website complies with the privacy regulations."
msgstr "从您自己的网络服务器提供您选择的 Google 字体。这将提高加载速度并确保您的网站符合隐私规定。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:71
msgctxt "Extension Brand Name"
msgid "Advanced Menu"
msgstr "高级菜单"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:72
msgid "Create beautiful personalised menus that set your website apart from the others. Add icons and badges to your entries and even add Content Blocks inside your dropdowns."
msgstr "创建精美的个性化菜单，让您的网站与众不同。为您的条目添加图标和徽章，甚至在下拉列表中添加内容块。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:84
msgctxt "Extension Brand Name"
msgid "Post Types Extra"
msgstr "文章类型增强"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:85
msgid "Enables support for Custom Fields inside archive cards and single page post title, adds a reading progress bar for your posts and lets you set featured images and colors for your categories archives."
msgstr "启用对存档卡和单页文章标题中的自定义字段的支持，为您的文章添加阅读进度条，并允许您为分类存档设置特色图像和颜色。"

#: framework/helpers/exts.php:90
msgid "Read Time"
msgstr "阅读时间"

#: framework/helpers/exts.php:91
msgid "Display the approximate reading time of an article, so that visitors know what to expect when starting to read the content."
msgstr "显示文章的大概阅读时间，以便访问者知道开始阅读内容时会发生什么。"

#: framework/helpers/exts.php:97 static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/dynamic-data/index.js:16
#: static/js/editor/blocks/dynamic-data/index.js:100
msgid "Dynamic Data"
msgstr "动态数据"

#: framework/helpers/exts.php:98
msgid "Integrates custom fields solutions in the meta layers of a post and presents additional information."
msgstr "将自定义字段解决方案集成到文章的元层中并提供附加信息。"

#: framework/helpers/exts.php:104
msgid "Posts Filter"
msgstr "文章过滤器"

#: framework/helpers/exts.php:105
msgid "Let your guests easily filter the posts by their category or tags taxonomy terms, instantly drilling down the listings."
msgstr "让您的客人轻松地按分类或标签分类术语过滤文章，立即深入查看列表。"

#: framework/helpers/exts.php:111
msgid "Taxonomy Customisations"
msgstr "分类法定制"

#: framework/helpers/exts.php:112
msgid "Additional customisation options for your taxonomies such as hero backgrounds and custom colour labels."
msgstr "分类法的其他自定义选项，例如英雄背景和自定义颜色标签。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:127
msgctxt "Extension Brand Name"
msgid "Shortcuts Bar"
msgstr "快捷键工具条"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:128
msgid "Easily turn your websites into mobile first experiences. You can easily add the most important actions at the bottom of the screen for easy access."
msgstr "轻松将您的网站转变为移动优先体验。您可以在屏幕底部轻松添加最重要的操作按钮，以便于访问。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:143
msgid "Create unlimited personalized sets of widget areas and display them on any page or post using our conditional logic functionality."
msgstr "创建无限的个性化小工具区域集，并使用我们的条件逻辑功能在任何页面或文章上显示它们。"

#: framework/helpers/exts.php:147
msgid "Create New Sidebar"
msgstr "新建侧边栏"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:175
msgctxt "Extension Brand Name"
msgid "Shop Extra"
msgstr "WooCommerce 增强"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:176
msgid "Make the shopping experience better for your visitors! Add features such as Product Quick View, Wishlist functionality and a Floating Add to Cart button. Customize the single product gallery/slider and the layout."
msgstr "为您的访客提供更好的购物体验！添加产品快速查看、愿望清单功能和浮动添加到购物车按钮等功能。自定义单个产品库/滑块和布局。"

#: framework/helpers/exts.php:187
msgid "Adds the “add to cart” actions to the product page as a floating bar if the product summary has disappeared from view."
msgstr "如果产品摘要从视图中消失，则将“添加到购物车”操作作为浮动栏添加到产品页面。"

#: framework/helpers/exts.php:202
msgid "Filters"
msgstr "过滤器"

#: framework/helpers/exts.php:203
msgid "Drill down the product list with new filtering widgets, an off canvas area for them and showing the active filters on the page."
msgstr "使用新的过滤小工具、画布外区域以及在页面上显示活动过滤器来深入了解产品列表。"

#: framework/helpers/exts.php:211
msgid "A set of features that lets you create easily your dream products wishlists and share them with friends and family."
msgstr "一组功能可让您轻松创建梦想产品愿望清单并与朋友和家人分享。"

#: framework/helpers/exts.php:218
msgid "Compare View"
msgstr "比较视图"

#: framework/helpers/exts.php:294
msgid "Product Share Box"
msgstr "产品分享框"

#: framework/helpers/exts.php:295
msgid "Enable social sharing abilities for products available on the site, letting even more users discover your great shop selection."
msgstr "为网站上提供的产品启用社交共享功能，让更多用户发现您精选的商店。"

#: framework/helpers/exts.php:286
msgid "Advanced Gallery"
msgstr "高级相册"

#: framework/helpers/exts.php:287
msgid "Replace the standard product gallery with additional layouts which can showcase the photos as a grid or even a slider."
msgstr "用额外的布局替换标准产品库，这些布局可以将照片显示为网格甚至滑块。"

#: framework/helpers/exts.php:302
msgid "Search by SKU"
msgstr "按SKU搜索"

#: framework/helpers/exts.php:303
msgid "Advanced searching for products by their SKU classification can be useful in cases of vast product catalogues."
msgstr "在产品目录庞大的情况下，按 SKU 分类对产品进行高级搜索非常有用。"

#: framework/helpers/exts.php:279
msgid "Add a visual cue that tells your visitors how much the cart total must be to be able to benefit of free shipping."
msgstr "添加视觉提示，告诉访问者购物车总数必须达到多少才能享受免费送货。"

#: framework/helpers/exts.php:226
msgid "Variation Swatches"
msgstr "变化色板"

#: framework/helpers/exts.php:227
msgid "Catch the attention of your clients by showcasing your product variations as colour, image or button swatches."
msgstr "通过以颜色、图像或按钮样本展示您的产品变体来吸引客户的注意力。"

#: framework/helpers/exts.php:235
msgid "Categorise products by brands and show their logo in archive or single page so users could discover more about their makers."
msgstr "按品牌对产品进行分类，并在存档或单页中显示其徽标，以便用户可以更多地了解其制造商。"

#: framework/helpers/exts.php:309
msgid "Affiliate Product Links"
msgstr "联盟推广产品链接"

#: framework/helpers/exts.php:310
msgid "Better management for affiliate products with a few simple options that strengthen the external integration with these."
msgstr "通过一些简单的选项可以更好地管理联属产品，从而加强与这些产品的外部集成。"

#: framework/helpers/exts.php:244
msgid "Present additional information about your products by adding new custom tabs to the product information section."
msgstr "通过向产品信息部分添加新的自定义选项卡来显示有关您产品的其他信息。"

#: framework/helpers/exts.php:253
msgid "Show a size chart guide so that your visitors can pick the right size for them when ordering a product."
msgstr "显示尺码表指南，以便您的访客在订购产品时可以选择适合他们的尺码。"

#: framework/helpers/exts.php:261
msgid "Custom Thank You Pages"
msgstr "自定义感谢页面"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:163
msgid "Replace Blocksy's branding with your own. Easily hide licensing info and other sections of the theme and companion plugin from your clients and make your final product look more professional."
msgstr "用你自己的品牌取代Blocksy品牌。轻松隐藏授权信息和其他主题和配套插件，让客户了解最终产品，使您的最终产品看起来更加专业。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:45
msgid "Upload an unlimited number of custom fonts or variable fonts and use them throughout Blocksy and your favorite page builder."
msgstr "上传无限数量的自定义字体或可变字体，并在整个 Blocksy 和您最喜欢的页面构建器中使用它们。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts.php:8
msgid "Connect your Adobe Fonts project and use the selected fonts throughout Blocksy and your favorite page builder."
msgstr "连接您的 Adobe Fonts 项目并在整个 Blocksy 和您最喜欢的页面构建器中使用选定的字体。"

#: framework/features/blocks/share-box/options.php:25
#: framework/theme-integration.php:221
msgid "X (Twitter)"
msgstr "推特"

#: framework/extensions/trending/customizer.php:401
msgid "Module Title Font"
msgstr "模块标题字体"

#: framework/extensions/trending/customizer.php:460
msgid "Posts Title Font"
msgstr "文章标题字体"

#: framework/extensions/trending/customizer.php:469
msgid "Posts Title Font Color"
msgstr "文章标题字体颜色"

#: framework/extensions/trending/customizer.php:409
msgid "Module Title Color"
msgstr "模块标题名称颜色"

#: framework/extensions/trending/customizer.php:500
msgid "Arrows Color"
msgstr "箭头颜色"

#: framework/theme-integration.php:234
msgid "Mastodon"
msgstr "Mastodon"

#: framework/extensions/cookies-consent/extension.php:106
msgid "Comment Submission Failure"
msgstr "评论提交失败"

#: framework/extensions/cookies-consent/extension.php:105
msgid "Please accept the Privacy Policy in order to comment."
msgstr "请接受隐私政策以便发表评论。"

#: framework/dashboard.php:34
#: framework/features/header/items/account/options.php:23
#: framework/features/header/items/account/options.php:73
#: framework/features/header/items/account/options.php:77
#: framework/features/header/items/account/options.php:649
#: framework/features/header/items/account/views/login.php:158
#: framework/features/header/items/account/views/login.php:335
msgid "Dashboard"
msgstr "仪表盘"

#: framework/theme-integration.php:91
msgid "Please enter the same value again."
msgstr "请再次输入相同数值。"

#: framework/theme-integration.php:89
msgid "Please enter only digits."
msgstr "请只输入数字。"

#: framework/theme-integration.php:87
msgid "Please enter a valid date (ISO)."
msgstr "请输入有效日期（ISO）。"

#: framework/theme-integration.php:85
msgid "Please enter a valid URL."
msgstr "请输入有效网址。"

#: framework/theme-integration.php:82
msgid "This field is required"
msgstr "这是必填栏"

#: framework/theme-integration.php:83
msgid "Please fix this field."
msgstr "请修复此字段."

#: framework/theme-integration.php:84
msgid "Please enter a valid email address."
msgstr "请输入有效的电子邮件地址。"

#: framework/theme-integration.php:86
msgid "Please enter a valid date."
msgstr "请输入一个有效的日期。"

#: framework/theme-integration.php:88
msgid "Please enter a valid number."
msgstr "请输入有效的号码。"

#: framework/theme-integration.php:90
msgid "Please enter a valid credit card number."
msgstr "请输入一个有效的信用卡号码。"

#: framework/theme-integration.php:92
msgid "Please enter no more than {0} characters."
msgstr "请输入不超过{0}个字符。"

#: framework/theme-integration.php:93
msgid "Please enter at least {0} characters."
msgstr "请输入至少{0}个字符。"

#: framework/theme-integration.php:94
msgid "Please enter a value between {0} and {1} characters long."
msgstr "请输入介于{0}和{1}个字符之间的值。"

#: framework/theme-integration.php:95
msgid "Please enter a value between {0} and {1}."
msgstr "请输入介于{0}和{1}之间的值。"

#: framework/theme-integration.php:96
msgid "Please enter a value less than or equal to {0}."
msgstr "请输入小于或等于{0}的值。"

#: framework/theme-integration.php:97
msgid "Please enter a value greater than or equal to {0}."
msgstr "请输入大于或等于{0}的值。"

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:106
msgid "Check your email for the confirmation link, then visit the %slogin page%s."
msgstr "查看电子邮件中的确认链接，然后访问 %s 登录页面%s。"

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:226
msgid "Your account was created successfully. Your login details have been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "您的帐户已成功创建。您的登录信息已发送到您的电子邮箱。请访问 %1$s 登录页面%2$s。"

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:238
msgid "Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "您的帐户已成功创建，密码已发送到您的电子邮箱。请访问 %1$s 登录页面%2$s。"

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:253
msgid "Registration complete. Please check your email, then visit the %1$slogin page%2$s."
msgstr "注册完成。请检查您的电子邮件，然后访问 %1$s 登录页面%2$s。"

#: framework/dashboard.php:462
msgid "You do not have sufficient permissions to access this page."
msgstr "您没有足够的权限访问此页面。"

#: framework/theme-integration.php:69
msgctxt "password mismatch"
msgid "Mismatch"
msgstr "不匹配"

#: framework/theme-integration.php:66
msgctxt "password strength"
msgid "Weak"
msgstr "弱"

#: framework/theme-integration.php:64
msgctxt "password strength"
msgid "Password strength unknown"
msgstr "密码强度未知"

#: framework/features/header/modal/register.php:76
msgid "A link to set a new password will be sent to your email address."
msgstr "设置新密码的链接将被发送到你的电子邮件地址。"

#: framework/theme-integration.php:53
msgid "Please enter a stronger password."
msgstr "请输入一个复杂点的密码。"

#: framework/theme-integration.php:65
msgctxt "password strength"
msgid "Very weak"
msgstr "很弱"

#: framework/theme-integration.php:67
msgctxt "password strength"
msgid "Medium"
msgstr "中"

#: framework/theme-integration.php:68
msgctxt "password strength"
msgid "Strong"
msgstr "很强"

#: framework/features/conditions/rules/pages.php:46
msgid "Author Archives"
msgstr "作者档案"

#: framework/extensions/newsletter-subscribe/helpers.php:142
msgid "First name"
msgstr "名字"

#: framework/features/conditions/rules/pages.php:39
msgid "Privacy Policy Page"
msgstr "隐私策略页"

#: framework/features/blocks/contact-info/options.php:83
#: framework/features/blocks/contact-info/options.php:146
#: framework/features/blocks/contact-info/options.php:207
#: framework/features/blocks/contact-info/options.php:268
#: framework/features/blocks/contact-info/options.php:329
#: framework/features/blocks/contact-info/options.php:390
#: framework/features/blocks/contact-info/options.php:451
#: framework/features/header/items/account/options.php:375
#: framework/features/header/items/account/options.php:756
msgid "Icon Source"
msgstr "图标来源"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:5
#: framework/extensions/cookies-consent/customizer.php:5
msgctxt "Extension Brand Name"
msgid "Cookies Consent"
msgstr "同意Cookies"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:5
msgctxt "Extension Brand Name"
msgid "Newsletter Subscribe"
msgstr "邮件订阅"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:5
msgctxt "Extension Brand Name"
msgid "Product Reviews"
msgstr "产品评论"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:5
#: framework/extensions/trending/customizer.php:97
msgctxt "Extension Brand Name"
msgid "Trending Posts"
msgstr "热门文章"

#: framework/extensions/trending/customizer.php:307
#: framework/features/blocks/about-me/options.php:81
msgid "Image Size"
msgstr "图片大小"

#: framework/features/header/header-options.php:68
msgid "Effect"
msgstr "效果"

#: framework/features/header/header-options.php:84
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:46
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:73
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:42
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:69
msgid "Offset"
msgstr "偏移"

#: framework/extensions/newsletter-subscribe/providers/brevo.php:116
#: framework/extensions/newsletter-subscribe/providers/campaign-monitor.php:136
#: framework/extensions/newsletter-subscribe/providers/convertkit.php:97
#: framework/extensions/newsletter-subscribe/providers/demo.php:40
#: framework/extensions/newsletter-subscribe/providers/mailerlite-classic.php:104
#: framework/extensions/newsletter-subscribe/providers/mailerlite-new.php:123
msgid "Thank you for subscribing to our newsletter!"
msgstr "感谢您订阅我们的通讯！"

#: framework/extensions/cookies-consent/customizer.php:199
msgid "Accept Button"
msgstr "接受按钮"

#: framework/extensions/cookies-consent/customizer.php:85
msgid "Decline Button text"
msgstr "拒绝按钮文本"

#: framework/extensions/cookies-consent/customizer.php:76
msgid "Accept Button text"
msgstr "接受按钮文字"

#: framework/extensions/cookies-consent/customizer.php:88
#: framework/extensions/cookies-consent/helpers.php:11
msgid "Decline"
msgstr "拒绝"

#: framework/extensions/cookies-consent/customizer.php:147
#: framework/extensions/newsletter-subscribe/customizer.php:196
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/about-me/Edit.js:89
#: static/js/editor/blocks/breadcrumbs/Edit.js:43
#: static/js/editor/blocks/contact-info/Edit.js:96
msgid "Text Color"
msgstr "文字颜色"

#: framework/extensions/cookies-consent/customizer.php:266
msgid "Decline Button"
msgstr "“拒绝”按钮"

#: framework/extensions/product-reviews/metabox.php:60
msgid "Please note that some of this information (price, sku, brand) won't be displayed on the front-end. It is solely used for Google's Schema.org markup."
msgstr "请注意，其中一些信息（价格、SKU、品牌）不会显示在前端。它仅用于Google的 Schema.org 标记。"

#: framework/features/header/account-modal.php:25
msgid "Close account modal"
msgstr "关闭账户模式"

#: framework/features/header/items/account/options.php:306
msgid "Customizing: Logged out State"
msgstr "自定义：注销状态"

#: framework/features/header/items/account/options.php:1088
msgid "User Visibility"
msgstr "用户可见性"

#: framework/features/header/items/account/options.php:1099
msgid "Logged In"
msgstr "登录"

#: framework/features/header/items/account/options.php:1100
msgid "Logged Out"
msgstr "注销"

#: framework/features/account-auth.php:136
msgid "Lost Password"
msgstr "忘记密码"

#: framework/features/account-auth.php:87
msgid "<strong>Error</strong>: %s"
msgstr "<strong>错误</strong>: %s"

#: framework/features/account-auth.php:137
msgid "Please enter your username or email address. You will receive an email message with instructions on how to reset your password."
msgstr "请输入您的用户名或电子邮件地址。您将收到一封电子邮件，其中包含有关如何重置密码的说明。"

#: framework/features/header/header-options.php:209
#: framework/features/header/header-options.php:236
#: framework/features/header/header-options.php:251
#: framework/features/header/header-options.php:266
#: framework/features/header/items/account/options.php:1733
msgid "Background"
msgstr "背景"

#: framework/features/header/items/account/options.php:1724
msgid "Close Button Type"
msgstr "关闭按钮类型"

#: framework/features/header/items/account/options.php:1732
msgid "Border"
msgstr "边框"

#: framework/features/header/items/account/options.php:1778
msgid "Border Color"
msgstr "边框颜色"

#: framework/extensions/product-reviews/metabox.php:126
msgid "Sponsored Attribute"
msgstr "赞助属性"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:19
#: framework/features/blocks/about-me/options.php:67
msgid "You can add here some arbitrary HTML code."
msgstr "您可以在此处添加一些任意的 HTML 代码。"

#: framework/extensions/newsletter-subscribe/customizer.php:175
msgid "Title Color"
msgstr "标题颜色"

#: framework/extensions/newsletter-subscribe/customizer.php:373
msgid "Container Border"
msgstr "容器边框"

#: framework/extensions/newsletter-subscribe/customizer.php:389
msgid "Container Shadow"
msgstr "容器阴影"

#: framework/extensions/newsletter-subscribe/customizer.php:422
msgid "Container Border Radius"
msgstr "集装箱边界半径"

#: framework/extensions/product-reviews/metabox.php:18
msgid "Product"
msgstr "产品"

#: framework/extensions/product-reviews/metabox.php:69
msgid "Product Price"
msgstr "产品价格"

#: framework/extensions/product-reviews/metabox.php:76
msgid "Product SKU"
msgstr "产品SKU"

#: framework/extensions/product-reviews/metabox.php:83
msgid "Product Brand"
msgstr "产品品牌"

#: framework/features/conditions/rules/cpt.php:40
msgid "%s %s Taxonomy"
msgstr "%s %s 分类法"

#: framework/extensions/trending/customizer.php:124
msgid "Module Title Tag"
msgstr "模块标题标签"

#: framework/extensions/product-reviews/metabox.php:10
msgid "Review Entity"
msgstr "评价实体"

#: framework/extensions/product-reviews/metabox.php:19
msgid "Book"
msgstr "书籍"

#: framework/extensions/product-reviews/metabox.php:21
msgid "Creative Work Season"
msgstr "创意工作季节"

#: framework/extensions/product-reviews/metabox.php:22
msgid "Creative Work Series"
msgstr "创意工作系列"

#: framework/extensions/product-reviews/metabox.php:23
msgid "Episode"
msgstr "剧集"

#: framework/extensions/product-reviews/metabox.php:25
msgid "Game"
msgstr "游戏"

#: framework/extensions/product-reviews/metabox.php:27
msgid "Local Business"
msgstr "本地业务"

#: framework/extensions/product-reviews/metabox.php:28
msgid "Media Object"
msgstr "媒体对象"

#: framework/extensions/product-reviews/metabox.php:29
msgid "Movie"
msgstr "电影"

#: framework/extensions/product-reviews/metabox.php:30
msgid "Music Playlist"
msgstr "音乐播放列表"

#: framework/extensions/product-reviews/metabox.php:31
msgid "Music Recording"
msgstr "音乐录音"

#: framework/extensions/product-reviews/metabox.php:32
msgid "Organization"
msgstr "组织"

#: framework/extensions/product-reviews/metabox.php:38
msgid "More info about review entity and how to choose the right one can be found %shere%s."
msgstr "有关评价实体以及如何选择正确实体的更多信息，请访问%s这里%s。"

#: framework/extensions/product-reviews/metabox.php:170
msgid "Short Description"
msgstr "简短描述"

#: framework/extensions/product-reviews/extension.php:256
#: framework/extensions/product-reviews/extension.php:427
msgid "Star Rating Color"
msgstr "星级颜色"

#: framework/extensions/product-reviews/extension.php:274
#: framework/extensions/product-reviews/extension.php:444
#: framework/extensions/product-reviews/extension.php:472
#: framework/extensions/product-reviews/extension.php:493
#: framework/features/header/items/account/options.php:1985
#: static/bundle/dashboard.js:7
#: static/js/dashboard/screens/Extensions/Sidebar.js:120
msgid "Active"
msgstr "激活"

#: framework/extensions/product-reviews/extension.php:280
#: framework/extensions/product-reviews/extension.php:450
msgid "Inactive"
msgstr "未激活"

#: framework/extensions/product-reviews/extension.php:376
msgid "Review Summary"
msgstr "评论摘要"

#: framework/extensions/product-reviews/extension.php:387
msgid "Scores Box Width"
msgstr "分数框宽度"

#: framework/extensions/product-reviews/extension.php:408
msgid "Buy Now Button"
msgstr "立即购买按钮"

#: framework/extensions/product-reviews/extension.php:458
msgid "Overll Score Text"
msgstr "评分文本"

#: framework/extensions/product-reviews/extension.php:480
msgid "Overll Score Background"
msgstr "分数背景"

#: framework/extensions/product-reviews/extension.php:397
msgid "Read More Button"
msgstr "查看全文按钮"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:6
msgid "Display a cookie acceptance box in order to comply with the privacy regulations in your country."
msgstr "显示 cookie 接受框以遵守您所在国家/地区的隐私法规。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:6
msgid "Easily capture new leads for your newsletter with the help of a widget, shortcode or even a block inserted on your pages or posts."
msgstr "借助小工具、短码甚至插入到您的页面或文章中的块，轻松捕获时事通讯的新潜在客户。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:6
msgid "This extension lets you easily create an affiliate marketing type of website by giving you options to create a personalized product review and use your affiliate links to direct your readers to the purchase page."
msgstr "此扩展程序为您提供创建个性化产品评论的选项，并使用您的会员链接将您的读者引导至购买页面，从而让您轻松创建会员营销类型的网站。"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:6
msgid "Highlight your most popular posts or products based on the number of comments or reviews they have gotten in the specified period of time."
msgstr "根据他们在指定时间段内获得的评论或评论数量突出显示您最受欢迎的文章或产品。"

#: framework/features/google-analytics.php:109
msgid "Link your Google Analytics 4 tracking ID. More info and instructions can be found %shere%s."
msgstr "关联您的 Google Analytics 4 跟踪 ID。可以在%s此处%s找到更多信息和说明。"

#: framework/features/opengraph-meta-data.php:17
msgid "Enable the OpenGraph rich meta data features for your website."
msgstr "为您的网站启用 OpenGraph 丰富的元数据功能。"

#: framework/extensions/cookies-consent/customizer.php:119
msgid "I accept the %sPrivacy Policy%s*"
msgstr "我接受%s隐私政策%s*"

#: framework/extensions/newsletter-subscribe/customizer.php:227
#: framework/features/header/items/account/options.php:1553
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:128
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/search/Edit.js:196
msgid "Input Font Color"
msgstr "输入字体颜色"

#: framework/extensions/newsletter-subscribe/customizer.php:259
#: framework/features/header/items/account/options.php:1588
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:159
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/search/Edit.js:223
msgid "Input Border Color"
msgstr "输入边框颜色"

#: framework/extensions/newsletter-subscribe/customizer.php:296
#: framework/features/header/items/account/options.php:1631
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:190
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/search/Edit.js:250
msgid "Input Background Color"
msgstr "输入背景颜色"

#: framework/extensions/newsletter-subscribe/customizer.php:251
#: framework/extensions/newsletter-subscribe/customizer.php:282
#: framework/extensions/newsletter-subscribe/customizer.php:318
#: framework/features/header/items/account/options.php:1579
#: framework/features/header/items/account/options.php:1617
#: framework/features/header/items/account/options.php:1663
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:146
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:177
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:210
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/search/Edit.js:213
#: static/js/editor/blocks/search/Edit.js:240
#: static/js/editor/blocks/search/Edit.js:269
msgid "Focus"
msgstr "焦点"

#: framework/features/conditions/rules/bbPress.php:11
#: framework/features/header/items/account/options.php:231
msgid "bbPress"
msgstr "bbPress"

#: framework/features/conditions/rules/bbPress.php:15
#: framework/features/header/items/account/options.php:24
#: framework/features/header/items/account/options.php:87
msgid "Profile"
msgstr "个人资料"

#: framework/extensions/trending/customizer.php:116
msgid "Module Title"
msgstr "模块标题"

#: framework/features/header/items/account/options.php:1514
msgid "Modal Options"
msgstr "模态选项"

#: framework/features/header/items/account/options.php:1677
msgid "Modal Background"
msgstr "弹窗背景"

#: framework/features/header/items/account/options.php:1705
msgid "Modal Shadow"
msgstr "模态阴影"

#: framework/extensions/product-reviews/metabox.php:120
msgid "Open Link In New Tab"
msgstr "在新选项卡中打开链接"

#: framework/extensions/trending/customizer.php:350
msgid "Display Location"
msgstr "显示位置"

#: framework/extensions/trending/customizer.php:358
msgid "Before Footer"
msgstr "页脚之前"

#: framework/extensions/trending/customizer.php:363
msgid "After Footer"
msgstr "页脚之后"

#: framework/extensions/trending/customizer.php:368
msgid "After Header"
msgstr "页眉之后"

#: framework/extensions/trending/customizer.php:385
msgid "Trending Block Display Conditions"
msgstr "趋势块显示条件"

#: framework/extensions/trending/customizer.php:386
msgid "Add one or more conditions to display the trending block."
msgstr "添加一个或多个条件以显示趋势块。"

#: framework/features/header/items/account/options.php:169
#: framework/features/header/items/account/options.php:284
#: framework/features/header/items/account/options.php:285
msgid "WooCommerce Account"
msgstr "WooCommerce帐户"

#: framework/features/account-auth.php:273
msgid "Registration Form"
msgstr "注册表单"

#: framework/features/account-auth.php:119
#: framework/features/account-auth.php:267
msgid "Check your email"
msgstr "查看你的电子邮件"

#: framework/features/account-auth.php:274
msgid "Register For This Site"
msgstr "注册"

#: framework/features/conditions-manager.php:299
msgid "Language"
msgstr "语言"

#: framework/features/conditions/rules/localization.php:11
msgid "Languages"
msgstr "语言"

#: framework/features/conditions/rules/localization.php:15
msgid "Current Language"
msgstr "当前语言"

#: framework/features/header/items/account/options.php:340
#: framework/features/header/items/account/options.php:739
msgid "Account Image"
msgstr "帐户图像"

#: framework/features/header/items/account/options.php:554
#: framework/features/header/items/account/options.php:1017
msgid "Label Position"
msgstr "标签位置"

#: framework/features/header/items/account/options.php:563
#: framework/features/header/items/account/options.php:1035
msgid "Bottom"
msgstr "底部"

#: framework/features/header/items/account/options.php:570
msgid "Label Type"
msgstr "标签类型"

#: framework/features/header/items/account/options.php:1125
msgid "Label Font"
msgstr "标签字体"

#: framework/features/header/items/account/options.php:1135
#: framework/features/header/items/account/options.php:1174
#: framework/features/header/items/account/options.php:1218
#: framework/features/header/items/account/options.php:1260
msgid "Label Color"
msgstr "标签颜色"

#: framework/features/header/items/account/options.php:2059
msgid "Element Visibility"
msgstr "元素可见性"

#: framework/features/header/items/account/options.php:587
#: framework/features/header/items/account/options.php:1045
msgid "Label Text"
msgstr "标签文本"

#: framework/features/header/items/account/options.php:1327
#: framework/features/header/items/account/options.php:1366
#: framework/features/header/items/account/options.php:1409
#: framework/features/header/items/account/options.php:1450
#: framework/features/header/items/account/options.php:1738
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/share-box/Edit.js:81
#: static/js/editor/blocks/socials/Edit.js:81
msgid "Icon Color"
msgstr "图标颜色"

#: framework/extensions/product-reviews/extension.php:540
#: framework/extensions/product-reviews/extension.php:541
#: framework/extensions/product-reviews/extension.php:544
#: framework/extensions/product-reviews/extension.php:546
msgid "Product Reviews"
msgstr "产品评论"

#: framework/extensions/product-reviews/extension.php:545
msgid "Product Review"
msgstr "商品评价"

#: framework/extensions/product-reviews/extension.php:547
msgid "Parent Product Review"
msgstr "父级产品评价"

#: framework/extensions/product-reviews/extension.php:548
msgid "All Reviews"
msgstr "所有评论"

#: framework/extensions/product-reviews/extension.php:549
msgid "View Product Review"
msgstr "查看产品评价"

#: framework/extensions/product-reviews/extension.php:550
msgid "Add New Product Review"
msgstr "添加新产品评价"

#: framework/extensions/product-reviews/extension.php:551
msgid "Add New Review"
msgstr "添加新评论"

#: framework/extensions/product-reviews/extension.php:552
msgid "Edit Product Review"
msgstr "编辑产品评价"

#: framework/extensions/product-reviews/extension.php:553
msgid "Update Product Review"
msgstr "更新产品评价"

#: framework/extensions/product-reviews/extension.php:554
msgid "Search Product Review"
msgstr "搜索产品评价"

#: framework/features/header/items/account/options.php:7
#: framework/features/header/items/account/options.php:36
#: framework/features/header/items/account/options.php:117
#: framework/features/header/items/account/options.php:280
#: framework/features/header/items/account/views/login.php:396
msgid "Custom Link"
msgstr "自定义链接"

#: framework/features/header/items/account/options.php:279
msgid "Modal"
msgstr "弹窗"

#: framework/features/header/items/account/options.php:295
msgid "Customizing: Logged in State"
msgstr "自定义：登录状态"

#: framework/features/header/items/account/options.php:319
msgid "Logged In Options"
msgstr "登录选项"

#: framework/features/header/items/account/options.php:324
msgid "Logged Out Options"
msgstr "注销选项"

#: framework/features/header/items/account/options.php:611
#: framework/features/header/items/account/options.php:1059
msgid "Account Action"
msgstr "帐户操作"

#: framework/features/header/items/account/options.php:467
#: framework/features/header/items/account/options.php:841
#: framework/features/header/items/account/options.php:929
msgid "Type 3"
msgstr "类型3"

#: framework/features/header/items/account/options.php:477
#: framework/features/header/items/account/options.php:851
#: framework/features/header/items/account/options.php:939
msgid "Type 4"
msgstr "类型4"

#: framework/features/header/items/account/options.php:487
#: framework/features/header/items/account/options.php:861
#: framework/features/header/items/account/options.php:949
msgid "Type 5"
msgstr "类型5"

#: framework/features/header/items/account/options.php:497
#: framework/features/header/items/account/options.php:871
#: framework/features/header/items/account/options.php:959
msgid "Type 6"
msgstr "类型6"

#: framework/features/header/items/account/options.php:561
#: framework/features/header/items/account/options.php:1027
msgid "Left"
msgstr "左"

#: framework/features/header/items/account/options.php:562
#: framework/features/header/items/account/options.php:1031
msgid "Right"
msgstr "右"

#: framework/extensions/product-reviews/extension.php:318
#: framework/extensions/product-reviews/views/single-top.php:139
msgid "Overall Score"
msgstr "总体得分"

#: framework/extensions/product-reviews/extension.php:555
msgid "Not Found"
msgstr "未找到"

#: framework/extensions/product-reviews/extension.php:556
msgid "Not found in Trash"
msgstr "在回收站中找不到"

#: framework/extensions/product-reviews/extension.php:590
#: framework/extensions/product-reviews/extension.php:600
msgid "Categories"
msgstr "分类"

#: framework/extensions/product-reviews/extension.php:592
msgid "Search Category"
msgstr "搜索分类"

#: framework/extensions/product-reviews/extension.php:593
#: static/bundle/dashboard.js:7
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:60
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:65
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:88
msgid "All Categories"
msgstr "全部"

#: framework/extensions/product-reviews/extension.php:594
msgid "Parent Category"
msgstr "父分类"

#: framework/extensions/product-reviews/extension.php:595
msgid "Parent Category:"
msgstr "父分类："

#: framework/extensions/product-reviews/extension.php:596
msgid "Edit Category"
msgstr "编辑分类"

#: framework/extensions/product-reviews/extension.php:597
msgid "Update Category"
msgstr "更新分类"

#: framework/extensions/product-reviews/extension.php:598
msgid "Add New Category"
msgstr "添加新分类"

#: framework/extensions/product-reviews/extension.php:599
msgid "New Category Name"
msgstr "新分类名称"

#. Translators: %s is the theme name.
#: framework/extensions/product-reviews/extension.php:618
msgid "%s Settings"
msgstr "%s 设置"

#: framework/extensions/product-reviews/helpers.php:30
#: framework/extensions/product-reviews/metabox.php:179
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:131
msgid "Rating"
msgstr "评分"

#: framework/extensions/product-reviews/metabox.php:96
msgid "Gallery"
msgstr "相册"

#: framework/extensions/product-reviews/metabox.php:107
msgid "Affiliate Button Label"
msgstr "联盟按钮标签"

#: framework/extensions/product-reviews/metabox.php:109
#: framework/extensions/product-reviews/views/single-top.php:156
msgid "Buy Now"
msgstr "立即购买"

#: framework/extensions/product-reviews/metabox.php:114
msgid "Affiliate Link"
msgstr "会员链接"

#: framework/extensions/product-reviews/metabox.php:148
msgid "Read More Button Label"
msgstr "阅读更多按钮标签"

#: framework/extensions/product-reviews/metabox.php:150
#: framework/extensions/product-reviews/views/single-top.php:162
msgid "Read More"
msgstr "阅读更多"

#: framework/extensions/product-reviews/metabox.php:185
msgid "Scores"
msgstr "分数"

#: framework/extensions/product-reviews/metabox.php:225
msgid "Product specs"
msgstr "产品规格"

#: framework/extensions/product-reviews/metabox.php:250
#: framework/extensions/product-reviews/views/single-top.php:262
msgid "Pros"
msgstr "优点"

#: framework/extensions/product-reviews/metabox.php:270
#: framework/extensions/product-reviews/views/single-top.php:285
msgid "Cons"
msgstr "缺点"

#: framework/extensions/product-reviews/views/single-top.php:238
msgid "Specs"
msgstr "规格"

#. Translators: %s is the theme name.
#: framework/dashboard.php:417 framework/dashboard.php:418
#: framework/extensions/product-reviews/extension.php:619
msgid "Blocksy"
msgstr "Blocksy"

#: framework/theme-integration.php:229
msgid "YouTube"
msgstr "YouTube"

#: framework/theme-integration.php:230
msgid "Vimeo"
msgstr "Vimeo"

#: framework/features/blocks/share-box/options.php:31
#: framework/theme-integration.php:225
msgid "Pinterest"
msgstr "Pinterest"

#: framework/theme-integration.php:226
msgid "WordPress"
msgstr "WordPress"

#: framework/theme-integration.php:227
msgid "GitHub"
msgstr "GitHub"

#: framework/features/blocks/share-box/options.php:55
#: framework/theme-integration.php:231
msgid "VKontakte"
msgstr "VKontakte"

#: framework/features/blocks/share-box/options.php:61
#: framework/theme-integration.php:232
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: framework/theme-integration.php:233
msgid "TikTok"
msgstr "TikTok"

#: framework/features/header/header-options.php:79
msgid "Auto Hide/Show"
msgstr "自动隐藏/显示"

#: framework/features/conditions/rules/specific.php:25
msgid "Post with Taxonomy ID"
msgstr "文章带分类法 ID"

#: framework/features/header/items/account/options.php:520
#: framework/features/header/items/account/options.php:983
msgid "Label Visibility"
msgstr "标签的可见性"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:13
#: framework/features/blocks/contact-info/options.php:19
#: framework/features/header/items/account/options.php:577
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/breadcrumbs/Edit.js:54
#: static/js/editor/blocks/contact-info/Edit.js:107
msgid "Text"
msgstr "文本"

#: framework/extensions/newsletter-subscribe/customizer.php:26
#: framework/features/blocks/about-me/options.php:64
#: framework/features/blocks/dynamic-data/options.php:143
msgid "Description"
msgstr "描述"

#: framework/extensions/product-reviews/extension.php:591
#: framework/extensions/trending/customizer.php:35
msgid "Category"
msgstr "分类"

#: framework/extensions/trending/customizer.php:36
msgid "All categories"
msgstr "所有分类"

#: framework/extensions/trending/customizer.php:41
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:66
#: static/js/editor/blocks/tax-query/Edit.js:156
msgid "Taxonomy"
msgstr "分类法"

#: framework/extensions/trending/customizer.php:42
msgid "All taxonomies"
msgstr "所有分类法"

#: framework/extensions/trending/customizer.php:238
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:186
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:229
msgid "Taxonomies"
msgstr "分类法"

#: framework/extensions/trending/customizer.php:239
msgid "Custom Query"
msgstr "自定义查询"

#: framework/extensions/trending/customizer.php:262
msgid "Posts ID"
msgstr "文章ID"

#: framework/extensions/trending/customizer.php:266
msgid "Separate posts ID by comma. How to find the %spost ID%s."
msgstr "按逗号分隔文章 ID。如何查找%s ID%s。"

#: framework/features/conditions-manager.php:234
#: static/bundle/blocks/blocks.js:7 static/bundle/options.js:15
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:283
#: static/js/options/ConditionsManager/SingleCondition.js:91
msgid "Include"
msgstr "包括"

#: framework/features/conditions-manager.php:234
#: static/bundle/blocks/blocks.js:7 static/bundle/options.js:15
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:291
#: static/js/options/ConditionsManager/SingleCondition.js:92
msgid "Exclude"
msgstr "排除"

#: framework/features/blocks/about-me/options.php:204
#: framework/features/blocks/contact-info/options.php:530
#: framework/features/blocks/share-box/options.php:148
#: framework/features/blocks/socials/options.php:112
#: framework/features/header/items/account/options.php:22
#: framework/features/header/items/account/options.php:268
#: framework/features/header/items/account/options.php:348
#: framework/features/header/items/account/options.php:746
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:170
msgid "None"
msgstr "无"

#: framework/features/header/items/account/options.php:726
#: framework/features/header/items/account/options.php:1731
msgid "Simple"
msgstr "简单"

#. translators: %s: WordPress version
#: blocksy-companion.php:122
msgid "Blocksy requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Blocksy 需要 WordPress 版本%s+。由于您使用的是早期版本，因此该插件当前未运行。"

#. translators: %s: PHP version
#: blocksy-companion.php:110
msgid "Blocksy requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Blocksy 需要 PHP 版本%s+，插件当前未运行。"

#: framework/helpers/exts.php:186
msgid "Floating Cart"
msgstr "浮动购物车"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:262
#: framework/features/blocks/contact-info/options.php:361
#: framework/features/blocks/dynamic-data/options.php:138
#: framework/features/blocks/share-box/options.php:97
#: framework/features/header/modal/register.php:47
msgid "Email"
msgstr "电子邮件"

#: framework/features/header/modal/login.php:79
msgid "Log In"
msgstr "登录"

#: framework/features/header/modal/login.php:57
msgid "Forgot Password?"
msgstr "忘记密码？"

#: framework/features/header/modal/login.php:53
msgid "Remember Me"
msgstr "记住我"

#: framework/features/header/modal/login.php:28
#: framework/features/header/modal/register.php:53
msgid "Password"
msgstr "密码"

#: framework/features/header/account-modal.php:41
msgid "Sign Up"
msgstr "注册"

#: framework/features/header/items/account/config.php:4
msgid "Account"
msgstr "账户"

#: framework/features/header/account-modal.php:61
msgid "Back to login"
msgstr "返回登录"

#: framework/features/header/modal/lostpassword.php:13
msgid "Get New Password"
msgstr "获取新密码"

#: framework/features/header/modal/login.php:23
#: framework/features/header/modal/lostpassword.php:5
msgid "Username or Email Address"
msgstr "用户名或邮箱地址"

#: framework/features/header/modal/register.php:112
msgid "Register"
msgstr "注册"

#: framework/extensions/cookies-consent/customizer.php:15
#: framework/extensions/newsletter-subscribe/customizer.php:12
#: framework/extensions/product-reviews/extension.php:382
#: framework/extensions/product-reviews/metabox.php:6
#: framework/extensions/trending/customizer.php:110
#: framework/features/header/header-options.php:6
#: framework/features/header/items/account/options.php:330
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:43
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:39
msgid "General"
msgstr "常规"

#: framework/features/header/items/account/options.php:346
msgid "Avatar"
msgstr "头像"

#: framework/features/blocks/search/options.php:60
#: framework/features/blocks/search/options.php:66
#: framework/features/blocks/search/view.php:80
#: framework/features/blocks/search/view.php:261
#: framework/features/conditions/rules/pages.php:22
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:92
#: static/js/editor/blocks/search/Preview.js:23
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:78
msgid "Search"
msgstr "搜索"

#: framework/features/conditions/rules/pages.php:27
msgid "Blog"
msgstr "博客"

#: framework/features/conditions/rules/specific.php:15
msgid "Page ID"
msgstr "页面ID"

#: framework/features/header/items/account/options.php:702
#: framework/features/header/items/account/options.php:1073
msgid "Custom Page Link"
msgstr "自定义页面链接"

#: framework/extensions/trending/customizer.php:291
msgid "All Time"
msgstr "所有时间"

#: framework/extensions/trending/customizer.php:293
msgid "Last 7 Days"
msgstr "最近7天"

#: framework/features/conditions/rules/user-auth.php:34
msgid "User Role"
msgstr "用户角色"

#: framework/extensions/cookies-consent/customizer.php:28
#: framework/features/header/items/account/options.php:447
#: framework/features/header/items/account/options.php:821
#: framework/features/header/items/account/options.php:909
msgid "Type 1"
msgstr "类型1"

#: framework/extensions/cookies-consent/customizer.php:33
#: framework/features/header/items/account/options.php:457
#: framework/features/header/items/account/options.php:831
#: framework/features/header/items/account/options.php:919
msgid "Type 2"
msgstr "类型2"

#: framework/extensions/cookies-consent/customizer.php:40
msgid "Cookie period"
msgstr "Cookie 周期"

#: framework/extensions/cookies-consent/customizer.php:48
msgid "One hour"
msgstr "一小时"

#: framework/extensions/cookies-consent/customizer.php:49
msgid "One day"
msgstr "一天"

#: framework/extensions/cookies-consent/customizer.php:50
msgid "One week"
msgstr "一周"

#: framework/extensions/cookies-consent/customizer.php:51
msgid "One month"
msgstr "一个月"

#: framework/extensions/cookies-consent/customizer.php:52
msgid "Three months"
msgstr "三个月"

#: framework/extensions/cookies-consent/customizer.php:53
msgid "Six months"
msgstr "六个月"

#: framework/extensions/cookies-consent/customizer.php:54
msgid "One year"
msgstr "一年"

#: framework/extensions/cookies-consent/customizer.php:55
msgid "Forever"
msgstr "永远"

#: framework/extensions/cookies-consent/customizer.php:62
#: framework/features/blocks/contact-info/options.php:67
#: framework/features/blocks/contact-info/options.php:130
#: framework/features/blocks/contact-info/options.php:191
#: framework/features/blocks/contact-info/options.php:252
#: framework/features/blocks/contact-info/options.php:313
#: framework/features/blocks/contact-info/options.php:374
#: framework/features/blocks/contact-info/options.php:435
msgid "Content"
msgstr "内容"

#: framework/extensions/cookies-consent/customizer.php:64
#: framework/extensions/cookies-consent/helpers.php:7
msgid "We use cookies to ensure that we give you the best experience on our website."
msgstr "我们使用 cookie 来确保在我们的网站上为您提供最佳体验。"

#: framework/extensions/cookies-consent/customizer.php:80
#: framework/extensions/cookies-consent/helpers.php:10
msgid "Accept"
msgstr "接受"

#: framework/extensions/cookies-consent/customizer.php:98
msgid "Maximum Width"
msgstr "最大宽度"

#: framework/extensions/cookies-consent/customizer.php:112
msgid "Forms Cookies Content"
msgstr "表单 Cookie 内容"

#: framework/extensions/cookies-consent/customizer.php:123
msgid "This text will appear under each comment form and subscribe form."
msgstr "此文本将显示在每个注释表单和订阅表单下。"

#: framework/extensions/cookies-consent/customizer.php:142
#: framework/extensions/newsletter-subscribe/customizer.php:170
#: framework/extensions/product-reviews/extension.php:422
#: framework/extensions/trending/customizer.php:395
#: framework/features/header/header-options.php:203
#: framework/features/header/items/account/options.php:1107
msgid "Design"
msgstr "设计"

#: framework/extensions/cookies-consent/customizer.php:170
#: framework/extensions/cookies-consent/customizer.php:226
#: framework/extensions/cookies-consent/customizer.php:257
#: framework/extensions/cookies-consent/customizer.php:292
#: framework/extensions/cookies-consent/customizer.php:321
#: framework/extensions/newsletter-subscribe/customizer.php:219
#: framework/extensions/newsletter-subscribe/customizer.php:350
#: framework/extensions/trending/customizer.php:492
#: framework/extensions/trending/customizer.php:524
#: framework/features/header/items/account/options.php:1207
#: framework/features/header/items/account/options.php:1250
#: framework/features/header/items/account/options.php:1292
#: framework/features/header/items/account/options.php:1398
#: framework/features/header/items/account/options.php:1440
#: framework/features/header/items/account/options.php:1481
#: framework/features/header/items/account/options.php:1545
#: framework/features/header/items/account/options.php:1764
#: framework/features/header/items/account/options.php:1810
#: framework/features/header/items/account/options.php:1861
#: framework/features/header/items/account/options.php:1979
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:239
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:270
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/about-me/Edit.js:106
#: static/js/editor/blocks/about-me/Edit.js:134
#: static/js/editor/blocks/about-me/Edit.js:169
#: static/js/editor/blocks/about-me/Edit.js:198
#: static/js/editor/blocks/contact-info/Edit.js:150
#: static/js/editor/blocks/contact-info/Edit.js:183
#: static/js/editor/blocks/contact-info/Edit.js:212
#: static/js/editor/blocks/search/Edit.js:300
#: static/js/editor/blocks/search/Edit.js:332
#: static/js/editor/blocks/search/Edit.js:368
#: static/js/editor/blocks/share-box/Edit.js:100
#: static/js/editor/blocks/share-box/Edit.js:131
#: static/js/editor/blocks/share-box/Edit.js:161
#: static/js/editor/blocks/socials/Edit.js:100
#: static/js/editor/blocks/socials/Edit.js:131
#: static/js/editor/blocks/socials/Edit.js:161
msgid "Hover"
msgstr "悬停"

#: framework/extensions/cookies-consent/customizer.php:178
#: framework/extensions/cookies-consent/customizer.php:234
#: framework/extensions/cookies-consent/customizer.php:299
#: framework/features/header/items/account/options.php:1829
#: framework/features/header/items/account/options.php:1953
msgid "Background Color"
msgstr "背景颜色"

#: framework/extensions/cookies-consent/customizer.php:203
#: framework/extensions/cookies-consent/customizer.php:270
#: framework/features/header/items/account/options.php:1518
#: framework/features/header/items/account/options.php:1902
msgid "Font Color"
msgstr "字体颜色"

#: framework/extensions/cookies-consent/customizer.php:164
#: framework/extensions/cookies-consent/customizer.php:191
#: framework/extensions/cookies-consent/customizer.php:220
#: framework/extensions/cookies-consent/customizer.php:251
#: framework/extensions/cookies-consent/customizer.php:287
#: framework/extensions/cookies-consent/customizer.php:316
#: framework/extensions/newsletter-subscribe/customizer.php:188
#: framework/extensions/newsletter-subscribe/customizer.php:213
#: framework/extensions/newsletter-subscribe/customizer.php:245
#: framework/extensions/newsletter-subscribe/customizer.php:276
#: framework/extensions/newsletter-subscribe/customizer.php:313
#: framework/extensions/newsletter-subscribe/customizer.php:345
#: framework/extensions/trending/customizer.php:423
#: framework/extensions/trending/customizer.php:486
#: framework/extensions/trending/customizer.php:518
#: framework/features/header/items/account/options.php:1198
#: framework/features/header/items/account/options.php:1242
#: framework/features/header/items/account/options.php:1284
#: framework/features/header/items/account/options.php:1389
#: framework/features/header/items/account/options.php:1432
#: framework/features/header/items/account/options.php:1473
#: framework/features/header/items/account/options.php:1539
#: framework/features/header/items/account/options.php:1572
#: framework/features/header/items/account/options.php:1610
#: framework/features/header/items/account/options.php:1653
#: framework/features/header/items/account/options.php:1758
#: framework/features/header/items/account/options.php:1801
#: framework/features/header/items/account/options.php:1852
#: framework/features/header/items/account/options.php:1974
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:137
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:168
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:201
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:230
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:261
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/about-me/Edit.js:99
#: static/js/editor/blocks/about-me/Edit.js:127
#: static/js/editor/blocks/about-me/Edit.js:160
#: static/js/editor/blocks/about-me/Edit.js:189
#: static/js/editor/blocks/contact-info/Edit.js:143
#: static/js/editor/blocks/contact-info/Edit.js:174
#: static/js/editor/blocks/contact-info/Edit.js:203
#: static/js/editor/blocks/search/Edit.js:205
#: static/js/editor/blocks/search/Edit.js:232
#: static/js/editor/blocks/search/Edit.js:261
#: static/js/editor/blocks/search/Edit.js:292
#: static/js/editor/blocks/search/Edit.js:324
#: static/js/editor/blocks/search/Edit.js:359
#: static/js/editor/blocks/search/Edit.js:389
#: static/js/editor/blocks/search/Edit.js:409
#: static/js/editor/blocks/share-box/Edit.js:91
#: static/js/editor/blocks/share-box/Edit.js:122
#: static/js/editor/blocks/share-box/Edit.js:152
#: static/js/editor/blocks/socials/Edit.js:91
#: static/js/editor/blocks/socials/Edit.js:122
#: static/js/editor/blocks/socials/Edit.js:152
msgid "Initial"
msgstr "初始"

#: framework/extensions/cookies-consent/helpers.php:55
msgid "I accept the %sPrivacy Policy%s"
msgstr "我接受%s隐私政策%s"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:14
#: framework/extensions/newsletter-subscribe/admin-static/bundle/newsletter-block.js:1
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:54
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:56
msgid "Newsletter"
msgstr "通讯"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:34
#: framework/extensions/newsletter-subscribe/customizer.php:41
msgid "List Source"
msgstr "列表来源"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:40
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:81
#: framework/extensions/newsletter-subscribe/customizer.php:48
#: framework/extensions/product-reviews/metabox.php:17
#: framework/extensions/trending/customizer.php:169
#: framework/features/blocks/contact-info/options.php:90
#: framework/features/blocks/contact-info/options.php:153
#: framework/features/blocks/contact-info/options.php:214
#: framework/features/blocks/contact-info/options.php:275
#: framework/features/blocks/contact-info/options.php:336
#: framework/features/blocks/contact-info/options.php:397
#: framework/features/blocks/contact-info/options.php:458
#: framework/features/header/header-options.php:76
#: framework/features/header/items/account/options.php:385
#: framework/features/header/items/account/options.php:766
msgid "Default"
msgstr "默认"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:50
#: framework/extensions/newsletter-subscribe/customizer.php:61
#: framework/extensions/newsletter-subscribe/dashboard-static/bundle/main.js:15
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:204
msgid "List ID"
msgstr "列表ID"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:67
#: framework/features/dynamic-css.php:55
msgid "Inline"
msgstr "行内"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:109
#: framework/extensions/newsletter-subscribe/customizer.php:79
msgid "Name Field"
msgstr "姓名字段"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:121
#: framework/extensions/newsletter-subscribe/customizer.php:117
msgid "Name Label"
msgstr "名称标签"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:122
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:147
#: framework/extensions/newsletter-subscribe/customizer.php:119
#: framework/extensions/newsletter-subscribe/extension.php:208
#: framework/extensions/newsletter-subscribe/helpers.php:37
#: framework/extensions/newsletter-subscribe/helpers.php:81
msgid "Your name"
msgstr "你的名字"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:41
#: framework/extensions/newsletter-subscribe/customizer.php:49
#: framework/extensions/trending/customizer.php:173
#: framework/features/blocks/about-me/options.php:27
#: framework/features/blocks/about-me/options.php:194
#: framework/features/blocks/contact-info/options.php:91
#: framework/features/blocks/contact-info/options.php:154
#: framework/features/blocks/contact-info/options.php:215
#: framework/features/blocks/contact-info/options.php:276
#: framework/features/blocks/contact-info/options.php:337
#: framework/features/blocks/contact-info/options.php:398
#: framework/features/blocks/contact-info/options.php:459
#: framework/features/blocks/dynamic-data/options.php:64
#: framework/features/blocks/share-box/options.php:136
#: framework/features/blocks/socials/options.php:100
#: framework/features/header/items/account/options.php:25
#: framework/features/header/items/account/options.php:389
#: framework/features/header/items/account/options.php:770
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/dynamic-data/utils.js:10
msgid "Custom"
msgstr "自定义"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:136
#: framework/extensions/newsletter-subscribe/customizer.php:128
msgid "Mail Label"
msgstr "邮件标签"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:142
#: framework/extensions/newsletter-subscribe/customizer.php:137
msgid "Button Label"
msgstr "按钮标签"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:143
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:21
#: framework/extensions/newsletter-subscribe/customizer.php:139
#: framework/extensions/newsletter-subscribe/extension.php:203
#: framework/extensions/newsletter-subscribe/helpers.php:31
#: framework/extensions/newsletter-subscribe/helpers.php:76
msgid "Subscribe"
msgstr "订阅"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:253
#: framework/features/blocks/about-me/options.php:58
#: framework/features/header/items/account/options.php:578
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:129
msgid "Name"
msgstr "名称"

#: framework/extensions/newsletter-subscribe/customizer.php:4
msgid "Subscribe Form"
msgstr "订阅表单"

#: framework/extensions/newsletter-subscribe/customizer.php:18
#: framework/features/blocks/about-me/options.php:15
#: framework/features/blocks/contact-info/options.php:14
#: framework/features/blocks/contact-info/options.php:60
#: framework/features/blocks/contact-info/options.php:123
#: framework/features/blocks/contact-info/options.php:184
#: framework/features/blocks/contact-info/options.php:245
#: framework/features/blocks/contact-info/options.php:306
#: framework/features/blocks/contact-info/options.php:367
#: framework/features/blocks/contact-info/options.php:428
#: framework/features/blocks/share-box/options.php:14
#: framework/features/blocks/socials/options.php:14
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:44
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:125
msgid "Title"
msgstr "标题"

#: framework/extensions/newsletter-subscribe/customizer.php:20
#: framework/extensions/newsletter-subscribe/helpers.php:21
#: framework/extensions/newsletter-subscribe/helpers.php:69
msgid "Newsletter Updates"
msgstr "通讯更新"

#: framework/extensions/newsletter-subscribe/customizer.php:130
#: framework/extensions/newsletter-subscribe/extension.php:209
#: framework/extensions/newsletter-subscribe/helpers.php:41
#: framework/extensions/newsletter-subscribe/helpers.php:82
msgid "Your email"
msgstr "您的邮箱"

#: framework/extensions/newsletter-subscribe/customizer.php:149
msgid "Visibility"
msgstr "可见性"

#: framework/extensions/newsletter-subscribe/customizer.php:160
#: framework/extensions/trending/customizer.php:341
#: framework/features/blocks/search/options.php:168
#: framework/features/header/header-options.php:108
#: framework/features/header/header-options.php:190
#: framework/features/header/items/account/options.php:532
#: framework/features/header/items/account/options.php:995
msgid "Desktop"
msgstr "电脑"

#: framework/extensions/newsletter-subscribe/customizer.php:161
#: framework/extensions/trending/customizer.php:342
#: framework/features/blocks/search/options.php:169
#: framework/features/header/items/account/options.php:533
#: framework/features/header/items/account/options.php:996
#: framework/features/header/items/account/options.php:2069
msgid "Tablet"
msgstr "平板"

#: framework/extensions/newsletter-subscribe/customizer.php:162
#: framework/extensions/trending/customizer.php:343
#: framework/features/blocks/contact-info/options.php:178
#: framework/features/blocks/search/options.php:170
#: framework/features/header/header-options.php:110
#: framework/features/header/header-options.php:192
#: framework/features/header/items/account/options.php:534
#: framework/features/header/items/account/options.php:997
#: framework/features/header/items/account/options.php:2070
msgid "Mobile"
msgstr "手机"

#: framework/extensions/newsletter-subscribe/customizer.php:328
msgid "Button Color"
msgstr "按钮颜色"

#: framework/extensions/newsletter-subscribe/customizer.php:357
#: framework/extensions/trending/customizer.php:532
msgid "Container Background"
msgstr "容器背景"

#: framework/extensions/newsletter-subscribe/customizer.php:408
#: framework/extensions/trending/customizer.php:548
msgid "Container Inner Spacing"
msgstr "容器内部间距"

#: framework/extensions/newsletter-subscribe/extension.php:155
msgid "Disable Subscribe Form"
msgstr "禁用订阅表单"

#: framework/extensions/newsletter-subscribe/helpers.php:72
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "请输入您的电子邮件地址进行订阅"

#: framework/extensions/newsletter-subscribe/helpers.php:147
msgid "Email address"
msgstr "电子邮件地址"

#: framework/extensions/trending/customizer.php:4
#: framework/features/blocks/query/block.php:19
#: framework/features/blocks/search/options.php:6
#: framework/features/conditions/rules/posts.php:33
msgid "Posts"
msgstr "文章"

#: framework/extensions/trending/customizer.php:8
#: framework/features/blocks/search/options.php:16
msgid "Products"
msgstr "产品"

#: framework/extensions/trending/customizer.php:106
msgid "Trending Posts"
msgstr "热门文章"

#: framework/extensions/trending/customizer.php:119
#: framework/extensions/trending/helpers.php:230
msgid "Trending now"
msgstr "实时焦点"

#: framework/extensions/trending/customizer.php:210
#: framework/extensions/trending/customizer.php:224
#: static/bundle/blocks/blocks.js:7 static/js/editor/blocks/query/Edit.js:169
msgid "Post Type"
msgstr "内容类型"

#: framework/extensions/trending/customizer.php:233
msgid "Source"
msgstr "来源"

#: framework/extensions/trending/customizer.php:188
#: framework/features/blocks/contact-info/options.php:101
#: framework/features/blocks/contact-info/options.php:164
#: framework/features/blocks/contact-info/options.php:225
#: framework/features/blocks/contact-info/options.php:286
#: framework/features/blocks/contact-info/options.php:347
#: framework/features/blocks/contact-info/options.php:408
#: framework/features/blocks/contact-info/options.php:469
#: framework/features/blocks/search/options.php:86
#: framework/features/header/items/account/options.php:347
#: framework/features/header/items/account/options.php:404
#: framework/features/header/items/account/options.php:745
#: framework/features/header/items/account/options.php:785
msgid "Icon"
msgstr "图标"

#: framework/extensions/trending/customizer.php:282
msgid "Trending From"
msgstr "热门依据"

#: framework/extensions/trending/customizer.php:292
msgid "Last 24 Hours"
msgstr "最近24小时"

#: framework/extensions/trending/customizer.php:294
msgid "Last Month"
msgstr "上个月"

#: framework/extensions/trending/customizer.php:329
msgid "Container Visibility"
msgstr "容器可见性"

#: framework/extensions/trending/customizer.php:374
#: framework/features/header/header-options.php:163 static/bundle/options.js:15
#: static/js/header/EditConditions.js:103
msgid "Display Conditions"
msgstr "显示条件"

#: framework/features/conditions/rules/basic.php:3
msgid "Entire Website"
msgstr "整个网站"

#: framework/features/conditions/rules/basic.php:41
msgid "Basic"
msgstr "基本"

#: framework/features/conditions/rules/cpt.php:17
msgid "%s Single"
msgstr "%s 单个"

#: framework/features/conditions/rules/cpt.php:27
msgid "%s Archive"
msgstr "%s 存档"

#: framework/features/conditions/rules/cpt.php:53
msgid "Custom Post Types"
msgstr "自定义文章类型"

#: framework/features/conditions/rules/pages.php:8
msgid "Single Page"
msgstr "单页"

#: framework/features/conditions/rules/pages.php:15
msgid "404"
msgstr "404"

#: framework/features/conditions/rules/pages.php:33
msgid "Front Page"
msgstr "首页"

#: framework/features/blocks/search/options.php:7
#: framework/features/conditions/rules/pages.php:52
msgid "Pages"
msgstr "页面"

#: framework/features/conditions/rules/posts.php:15
msgid "Post Categories"
msgstr "文章分类"

#: framework/features/conditions/rules/posts.php:20
msgid "Post Tags"
msgstr "文章标签"

#: framework/features/conditions/rules/posts.php:27
msgid "Single Post"
msgstr "单个文章"

#: framework/features/conditions/rules/specific.php:10
msgid "Post ID"
msgstr "Post ID"

#: framework/features/conditions/rules/specific.php:20
#: static/bundle/options.js:15
#: static/js/options/ConditionsManager/PostIdPicker.js:79
msgid "Custom Post Type ID"
msgstr "自定义文章类型 ID"

#: framework/features/conditions/rules/specific.php:38
#: framework/features/conditions/rules/woo.php:65
msgid "Taxonomy ID"
msgstr "分类法 ID"

#: framework/features/conditions/rules/specific.php:48
#: static/bundle/blocks/blocks.js:7
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:69
msgid "Specific"
msgstr "特定"

#: framework/features/conditions/rules/user-auth.php:20
msgid "User Auth"
msgstr "用户验证"

#: framework/features/conditions/rules/user-auth.php:24
msgid "User Logged In"
msgstr "用户登录"

#: framework/features/conditions/rules/user-auth.php:29
msgid "User Logged Out"
msgstr "用户注销"

#: framework/features/conditions/rules/woo.php:15
msgid "Shop Home"
msgstr "购物首页"

#: framework/features/conditions/rules/woo.php:20
msgid "Single Product"
msgstr "单个产品"

#: framework/features/conditions/rules/woo.php:25
msgid "Product Archives"
msgstr "产品档案"

#: framework/features/conditions/rules/woo.php:30
msgid "Product Categories"
msgstr "产品分类"

#: framework/features/conditions/rules/woo.php:49
msgid "Product Tags"
msgstr "产品标签"

#: framework/features/conditions/rules/woo.php:89
msgid "WooCommerce"
msgstr "WooCommerce"

#: framework/features/demo-install.php:181
msgid "Your PHP installation doesn't have support for XML. Please install the <i>xml</i> or <i>simplexml</i> PHP extension in order to be able to install starter sites. You might need to contact your hosting provider to assist you in doing so."
msgstr "您的 PHP 安装不支持 XML。请安装<i>xml</i><i>或简单xml</i> PHP扩展，以便能够安装初学者网站。您可能需要联系您的托管提供商，以帮助您这样做。"

#: framework/features/demo-install/parsers.php:53
#: framework/features/demo-install/parsers.php:97
#: framework/features/demo-install/parsers.php:105
msgid "There was an error when reading this WXR file"
msgstr "阅读此WXR文件时发生错误"

#: framework/features/demo-install/parsers.php:54
msgid "Details are shown above. The importer will now try again with a different parser..."
msgstr "情如上所示。 导入器现在再次尝试使用不同的解析器..."

#: framework/features/demo-install/parsers.php:109
#: framework/features/demo-install/parsers.php:114
#: framework/features/demo-install/parsers.php:371
#: framework/features/demo-install/parsers.php:585
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "这似乎不是WXR文件，缺少/无效的WXR版本号"

#: framework/features/demo-install/wp-importer.php:194
#: framework/features/demo-install/wp-importer.php:245
#: framework/features/demo-install/wp-importer.php:249
#: framework/features/demo-install/wp-importer.php:258
msgid "Sorry, there has been an error."
msgstr "对不起，出现了一个错误。"

#: framework/features/demo-install/wp-importer.php:229
msgid "All done."
msgstr "全部完成。"

#: framework/features/demo-install/wp-importer.php:229
msgid "Have fun!"
msgstr "用的开心！"

#: framework/features/demo-install/wp-importer.php:230
msgid "Remember to update the passwords and roles of imported users."
msgstr "记得更新已导入的用户的密码和角色。"

#: framework/features/demo-install/wp-importer.php:250
msgid "The export file could not be found at <code>%s</code>. It is likely that this was caused by a permissions problem."
msgstr "无法找到导出文件 <code>%s</code>. 上找不到导出文件。这可能是由权限问题引起的。"

#: framework/features/demo-install/wp-importer.php:266
msgid "This WXR file (version %s) may not be supported by this version of the importer. Please consider updating."
msgstr "此版本的导入程序可能不支持此WXR文件（版本%s）。 请考虑更新。"

#: framework/features/demo-install/wp-importer.php:291
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "导入作者 %s 失败了，他们的文章将被分配给当前用户。"

#: framework/features/demo-install/wp-importer.php:317
msgid "Assign Authors"
msgstr "分配作者"

#: framework/features/demo-install/wp-importer.php:318
msgid "To make it simpler for you to edit and save the imported content, you may want to reassign the author of the imported item to an existing user of this site, such as your primary administrator account."
msgstr "为了使您更轻松地编辑和保存导入的内容，您可能需要将导入项目的作者重新分配给该站点的现有用户，例如您的主要管理员帐户。"

#: framework/features/demo-install/wp-importer.php:320
msgid "If a new user is created by WordPress, a new password will be randomly generated and the new user&#8217;s role will be set as %s. Manually changing the new user&#8217;s details will be necessary."
msgstr "WordPress 创建了一个新用户，新的密码将随机生成，而且新用户的角色被设置为 %s。如有需要，可以手动更改这个新用户的信息。"

#: framework/features/demo-install/wp-importer.php:330
msgid "Import Attachments"
msgstr "导入附件"

#: framework/features/demo-install/wp-importer.php:333
msgid "Download and import file attachments"
msgstr "下载和导入附件"

#: framework/features/demo-install/wp-importer.php:337
msgid "Submit"
msgstr "发送"

#: framework/features/demo-install/wp-importer.php:350
msgid "Import author:"
msgstr "导入作者："

#: framework/features/demo-install/wp-importer.php:361
msgid "or create new user with login name:"
msgstr "或创建用户使用用户名："

#: framework/features/demo-install/wp-importer.php:364
msgid "as a new user:"
msgstr "作为一个新用户："

#: framework/features/demo-install/wp-importer.php:372
msgid "assign posts to an existing user:"
msgstr "分配文章到已存在的用户："

#: framework/features/demo-install/wp-importer.php:374
msgid "or assign posts to an existing user:"
msgstr "或分配文章给已存在的用户："

#: framework/features/demo-install/wp-importer.php:380
msgid "- Select -"
msgstr "- 选择 -"

#: framework/features/demo-install/wp-importer.php:433
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "创建新用户给 %s 失败了。他们的文章将被分配给当前用户。"

#: framework/features/demo-install/wp-importer.php:485
msgid "Failed to import category %s"
msgstr "导入分类 %s 失败"

#: framework/features/demo-install/wp-importer.php:531
msgid "Failed to import post tag %s"
msgstr "导入文章标签 %s 失败"

#: framework/features/demo-install/wp-importer.php:588
#: framework/features/demo-install/wp-importer.php:849
msgid "Failed to import %s %s"
msgstr "无法导入 %s %s"

#: framework/features/demo-install/wp-importer.php:751
msgid "%s &#8220;%s&#8221; already exists."
msgstr "%s &#8220;%s&#8221; 已经存在。"

#: framework/features/demo-install/wp-importer.php:815
msgid "Failed to import %s &#8220;%s&#8221;"
msgstr "无法导入 %s &#8220;%s&#8221;"

#: framework/features/demo-install/wp-importer.php:986
msgid "Menu item skipped due to missing menu slug"
msgstr "菜单项由于丢失菜单别名（slug）而跳过"

#: framework/features/demo-install/wp-importer.php:993
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "菜单项由于无效的菜单别名（slug）而跳过：%s"

#: framework/features/demo-install/wp-importer.php:1077
msgid "Fetching attachments is not enabled"
msgstr "提取附件未启用"

#: framework/features/demo-install/wp-importer.php:1090
msgid "Invalid file type"
msgstr "文件类型无效"

#: framework/features/demo-install/wp-importer.php:1142
msgid "Remote server did not respond"
msgstr "远程服务器没有响应"

#: framework/features/demo-install/wp-importer.php:1150
msgid "Remote server returned error response %1$d %2$s"
msgstr "远程服务器返回错误响应 %1$d %2$s"

#: framework/features/demo-install/wp-importer.php:1157
msgid "Remote file is incorrect size"
msgstr "远程文件大小不正确"

#: framework/features/demo-install/wp-importer.php:1162
msgid "Zero size file downloaded"
msgstr "零大小的文件下载"

#: framework/features/demo-install/wp-importer.php:1168
msgid "Remote file is too large, limit is %s"
msgstr "远程文件太大，限制为%s"

#: framework/features/demo-install/wp-importer.php:1268
msgid "Import WordPress"
msgstr "导入 WordPress"

#: framework/features/demo-install/wp-importer.php:1275
msgid "A new version of this importer is available. Please update to version %s to ensure compatibility with newer export files."
msgstr "一个新版本的导入工具可用，请更新到版本 %s，以确保与新的导出文件的兼容性。"

#: framework/features/demo-install/wp-importer.php:1290
msgid "Howdy! Upload your WordPress eXtended RSS (WXR) file and we&#8217;ll import the posts, pages, comments, custom fields, categories, and tags into this site."
msgstr "你好！上传你的 WordPress  eXtended RSS (WXR) 文件，我们将导入文章、页面、评论、自定义字段、分类和标签到这个网站。"

#: framework/features/demo-install/wp-importer.php:1291
msgid "Choose a WXR (.xml) file to upload, then click Upload file and import."
msgstr "选择一个要上传的WXR（XML）文件，然后点击上传文件并导入。"

#: framework/features/dynamic-css.php:48
msgid "Dynamic CSS Output"
msgstr "动态 CSS 输出"

#: framework/features/dynamic-css.php:52
msgid "The strategy of outputting the dynamic CSS. File - all the CSS code will be placed in a static file, otherwise it will be placed inline in head."
msgstr "输出动态CSS的策略。文件 - 所有 CSS 代码将放置在静态文件中，否则将内联放置在头部中。"

#: framework/features/dynamic-css.php:54
msgid "File"
msgstr "文件"

#: framework/features/google-analytics.php:104
msgid "Google Analytics v4"
msgstr "谷歌分析 v4"

#: framework/features/header/account-modal.php:37
#: framework/features/header/items/account/options.php:1053
#: framework/features/header/items/account/views/logout.php:8
msgid "Login"
msgstr "登入"

#: framework/features/header/header-options.php:11
msgid "Sticky Functionality"
msgstr "固定功能"

#: framework/features/header/header-options.php:37
msgid "Only Main Row"
msgstr "仅主行"

#: framework/features/header/header-options.php:42
msgid "Top & Main Row"
msgstr "顶部和主行"

#: framework/features/header/header-options.php:47
msgid "All Rows"
msgstr "所有行"

#: framework/features/header/header-options.php:52
msgid "Main & Bottom Row"
msgstr "主行和下排"

#: framework/features/header/header-options.php:57
msgid "Only Top Row"
msgstr "仅顶行"

#: framework/features/header/header-options.php:62
msgid "Only Bottom Row"
msgstr "仅底部行"

#: framework/features/header/header-options.php:77
msgid "Slide Down"
msgstr "向下滑"

#: framework/features/header/header-options.php:78
msgid "Fade"
msgstr "渐变"

#: framework/features/header/header-options.php:97
#: framework/features/header/header-options.php:179
msgid "Enable on"
msgstr "激活"

#: framework/features/header/header-options.php:125
msgid "Transparent Functionality"
msgstr "透明功能"

#: framework/features/header/header-options.php:214
#: framework/features/header/items/account/options.php:1140
#: framework/features/header/items/account/options.php:1332
msgid "Default State"
msgstr "默认状态"

#: framework/features/header/header-options.php:219
#: framework/features/header/items/account/options.php:1148
#: framework/features/header/items/account/options.php:1340
msgid "Transparent State"
msgstr "透明状态"

#: framework/features/header/header-options.php:227
#: framework/features/header/items/account/options.php:1161
#: framework/features/header/items/account/options.php:1353
msgid "Sticky State"
msgstr "固定状态"

#: framework/features/header/items/account/options.php:4
msgid "Profile Page"
msgstr "资料页"

#: framework/features/header/items/account/options.php:5
msgid "Dashboard Page"
msgstr "仪表盘页面"

#: framework/features/header/items/account/options.php:6
msgid "Logout"
msgstr "注销"

#: framework/features/header/items/account/options.php:173
#: framework/features/header/items/account/options.php:597
#: framework/features/header/items/account/views/login.php:100
#: framework/features/header/items/account/views/login.php:472
msgid "My Account"
msgstr "我的账户"

#: framework/features/header/items/account/options.php:357
msgid "Avatar Size"
msgstr "头像大小"

#: framework/features/header/items/account/options.php:508
#: framework/features/header/items/account/options.php:882
#: framework/features/header/items/account/options.php:968
msgid "Icon Size"
msgstr "图标大小"

#: framework/features/header/items/account/options.php:1499
msgid "Item Margin"
msgstr "项目内边距"

#: framework/features/header/items/account/options.php:1691
msgid "Modal Backdrop"
msgstr "模式背景"

#: framework/features/header/items/account/options.php:2016
msgid "Shadow"
msgstr "阴影"

#: framework/features/header/modal/register.php:41
msgid "Username"
msgstr "帐号 ( 信箱 )"

#: framework/features/header/modal/register.php:106
msgid "Registration confirmation will be emailed to you."
msgstr "注册确认将通过电子邮件发送给您."

#: framework/features/opengraph-meta-data.php:13
msgid "OpenGraph Meta Data"
msgstr "OpenGraph元数据"

#: framework/features/opengraph-meta-data.php:26
msgid "Facebook Page URL"
msgstr "Facebook页面URL"

#: framework/features/opengraph-meta-data.php:33
msgid "Facebook App ID"
msgstr "Facebook App ID"

#: framework/features/opengraph-meta-data.php:40
msgid "Twitter Username"
msgstr "Twitter用户名"

#: framework/helpers/exts.php:194
msgid "Quick View"
msgstr "快速预览"

#: framework/features/blocks/about-me/options.php:86
#: framework/theme-integration.php:228
msgid "Medium"
msgstr "中"

#: framework/features/blocks/share-box/options.php:19
#: framework/theme-integration.php:220
msgid "Facebook"
msgstr "Facebook"

#: framework/features/blocks/share-box/options.php:37
#: framework/theme-integration.php:222
msgid "LinkedIn"
msgstr "LinkedIn"

#: framework/theme-integration.php:223
msgid "Dribbble"
msgstr "Dribbble"

#: framework/theme-integration.php:224
msgid "Instagram"
msgstr "Instagram"

#: framework/theme-integration.php:278
msgid "Companion"
msgstr "配套插件"

#: framework/theme-integration.php:296
msgid "PRO"
msgstr "专业版"

#. Author of the plugin
#: blocksy-companion.php
msgid "CreativeThemes"
msgstr "CreativeThemes"

#. Author URI of the plugin
#: blocksy-companion.php
msgid "https://creativethemes.com"
msgstr "https://creativethemes.com"

#. Plugin Name of the plugin
#: blocksy-companion.php
msgid "Blocksy Companion"
msgstr "Blocksy Companion"

#. Description of the plugin
#: blocksy-companion.php
msgid "This plugin is the companion for the Blocksy theme, it runs and adds its enhacements only if the Blocksy theme is installed and active."
msgstr "这是WBlock主题的配套插件，它只在 Blocksy 主题被安装和激活的情况下才运行并添加其功能。"