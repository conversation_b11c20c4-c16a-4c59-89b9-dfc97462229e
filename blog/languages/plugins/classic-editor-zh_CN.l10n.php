<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2024-11-24 11:00:57+0000','plural-forms'=>'nplurals=1; plural=0;','project-id-version'=>'Plugins - Classic Editor - Stable (latest release)','language'=>'zh_CN','messages'=>['Editor NameClassic Editor'=>'经典编辑器','Editor NameBlock editor'=>'区块编辑器','Editor NameClassic editor'=>'经典编辑器','Editor NameEdit (classic editor)'=>'编辑（经典编辑器）','Editor NameEdit (block editor)'=>'编辑（区块编辑器）','Change settings'=>'更改设置','Default editor for all sites'=>'所有站点的默认编辑器','Editor Settings'=>'编辑器设置','Default Editor'=>'默认编辑器','Editor Nameblock editor'=>'区块编辑器','Editor Nameclassic editor'=>'经典编辑器','Edit &#8220;%s&#8221; in the block editor'=>'在区块编辑器中编辑《%s》','Switch to block editor'=>'切换到区块编辑器','Switch to classic editor'=>'切换到经典编辑器','By default the block editor is replaced with the classic editor and users cannot switch editors.'=>'在默认情况下，区块编辑器将替换为经典编辑器，且用户将不能切换编辑器。','Allow site admins to change settings'=>'允许站点管理员修改设置','Editor'=>'编辑器','No'=>'否','Yes'=>'是','Allow users to switch editors'=>'允许用户切换编辑器','Default editor for all users'=>'所有用户的默认编辑器','https://github.com/WordPress/classic-editor/'=>'https://github.com/WordPress/classic-editor/','https://wordpress.org/plugins/classic-editor/'=>'https://cn.wordpress.org/plugins/classic-editor/','WordPress Contributors'=>'WordPress 贡献者','Enables the WordPress classic editor and the old-style Edit Post screen with TinyMCE, Meta Boxes, etc. Supports the older plugins that extend this screen.'=>'启用WordPress经典编辑器和旧式的编辑文章页面，包括TinyMCE、Meta Boxes等。支持扩展此页面的旧插件。','Classic Editor'=>'经典编辑器','Edit &#8220;%s&#8221; in the classic editor'=>'在经典编辑器中编辑《%s》','Settings'=>'设置']];