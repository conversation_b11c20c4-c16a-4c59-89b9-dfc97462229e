# Translation of Plugins - Regenerate Thumbnails - Stable (latest release) in Chinese (China)
# This file is distributed under the same license as the Plugins - Regenerate Thumbnails - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-12-04 01:59:01+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Plugins - Regenerate Thumbnails - Stable (latest release)\n"

#. translators: Used for listing old sizes of currently registered thumbnails
#: includes/class-regeneratethumbnails-regenerator.php:696
msgid "%s (old)"
msgstr "%s（旧）"

#: includes/class-regeneratethumbnails-regenerator.php:622
msgid "Attachment %d"
msgstr "附件 %d"

#: includes/class-regeneratethumbnails-regenerator.php:584
msgid "Unable to load the metadata for this attachment."
msgstr "无法加载此附件的元数据。"

#: includes/class-regeneratethumbnails-regenerator.php:568
msgid "The current image editor cannot process this file type."
msgstr "当前的图像编辑器无法处理这种文件类型。"

#. translators: The relative upload path to the attachment.
#: includes/class-regeneratethumbnails-regenerator.php:150
msgid "The fullsize image file cannot be found in your uploads directory at <code>%s</code>. Without it, new thumbnail images can't be generated."
msgstr "在<code>%s</code>的上传目录中找不到完整的图片文件。没有它，不能生成新的缩略图图像。"

#: includes/class-regeneratethumbnails-regenerator.php:93
msgid "This attachment is a site icon and therefore the thumbnails shouldn't be touched."
msgstr "此附件是站点图标，因此不应触摸缩略图。"

#: includes/class-regeneratethumbnails-regenerator.php:82
msgid "This item is not an attachment."
msgstr "此项目不是附件。"

#: includes/class-regeneratethumbnails-regenerator.php:71
msgid "No attachment exists with that ID."
msgstr "该ID不存在任何附件。"

#: includes/class-regeneratethumbnails-rest-controller.php:262
msgid "The page number requested is larger than the number of pages available."
msgstr "请求的页数大于总页数。"

#: includes/class-regeneratethumbnails-rest-controller.php:64
msgid "Posts to process per loop. This is to control memory usage and you likely don't need to adjust this."
msgstr "每次处理的文章数量，此选项控制内存使用量，一般不需要调整。"

#: includes/class-regeneratethumbnails-rest-controller.php:58
msgid "Specific post IDs to update rather than any posts that use this attachment."
msgstr "需要更新的具体文章ID，而不是所有使用此附件的文章。"

#: includes/class-regeneratethumbnails-rest-controller.php:52
msgid "The types of posts to update. Defaults to all public post types."
msgstr "要更新的文章类型。 默认为所有公开文章类型。"

#: includes/class-regeneratethumbnails-rest-controller.php:47
msgid "Whether to update the image tags in any posts that make use of this attachment."
msgstr "是否更新任何使用此附件的文章中的img标签。"

#: includes/class-regeneratethumbnails-rest-controller.php:42
msgid "Whether to delete any old, now unregistered thumbnail files."
msgstr "是否删除任何旧的，现在未注册的缩略图文件。"

#: includes/class-regeneratethumbnails-rest-controller.php:37
msgid "Whether to only regenerate missing thumbnails. It's faster with this enabled."
msgstr "是否仅重新生成缺失的缩略图。启用此功能会更快。"

#: regenerate-thumbnails.php:495
msgctxt "bulk actions dropdown"
msgid "Regenerate Thumbnails"
msgstr "重新生成缩略图"

#: regenerate-thumbnails.php:433 regenerate-thumbnails.php:449
#: regenerate-thumbnails.php:473
msgid "Regenerate the thumbnails for this single image"
msgstr "为此图像重新生成缩略图"

#: regenerate-thumbnails.php:360
msgid "This tool won't be able to do anything because your server doesn't support image editing which means that WordPress can't create thumbnail images. Please ask your host to install the Imagick or GD PHP extensions."
msgstr "此工具无法执行任何操作，因为您的服务器不支持图片编辑，这意味着 WordPress 无法创建缩略图图片。请要求您的主机安装 Imagick 或 GD PHP 扩展。"

#: regenerate-thumbnails.php:331
msgid "This tool requires that JavaScript be enabled to work."
msgstr "该工具要求启用 JavaScript 才能工作。"

#: regenerate-thumbnails.php:321
msgid "This plugin requires WordPress 4.7 or newer. You are on version %1$s. Please <a href=\"%2$s\">upgrade</a>."
msgstr "这个插件需要 WordPress 4.7 或更新版本。您位于版本%1$s。请<a href=\"%2$s\">升级</a>。"

#: regenerate-thumbnails.php:294
msgid "Regenerate Thumbnails For The %d Selected Attachments"
msgstr "重新生成 %d个选定附件的缩略图"

#: regenerate-thumbnails.php:282
msgid "Unable to fetch a list of attachment IDs to process from the WordPress REST API. You can check your browser's console for details."
msgstr "无法获取要从 WordPress REST API 处理的附件ID列表。您可以查看浏览器的控制台了解详细信息。"

#: regenerate-thumbnails.php:281
msgid "{count} seconds"
msgstr "{count} 秒"

#: regenerate-thumbnails.php:280
msgid "{count} minutes"
msgstr "{count} 分"

#: regenerate-thumbnails.php:279
msgid "{count} hours"
msgstr "{count} 小时"

#: regenerate-thumbnails.php:278
msgid "All done in {duration}."
msgstr "在 {duration} 内完成。"

#: regenerate-thumbnails.php:277
msgid "Skipped Attachment ID {id}: {reason}"
msgstr "已跳过附件ID {id}: {reason}"

#: regenerate-thumbnails.php:276
msgid "Skipped Attachment ID {id} ({name}): {reason}"
msgstr "已跳过附件ID {id} ({name}): {reason}"

#: regenerate-thumbnails.php:275
msgid "Regenerated {name}"
msgstr "已重新生成 {name}"

#: regenerate-thumbnails.php:274
msgid "Resume"
msgstr "恢复"

#: regenerate-thumbnails.php:273
msgid "Pause"
msgstr "暂停"

#: regenerate-thumbnails.php:272
msgid "Regeneration Log"
msgstr "生成缩略图日志"

#: regenerate-thumbnails.php:271
msgid "Errors Encountered"
msgstr "出现错误"

#: regenerate-thumbnails.php:268
msgid "The attachment says it also has these thumbnail sizes but they are no longer in use by WordPress. You can probably safely have this plugin delete them, especially if you have this plugin update any posts that make use of this attachment."
msgstr "附件包含WordPress已不再适用的缩略图尺寸文档。你可以让此插件安全的删除他们，尤其是使用此插件更新所有包含此插件的文章时。"

#: regenerate-thumbnails.php:267
msgid "These are the currently registered thumbnail sizes, whether they exist for this attachment, and their filenames:"
msgstr "这些是当前注册的缩略图大小，某附件是否存在此尺寸以及其文件名的信息："

#: regenerate-thumbnails.php:266
msgid "There was an error regenerating this attachment. The error was: <em>{message}</em>"
msgstr "重新生成缩略图时出现错误：<em>{message}</em>"

#: regenerate-thumbnails.php:265
msgid "Error Regenerating"
msgstr "重新生成缩略图时发生错误"

#: regenerate-thumbnails.php:264
msgid "Done! Click here to go back."
msgstr "搞定！ 点击这里返回。"

#: regenerate-thumbnails.php:263
msgid "Regenerating…"
msgstr "重新生成中..."

#: regenerate-thumbnails.php:262
msgid "Update the content of posts that use this attachment to use the new sizes."
msgstr "更新使用此附件的帖子的内容以使用新尺寸。"

#: regenerate-thumbnails.php:261
msgid "Preview"
msgstr "预览"

#: regenerate-thumbnails.php:260
msgid "<code>{filename}</code> {width}×{height} pixels"
msgstr "<code>{filename}</code> {width}×{height} px"

#: regenerate-thumbnails.php:259
msgid "<strong>ERROR:</strong> {error}"
msgstr "<strong>错误:</strong> {error}"

#. translators: single image sdmin page title
#: regenerate-thumbnails.php:258
msgid "Regenerate Thumbnails: {name} — WordPress"
msgstr "Regenerate Thumbnails：{name} — WordPress"

#: regenerate-thumbnails.php:256 regenerate-thumbnails.php:433
#: regenerate-thumbnails.php:449 regenerate-thumbnails.php:473
msgctxt "action for a single image"
msgid "Regenerate Thumbnails"
msgstr "重制缩略图"

#: regenerate-thumbnails.php:253
msgid "Another alternative is to use the <a href=\"{url-photon}\">Photon</a> functionality that comes with the <a href=\"{url-jetpack}\">Jetpack</a> plugin. It generates thumbnails on-demand using WordPress.com's infrastructure. <em>Disclaimer: The author of this plugin, Regenerate Thumbnails, is an employee of the company behind WordPress.com and Jetpack but I would recommend it even if I wasn't.</em>"
msgstr "另一种选择是使用<a href=\"{url-jetpack}\">Jetpack</a>插件包含的<a href=\"{url-photon}\">Photon</a>功能。它使用WordPress.com的基础架构按需生成缩略图。<em>免责声明：该插件的作者Regenerate Thumbnails是WordPress.com和Jetpack背后公司的雇员，但即使不是我也推荐这样做。</em>"

#: regenerate-thumbnails.php:252
msgid "If you have <a href=\"{url-cli}\">command-line</a> access to your site's server, consider using <a href=\"{url-wpcli}\">WP-CLI</a> instead of this tool. It has a built-in <a href=\"{url-wpcli-regenerate}\">regenerate command</a> that works similarly to this tool but should be significantly faster since it has the advantage of being a command-line tool."
msgstr "如果您有<a href=\"{url-cli}\">命令行</a>访问您网站的服务器，请考虑使用<a href=\"{url-wpcli}\"> WP-CLI </a>这个工具。它具有内置的<a href=\"{url-wpcli-regenerate}\">重新生成命令</a>，其工作方式与此工具类似，但应该快得多，因为它具有作为命令行工具的优势。"

#: regenerate-thumbnails.php:251
msgid "Alternatives"
msgstr "备择方案"

#: regenerate-thumbnails.php:250
msgid "These are all of the thumbnail sizes that are currently registered:"
msgstr "这些都是当前注册的所有缩略图尺寸："

#: regenerate-thumbnails.php:249
msgid "Thumbnail Sizes"
msgstr "缩略图大小"

#: regenerate-thumbnails.php:248
msgid "Regenerate Thumbnails For The {attachmentCount} Featured Images Only"
msgstr "仅为{attachmentCount}精选图片重新生成缩略图"

#: regenerate-thumbnails.php:247
msgid "Regenerate Thumbnails For Featured Images Only"
msgstr "仅为特色图片重新生成缩略图"

#: regenerate-thumbnails.php:246
msgid "Regenerate Thumbnails For All {attachmentCount} Attachments"
msgstr "为所有{attachmentCount}附件重新生成缩略图"

#: regenerate-thumbnails.php:245
msgid "Regenerate Thumbnails For All Attachments"
msgstr "重新生成所有附件的缩略图"

#: regenerate-thumbnails.php:244
msgid "Update the content of posts to use the new sizes."
msgstr "更新文章的内容以使用新尺寸。"

#. translators: %s: Media library URL
#: regenerate-thumbnails.php:241
msgid "To process a specific image, visit your media library and click the &quot;Regenerate Thumbnails&quot; link or button. To process multiple specific images, make sure you're in the <a href=\"%s\">list view</a> and then use the Bulk Actions dropdown after selecting one or more images."
msgstr "要处理特定图像，请访问媒体库并单击“重新生成缩略图” 链接或按钮。 要处理多个特定图像，请确保您在<a href=\"%s\">列表视图</a>中，然后在选择一个或多个图像后使用批量操作下拉列表。"

#. translators: %s: Media options URL
#: regenerate-thumbnails.php:236
msgid "When you change WordPress themes or change the sizes of your thumbnails at <a href=\"%s\">Settings → Media</a>, images that you have previously uploaded to you media library will be missing thumbnail files for those new image sizes. This tool will allow you to create those missing thumbnail files for all images."
msgstr "当您更换主题或在<a href=\"%s\">设置 → 媒体</a>中更改缩略图的大小时，之前上传到媒体库的图像将缺少这些新尺寸的缩略图文件。此工具将可以让您为所有图像创建缺少的缩略图文件。"

#: regenerate-thumbnails.php:231
msgid "proportionally resized to fit inside dimensions"
msgstr "按比例调整大小以适应图像尺寸"

#: regenerate-thumbnails.php:230
msgid "cropped to fit"
msgstr "裁剪到适合大小"

#: regenerate-thumbnails.php:229
msgid "<strong>{label}:</strong> {width}×{height} pixels (thumbnail would be larger than original)"
msgstr "<strong>{label}:</strong> {width}×{height} 像素 (缩略图会比原始图片大)"

#: regenerate-thumbnails.php:228
msgid "<strong>{label}:</strong> {width}×{height} pixels <code>{filename}</code>"
msgstr "<strong>{label}:</strong> {width}×{height} 像素<code>{filename}</code>"

#: regenerate-thumbnails.php:227
msgid "<strong>{label}:</strong> {width}×{height} pixels ({cropMethod}) <code>{filename}</code>"
msgstr "<strong>{label}:</strong> {width}×{height} 像素 ({cropMethod}) <code>{filename}</code>"

#: regenerate-thumbnails.php:226
msgid "<strong>{label}:</strong> {width}×{height} pixels ({cropMethod})"
msgstr "<strong>{label}:</strong> {width}×{height} 像素 ({cropMethod})"

#: regenerate-thumbnails.php:225
msgid "Delete thumbnail files for old unregistered sizes in order to free up server space. This may result in broken images in your posts and pages."
msgstr "删除旧的未注册尺寸的缩略图文件，以释放服务器空间。 此选项可能会导致文章和页面中的图像损坏。"

#: regenerate-thumbnails.php:224
msgid "Skip regenerating existing correctly sized thumbnails (faster)."
msgstr "不再重新生成已存在的缩略图（更快）。"

#: regenerate-thumbnails.php:223 regenerate-thumbnails.php:334
msgid "Loading…"
msgstr "正在加载…"

#: regenerate-thumbnails.php:166
msgctxt "admin menu entry title"
msgid "Regenerate Thumbnails"
msgstr "重制缩略图"

#: regenerate-thumbnails.php:165 regenerate-thumbnails.php:317
msgctxt "admin page title"
msgid "Regenerate Thumbnails"
msgstr "重制缩略图"

#. Author URI of the plugin
msgid "https://alex.blog/"
msgstr "https://alex.blog/"

#. Author of the plugin
msgid "Alex Mills (Viper007Bond)"
msgstr "Alex Mills (Viper007Bond)"

#. Description of the plugin
msgid "Regenerate the thumbnails for one or more of your image uploads. Useful when changing their sizes or your theme."
msgstr "重新生成一个或多个图片上传的缩略图。更改尺寸或主题时很有用。"

#. Plugin URI of the plugin
msgid "https://alex.blog/wordpress-plugins/regenerate-thumbnails/"
msgstr "https://alex.blog/wordpress-plugins/regenerate-thumbnails/"

#. Plugin Name of the plugin
msgid "Regenerate Thumbnails"
msgstr "重制缩略图"