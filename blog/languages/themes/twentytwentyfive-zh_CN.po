# Translation of Themes - Twenty Twenty-Five in Chinese (China)
# This file is distributed under the same license as the Themes - Twenty Twenty-Five package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-12-14 10:22:25+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Twenty Twenty-Five\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Five emphasizes simplicity and adaptability. It offers flexible design options, supported by a variety of patterns for different page types, such as services and landing pages, making it ideal for building personal blogs, professional portfolios, online magazines, or business websites. Its templates cater to various blog styles, from text-focused to image-heavy layouts. Additionally, it supports international typography and diverse color palettes, ensuring accessibility and customization for users worldwide."
msgstr "二〇二五强调简洁性和适应性。它提供灵活的设计选项，并为不同的页面类型（如服务和着陆页面）提供多种样板支持，使其成为建立个人博客、专业作品集、在线杂志或商业网站的理想之选。它的模板可满足各种博客风格的需要，从注重文字的布局到注重图片的布局。此外，它还支持国际化的排版和多样化的调色板，确保全球用户的可访问性和可定制性。"

#. Theme Name of the theme
#: style.css patterns/footer-columns.php:66 patterns/footer-newsletter.php:42
#: patterns/footer.php:75 patterns/page-portfolio-home.php:226
#, gp-priority: high
msgid "Twenty Twenty-Five"
msgstr "二〇二五"

#: patterns/text-faqs.php:35 patterns/text-faqs.php:51
#: patterns/text-faqs.php:71 patterns/text-faqs.php:87
msgctxt "Answer in the FAQs pattern."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist."
msgstr "这本精美的合辑展示了各种各样的照片，捕捉了不同时代和文化的精髓，反映了每位艺术家的独特风格和观点。"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "无标题页面"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "侧边栏"

#: theme.json
msgctxt "Template part name"
msgid "Footer Newsletter"
msgstr "页脚通讯"

#: theme.json
msgctxt "Template part name"
msgid "Footer Columns"
msgstr "页脚栏"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "页脚"

#: theme.json
msgctxt "Template part name"
msgid "Header with large title"
msgstr "大标题页眉"

#: theme.json
msgctxt "Template part name"
msgid "Vertical Header"
msgstr "垂直页眉"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "页眉"

#: styles/02-noon.json styles/typography/typography-preset-1.json
msgctxt "Font family name"
msgid "Beiruti"
msgstr "Beiruti"

#: styles/05-twilight.json styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Roboto Slab"
msgstr "Roboto Slab"

#: styles/04-afternoon.json styles/07-sunrise.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-6.json
msgctxt "Font family name"
msgid "Platypi"
msgstr "Platypi"

#: styles/04-afternoon.json styles/06-morning.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-5.json
msgctxt "Font family name"
msgid "Ysabeau Office"
msgstr "Ysabeau Office"

#: styles/08-midnight.json styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Fira Sans"
msgstr "Fira Sans"

#: styles/03-dusk.json styles/typography/typography-preset-2.json theme.json
msgctxt "Font family name"
msgid "Fira Code"
msgstr "Fira Code"

#: styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Vollkorn"
msgstr "Vollkorn"

#: styles/02-noon.json styles/06-morning.json styles/07-sunrise.json
#: styles/08-midnight.json styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-6.json
#: styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Literata"
msgstr "Literata"

#: styles/05-twilight.json styles/typography/typography-preset-4.json
#: theme.json
msgctxt "Font family name"
msgid "Manrope"
msgstr "Manrope"

#: theme.json
msgctxt "Space size name"
msgid "XX-Large"
msgstr "加加大号"

#: theme.json
msgctxt "Space size name"
msgid "X-Large"
msgstr "加大号"

#: theme.json
msgctxt "Space size name"
msgid "Large"
msgstr "大号"

#: theme.json
msgctxt "Space size name"
msgid "Regular"
msgstr "普通"

#: theme.json
msgctxt "Space size name"
msgid "Small"
msgstr "小号"

#: theme.json
msgctxt "Space size name"
msgid "X-Small"
msgstr "极小号"

#: theme.json
msgctxt "Space size name"
msgid "Tiny"
msgstr "最小号"

#: styles/typography/typography-preset-7.json
msgctxt "Style variation name"
msgid "Literata & Fira Sans"
msgstr "Literata & Fira Sans"

#: styles/typography/typography-preset-6.json
msgctxt "Style variation name"
msgid "Platypi & Literata"
msgstr "Platypi & Literata"

#: styles/typography/typography-preset-5.json
msgctxt "Style variation name"
msgid "Literata & Ysabeau Office"
msgstr "Literata & Ysabeau Office"

#: styles/typography/typography-preset-4.json
msgctxt "Style variation name"
msgid "Roboto Slab & Manrope"
msgstr "Roboto Slab & Manrope"

#: styles/typography/typography-preset-3.json
msgctxt "Style variation name"
msgid "Platypi & Ysabeau Office"
msgstr "Platypi & Ysabeau Office"

#: styles/typography/typography-preset-2.json
msgctxt "Style variation name"
msgid "Vollkorn & Fira Code"
msgstr "Vollkorn & Fira Code"

#: styles/typography/typography-preset-1.json
msgctxt "Style variation name"
msgid "Beiruti & Literata"
msgstr "Beiruti & Literata"

#: styles/sections/section-5.json
msgctxt "Style variation name"
msgid "Style 5"
msgstr "样式 5"

#: styles/sections/section-4.json
msgctxt "Style variation name"
msgid "Style 4"
msgstr "样式 4"

#: styles/sections/section-3.json
msgctxt "Style variation name"
msgid "Style 3"
msgstr "样式 3"

#: styles/sections/section-2.json
msgctxt "Style variation name"
msgid "Style 2"
msgstr "样式 2"

#: styles/sections/section-1.json
msgctxt "Style variation name"
msgid "Style 1"
msgstr "样式 1"

#: styles/blocks/post-terms-1.json
msgctxt "Style variation name"
msgid "Pill shaped"
msgstr "药丸形状"

#: styles/blocks/03-annotation.json
msgctxt "Style variation name"
msgid "Annotation"
msgstr "注释"

#: styles/blocks/02-subtitle.json
msgctxt "Style variation name"
msgid "Subtitle"
msgstr "字幕"

#: styles/blocks/01-display.json
msgctxt "Style variation name"
msgid "Display"
msgstr "显示"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Duotone name"
msgid "Midnight filter"
msgstr "午夜滤镜"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Style variation name"
msgid "Midnight"
msgstr "午夜"

#: styles/07-sunrise.json styles/colors/07-sunrise.json
msgctxt "Style variation name"
msgid "Sunrise"
msgstr "日出"

#: styles/06-morning.json styles/colors/06-morning.json
msgctxt "Style variation name"
msgid "Morning"
msgstr "早晨"

#: styles/05-twilight.json styles/colors/05-twilight.json
msgctxt "Style variation name"
msgid "Twilight"
msgstr "黄昏"

#: styles/04-afternoon.json styles/colors/04-afternoon.json
msgctxt "Style variation name"
msgid "Afternoon"
msgstr "下午"

#: styles/03-dusk.json styles/colors/03-dusk.json
msgctxt "Style variation name"
msgid "Dusk"
msgstr "黄昏"

#: styles/02-noon.json styles/04-afternoon.json styles/05-twilight.json
#: styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json theme.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "特大"

#: styles/02-noon.json styles/04-afternoon.json styles/05-twilight.json
#: styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "特大号"

#: styles/02-noon.json styles/04-afternoon.json styles/05-twilight.json
#: styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "大号"

#: styles/02-noon.json styles/04-afternoon.json styles/05-twilight.json
#: styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "中号"

#: styles/02-noon.json styles/04-afternoon.json styles/05-twilight.json
#: styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "小"

#: styles/02-noon.json styles/colors/02-noon.json
msgctxt "Style variation name"
msgid "Noon"
msgstr "中午"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Accent 6"
msgstr "强调色 6"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Accent 5"
msgstr "强调色 5"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Accent 4"
msgstr "强调色 4"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Accent 3"
msgstr "强调色 3"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Accent 2"
msgstr "强调色 2"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Accent 1"
msgstr "强调色 1"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "对比色"

#: styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "基准色"

#: styles/01-evening.json styles/colors/01-evening.json
msgctxt "Style variation name"
msgid "Evening"
msgstr "晚间"

#: patterns/vertical-header.php
msgctxt "Pattern description"
msgid "Vertical Header with site title and navigation"
msgstr "带网站标题和导航的垂直页眉"

#: patterns/vertical-header.php
msgctxt "Pattern title"
msgid "Vertical header"
msgstr "垂直标题"

#: patterns/text-faqs.php:83
msgctxt "Question in the FAQs pattern."
msgid "Are signed copies available?"
msgstr "是否提供签名本？"

#: patterns/text-faqs.php:67
msgctxt "Question in the FAQs pattern."
msgid "When will The Stories Book be released?"
msgstr "《故事集》何时发行？"

#: patterns/text-faqs.php:47
msgctxt "Question in the FAQs pattern."
msgid "How much does The Stories Book cost?"
msgstr "《故事集》的价格是多少？"

#: patterns/text-faqs.php:31
msgctxt "Question in the FAQs pattern."
msgid "What is The Stories Book about?"
msgstr "《故事集》讲述了什么？"

#: patterns/text-faqs.php:21
msgctxt "Heading of the FAQs pattern."
msgid "Frequently Asked Questions"
msgstr "常见问题"

#: patterns/text-faqs.php
msgctxt "Pattern description"
msgid "A FAQs section with a FAQ heading and list of questions and answers."
msgstr "常见问题专区，包含「常见问题」标题，以及问答列表。"

#: patterns/text-faqs.php
msgctxt "Pattern title"
msgid "FAQs"
msgstr "常问问题"

#: patterns/testimonials-large.php:47
msgctxt "Alt text for testimonial image."
msgid "Picture of a person typing on a typewriter."
msgstr "一个人在打字机上打字的图片。"

#: patterns/testimonials-large.php:24
msgctxt "Testimonial heading."
msgid "What people are saying"
msgstr "人们的评价"

#: patterns/testimonials-large.php
msgctxt "Pattern description"
msgid "A testimonial with a large image on the right."
msgstr "右边带有大图片的推荐。"

#: patterns/testimonials-large.php
msgctxt "Pattern title"
msgid "Review with large image on right"
msgstr "右边带有大图片的评论"

#: patterns/testimonials-6-col.php:18
msgctxt "Testimonial section heading."
msgid "What people are saying"
msgstr "人们的评价"

#: patterns/testimonials-6-col.php
msgctxt "Pattern description"
msgid "A section with three columns and two rows, each containing a testimonial and citation."
msgstr "三栏两行的版面，每行包含推荐和引文。"

#: patterns/testimonials-6-col.php
msgctxt "Pattern title"
msgid "3 column layout with 6 testimonials"
msgstr "3 栏布局，包含 6 篇推荐文章"

#: patterns/testimonials-2-col.php:67 patterns/testimonials-6-col.php:34
#: patterns/testimonials-6-col.php:51 patterns/testimonials-6-col.php:68
#: patterns/testimonials-6-col.php:89 patterns/testimonials-6-col.php:104
#: patterns/testimonials-6-col.php:119
msgctxt "Sample testimonial citation."
msgid "Otto Reid <br><sub>Springfield, IL</sub>"
msgstr "奥托-里德 <br><sub>伊利诺伊州斯普林菲尔德</sub>"

#: patterns/testimonials-2-col.php:65 patterns/testimonials-6-col.php:30
#: patterns/testimonials-6-col.php:47 patterns/testimonials-6-col.php:64
#: patterns/testimonials-6-col.php:85 patterns/testimonials-6-col.php:101
#: patterns/testimonials-6-col.php:116
msgctxt "Sample testimonial."
msgid "“Amazing quality and care. I love all your products.”"
msgstr "「令人惊叹的质量和关怀。我喜欢你们的所有产品。」"

#: patterns/testimonials-2-col.php:38 patterns/testimonials-large.php:36
msgctxt "Sample testimonial citation."
msgid "Jo Mulligan <br /><sub>Atlanta, GA</sub>"
msgstr "乔-穆里根<br /><sub>亚特兰大，佐治亚州</sub>"

#: patterns/testimonials-2-col.php:36 patterns/testimonials-large.php:32
msgctxt "Sample testimonial."
msgid "“Superb product and customer service!”"
msgstr "「一流的产品和客户服务！」"

#: patterns/testimonials-2-col.php:26 patterns/testimonials-2-col.php:55
msgctxt "Alt text for testimonial image."
msgid "Picture of a person"
msgstr "人物图片"

#: patterns/testimonials-2-col.php
msgctxt "Pattern description"
msgid "Two columns with testimonials and avatars."
msgstr "两栏推荐和头像。"

#: patterns/testimonials-2-col.php
msgctxt "Pattern title"
msgid "2 columns with avatar"
msgstr "带头像的 2 栏"

#: patterns/template-single-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned single post"
msgstr "右对齐的单篇文章"

#: patterns/template-single-text-blog.php
msgctxt "Pattern title"
msgid "Text-only blog, single post"
msgstr "纯文字博客，单篇文章"

#: patterns/template-single-photo-blog.php:79
msgid "Next Photo"
msgstr "下一张照片"

#: patterns/template-single-photo-blog.php:78
msgid "Previous Photo"
msgstr "上一张照片"

#: patterns/template-single-photo-blog.php:61
msgctxt "Prefix before one or more tags. The tags are displayed in a separate block on the next line."
msgid "Tagged:"
msgstr "已标记："

#: patterns/template-single-photo-blog.php:53
msgctxt "Prefix before one or more categories. The categories are displayed in a separate block on the next line."
msgid "Categories:"
msgstr "分类："

#: patterns/template-single-photo-blog.php:42
msgctxt "Prefix before the author name. The post author name is displayed in a separate block on the next line."
msgid "Posted by"
msgstr "发布者"

#: patterns/template-single-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog single post"
msgstr "照片博客单篇文章"

#: patterns/template-single-offset.php:40
#: patterns/template-single-photo-blog.php:36
msgctxt "Prefix before the post date block."
msgid "Published on"
msgstr "发表于"

#: patterns/template-single-offset.php
msgctxt "Pattern title"
msgid "Offset post without featured image"
msgstr "无特色图片的偏移文章"

#: patterns/template-single-news-blog.php
msgctxt "Pattern title"
msgid "News blog single post with sidebar"
msgstr "带侧边栏的新闻博客单篇文章"

#: patterns/template-home-with-sidebar-news-blog.php:88
#: patterns/template-single-left-aligned-content.php:56
#: patterns/template-single-news-blog.php:39
msgctxt "Separator between date and categories."
msgid "·"
msgstr "·"

#: patterns/template-single-left-aligned-content.php:31
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "by"
msgstr "由"

#: patterns/template-single-left-aligned-content.php
msgctxt "Pattern title"
msgid "Post with left-aligned content"
msgstr "带左对齐内容的文章"

#: patterns/template-search-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned blog, search"
msgstr "右对齐博客，搜索"

#: patterns/template-search-text-blog.php
msgctxt "Pattern title"
msgid "Text-only blog, search"
msgstr "纯文字博客，搜索"

#: patterns/template-search-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog search results"
msgstr "照片博客搜索结果"

#: patterns/template-search-news-blog.php
msgctxt "Pattern title"
msgid "News blog search results"
msgstr "新闻博客搜索结果"

#: patterns/template-query-loop.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column, with featured image and post date."
msgstr "一个列表文章，1 栏，有特色图片和文章日期。"

#: patterns/template-query-loop.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "职位列表，1 栏"

#: patterns/template-query-loop-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned posts"
msgstr "右对齐文章"

#: patterns/template-query-loop-text-blog.php
msgctxt "Pattern title"
msgid "Text-only blog, posts"
msgstr "纯文字博客，文章"

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns, with only featured images."
msgstr "一个列表文章，3 栏，只有精选图片。"

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog posts"
msgstr "图片博客文章"

#: patterns/template-query-loop-news-blog.php:49
msgid "Older Posts"
msgstr "旧文章"

#: patterns/template-query-loop-news-blog.php:45
msgid "Newer Posts"
msgstr "较新文章"

#: patterns/template-query-loop-news-blog.php:30
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "Written by"
msgstr "作者"

#: patterns/template-query-loop-news-blog.php
msgctxt "Pattern title"
msgid "News blog query loop"
msgstr "新闻博客查询循环"

#: patterns/template-page-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Page template for the right-aligned blog"
msgstr "右对齐博客的页面模板"

#: patterns/template-page-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog page"
msgstr "照片博客页面"

#: patterns/template-home-with-sidebar-news-blog.php:42
msgid "The Latest"
msgstr "最新"

#: patterns/template-home-with-sidebar-news-blog.php
msgctxt "Pattern title"
msgid "News blog with sidebar"
msgstr "新闻博客侧边栏"

#: patterns/template-home-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Homepage for right-aligned blog"
msgstr "右对齐博客首页"

#: patterns/template-home-text-blog.php
msgctxt "Pattern title"
msgid "Text-only blog, home"
msgstr "纯文字博客首页"

#: patterns/template-home-posts-grid-news-blog.php:114
msgid "Architecture"
msgstr "建筑"

#: patterns/template-home-posts-grid-news-blog.php
msgctxt "Pattern title"
msgid "News blog with featured posts grid"
msgstr "新闻博客文章精选网格"

#: patterns/template-home-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog home"
msgstr "图片博客首页"

#: patterns/template-home-news-blog.php
msgctxt "Pattern title"
msgid "News blog home"
msgstr "新闻博客首页"

#: patterns/template-archive-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Archive for the right-aligned blog"
msgstr "右对齐博客存档"

#: patterns/template-archive-text-blog.php
msgctxt "Pattern title"
msgid "Text-only blog, archive"
msgstr "纯文字博客归档"

#: patterns/template-archive-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog archive"
msgstr "图片博客归档"

#: patterns/template-archive-news-blog.php
msgctxt "Pattern title"
msgid "News blog archive"
msgstr "新闻博客归档"

#: patterns/template-404-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned blog, 404"
msgstr "右对齐博客，404"

#: patterns/services-team-photos.php:50
msgid "Man in hat, standing in front of a building."
msgstr "戴帽子的男子，站在建筑物前。"

#: patterns/services-team-photos.php:44
msgid "Picture of a person typing on a typewriter."
msgstr "一个人在打字机上打字的图片。"

#: patterns/services-team-photos.php:38
msgid "Portrait of a nurse"
msgstr "护士肖像"

#: patterns/services-team-photos.php:21
msgid "Our small team is a group of driven, detail-oriented people who are passionate about their customers."
msgstr "我们的小团队由一群积极进取、注重细节、对客户充满热情的人组成。"

#: patterns/services-team-photos.php
msgctxt "Pattern description"
msgid "Display team photos in a services section with grid layout."
msgstr "在网格布局的服务版块中展示团队照片。"

#: patterns/services-team-photos.php
msgctxt "Pattern title"
msgid "Services, team photos"
msgstr "服务、团队照片"

#: patterns/services-subscriber-only-section.php:69
msgid "Smartphones capturing a scenic wildflower meadow with trees"
msgstr "用智能手机拍摄风景优美的野花草地和树木"

#: patterns/services-subscriber-only-section.php:55
msgid "View plans"
msgstr "查看计划"

#: patterns/services-subscriber-only-section.php:21
msgid "Subscribe to get unlimited access"
msgstr "订阅即可无限制访问"

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern description"
msgid "A subscriber-only section highlighting exclusive services and offerings."
msgstr "订阅者专区，重点介绍独家服务和产品。"

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern title"
msgid "Services, subscriber only section"
msgstr "服务，订阅者专区"

#: patterns/services-3-col.php:68
msgid "Deliver"
msgstr "交付"

#: patterns/services-3-col.php:50
msgid "Assemble"
msgstr "组合"

#: patterns/services-3-col.php:36 patterns/services-3-col.php:54
#: patterns/services-3-col.php:72
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience"
msgstr "就像花朵在意想不到的地方绽放，每个故事都在美丽和坚韧中展开"

#: patterns/services-3-col.php:32
msgid "Collect"
msgstr "收集"

#: patterns/services-3-col.php:27 patterns/services-3-col.php:45
#: patterns/services-3-col.php:63
msgid "Image for service"
msgstr "服务图片"

#: patterns/services-3-col.php:17
msgid "Our services"
msgstr "我们的服务"

#: patterns/services-3-col.php
msgctxt "Pattern description"
msgid "Three columns with images and text to showcase services."
msgstr "三栏图片和文字展示服务。"

#: patterns/services-3-col.php
msgctxt "Pattern title"
msgid "Services, 3 columns"
msgstr "服务，3 栏"

#: patterns/pricing-3-col.php:125
msgid "40€"
msgstr "40€"

#: patterns/pricing-3-col.php:117
msgid "Get access to our paid newsletter and an unlimited pass."
msgstr "获得我们的付费时事通讯和无限通行证。"

#: patterns/pricing-3-col.php:113
msgctxt "Name of membership package."
msgid "Expert"
msgstr "专家"

#: patterns/pricing-3-col.php:89 patterns/pricing-3-col.php:129
msgid "Month"
msgstr "月"

#: patterns/pricing-3-col.php:85
msgid "20€"
msgstr "20€"

#: patterns/pricing-3-col.php:77
msgid "Get access to our paid newsletter and a limited pass for one event."
msgstr "获取我们的付费时事通讯和一个事件的有限通行证。"

#: patterns/pricing-3-col.php:41
msgid "Get access to our free articles and weekly newsletter."
msgstr "获取我们的免费文章和每周时事通讯。"

#: patterns/pricing-3-col.php:19
msgid "Choose your membership"
msgstr "选择您的会员资格"

#: patterns/pricing-3-col.php
msgctxt "Pattern description"
msgid "A three-column boxed pricing table designed to showcase services, descriptions, and pricing options."
msgstr "三栏式定价表，用于展示服务、描述和定价选项。"

#: patterns/pricing-3-col.php
msgctxt "Pattern title"
msgid "Pricing, 3 columns"
msgstr "定价，3 栏"

#: patterns/pricing-2-col.php:82
msgid "20€/month"
msgstr "20€ /月"

#: patterns/pricing-2-col.php:78 patterns/pricing-3-col.php:73
msgctxt "Name of membership package."
msgid "Single"
msgstr "单个"

#: patterns/pricing-2-col.php:68 patterns/pricing-2-col.php:112
#: patterns/pricing-3-col.php:59 patterns/pricing-3-col.php:99
#: patterns/pricing-3-col.php:139
msgctxt "Button text, refers to joining a community. Verb."
msgid "Join"
msgstr "加入"

#: patterns/pricing-2-col.php:60 patterns/pricing-2-col.php:104
#: patterns/services-subscriber-only-section.php:43
msgid "Join our forums."
msgstr "加入我们的论坛。"

#: patterns/pricing-2-col.php:56 patterns/pricing-2-col.php:100
#: patterns/services-subscriber-only-section.php:39
msgid "An elegant addition of home decor collection."
msgstr "为家居装饰系列增添一份优雅。"

#: patterns/pricing-2-col.php:52 patterns/pricing-2-col.php:96
#: patterns/services-subscriber-only-section.php:35
msgid "Get a free tote bag."
msgstr "免费获得一个手提袋。"

#: patterns/pricing-2-col.php:48 patterns/pricing-2-col.php:92
#: patterns/services-subscriber-only-section.php:31
msgid "Join our IRL events."
msgstr "加入我们的 IRL 事件。"

#: patterns/pricing-2-col.php:44 patterns/pricing-2-col.php:88
#: patterns/services-subscriber-only-section.php:27
msgid "Get access to our paid articles and weekly newsletter."
msgstr "获取我们的付费文章和每周时事通讯。"

#: patterns/pricing-2-col.php:38 patterns/pricing-3-col.php:49
msgid "0€"
msgstr "0€"

#: patterns/pricing-2-col.php:34 patterns/pricing-3-col.php:37
msgid "Free"
msgstr "免费"

#: patterns/pricing-2-col.php:22
#: patterns/services-subscriber-only-section.php:61
msgid "Cancel or pause anytime."
msgstr "随时取消或暂停。"

#: patterns/pricing-2-col.php:18 patterns/pricing-3-col.php:23
msgid "Pricing"
msgstr "定价"

#: patterns/pricing-2-col.php
msgctxt "Pattern description"
msgid "Pricing section with two columns, pricing plan, description, and call-to-action buttons."
msgstr "定价部分有两栏，包括定价计划、说明和「按钮动作」。"

#: patterns/pricing-2-col.php
msgctxt "Pattern title"
msgid "Pricing, 2 columns"
msgstr "定价，两栏"

#: patterns/post-navigation.php:17 patterns/post-navigation.php:18
#: patterns/template-single-left-aligned-content.php:78
#: patterns/template-single-left-aligned-content.php:79
#: patterns/template-single-news-blog.php:95
#: patterns/template-single-news-blog.php:96
#: patterns/template-single-offset.php:61
#: patterns/template-single-offset.php:62
#: patterns/template-single-photo-blog.php:76
#: patterns/template-single-photo-blog.php:77
#: patterns/template-single-text-blog.php:36
#: patterns/template-single-text-blog.php:37
#: patterns/template-single-vertical-header-blog.php:82
#: patterns/template-single-vertical-header-blog.php:83
msgid "Post navigation"
msgstr "文章导航"

#: patterns/post-navigation.php
msgctxt "Pattern description"
msgid "Next and previous post links."
msgstr "下一页和上一页文章链接。"

#: patterns/post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "文章导航"

#: patterns/page-shop-home.php
msgctxt "Pattern description"
msgid "A shop homepage pattern."
msgstr "商店主页样板。"

#: patterns/page-shop-home.php
msgctxt "Pattern title"
msgid "Shop homepage"
msgstr "商店主页"

#: patterns/page-portfolio-home.php:229
msgctxt "Phone number."
msgid "****** 349 1806"
msgstr "****** 349 1806"

#: patterns/page-portfolio-home.php:229
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/page-portfolio-home.php:27
msgid "My name is Anna Möller and these are some of my photo projects."
msgstr "我叫安娜-莫勒（Anna Möller），这些是我的一些照片项目。"

#: patterns/page-portfolio-home.php
msgctxt "Pattern description"
msgid "A portfolio homepage pattern."
msgstr "作品集主页样板。"

#: patterns/page-portfolio-home.php
msgctxt "Pattern title"
msgid "Portfolio homepage"
msgstr "作品集主页"

#: patterns/page-link-in-bio-with-tight-margins.php:42
msgid "I’m Asahachi Kōno, a Japanese photographer, a member of Los Angeles’s Japanese Camera Pictorialists of California. Before returning to Japan, I worked as a photo retoucher."
msgstr "我是 Asahachi Kōno，一名日本摄影师，洛杉矶加利福尼亚日本相机画报摄影师协会会员。回国前，我是一名照片修图师。"

#: patterns/page-link-in-bio-with-tight-margins.php:27
msgid "Black and white photo focusing on a woman and a child from afar."
msgstr "黑白照片，从远处聚焦一个女人和一个孩子。"

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern description"
msgid "A full-width, full-height link in bio section with an image, a paragraph and social links."
msgstr "带有图像、段落和社交链接的全宽、全高链接的个人简介部分"

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern title"
msgid "Link in bio with tight margins"
msgstr "生物链接中的紧边距"

#: patterns/page-link-in-bio-wide-margins.php:38
msgctxt "Pattern placeholder text."
msgid "I’m Nora, a dedicated public interest attorney based in Denver. I’m a graduate of Stanford University."
msgstr "我是诺拉（Nora），丹佛的一名专职公益律师。我毕业于斯坦福大学。"

#: patterns/page-link-in-bio-wide-margins.php:34
msgid "Nora Winslow Keene"
msgstr "诺拉-温斯洛-基恩"

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern description"
msgid "A link in bio landing page with social links, a profile photo and a brief description."
msgstr "包含社交链接、个人资料照片和简短描述的个人简介着陆页链接。"

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern title"
msgid "Link in bio with profile, links and wide margins"
msgstr "个人简介中的链接，带有简介、链接和宽边距"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:57
msgid "Photo of a woman worker."
msgstr "一位女工的照片。"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:30
msgid "Lewis W. Hine studied sociology before moving to New York in 1901 to work at the Ethical Culture School, where he took up photography to enhance his teaching practices"
msgstr "刘易斯-W-海涅曾学习社会学，1901 年移居纽约，在伦理文化学校工作，并在那里学习摄影以提高教学水平。"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:26
msgid "Lewis Hine"
msgstr "刘易斯-海涅"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern description"
msgid "A link in bio landing page with a heading, paragraph, links and a full height image."
msgstr "带有标题、段落、链接和全高图片的个人简介着陆页链接。"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern title"
msgid "Link in bio heading, paragraph, links and full-height image"
msgstr "个人简介标题、段落、链接和全高图像中的链接"

#: patterns/page-landing-podcast.php
msgctxt "Pattern description"
msgid "A landing page for the podcast with a hero section, description, logos, grid with videos and newsletter signup."
msgstr "包含主人公部分、描述、徽标、带视频的网格和时事通讯注册的播客着陆页面。"

#: patterns/page-landing-podcast.php
msgctxt "Pattern title"
msgid "Landing page for podcast"
msgstr "播客着陆页面"

#: patterns/page-landing-event.php
msgctxt "Pattern description"
msgid "A landing page for the event with a hero section, description, FAQs and call to action."
msgstr "活动着陆页面，包括主人公部分、说明、常见问题和行动号召。"

#: patterns/page-landing-event.php
msgctxt "Pattern title"
msgid "Landing page for event"
msgstr "活动着陆页面"

#: patterns/page-landing-book.php
msgctxt "Pattern description"
msgid "A landing page for the book with a hero section, pre-order links, locations, FAQs and newsletter signup."
msgstr "该书的着陆页面，包括主人公部分、预购链接、地点、常问问题和电子报订阅。"

#: patterns/page-landing-book.php
msgctxt "Pattern title"
msgid "Landing page for book"
msgstr "图书着陆页面"

#: patterns/page-cv-bio.php:47
msgctxt "Link to a page with information about what the person is working on right now."
msgid "Now"
msgstr "现在"

#: patterns/page-cv-bio.php:47
msgid "LinkedIn"
msgstr "链接"

#: patterns/page-cv-bio.php:43 patterns/page-link-in-bio-wide-margins.php:24
#: patterns/services-team-photos.php:32
msgid "Woman on beach, splashing water."
msgstr "海滩上的女子，水花四溅。"

#: patterns/page-cv-bio.php:31
msgctxt "Pattern placeholder text."
msgid "My name is Nora Winslow Keene, and I’m a committed public interest attorney. Living in Denver, Colorado, I’ve spent years championing the rights of underrepresented workers. A graduate of Stanford University, I played a key role in securing critical protections for agricultural laborers, ensuring better wages and access to healthcare. My work has focused on advocating for environmental justice and improving the quality of life for rural communities. Every case I take on is driven by the belief that everyone deserves dignity and fair treatment in the workplace."
msgstr "我叫诺拉-温斯洛-基恩（Nora Winslow Keene），是一名坚定的公益律师。我居住在科罗拉多州丹佛市，多年来一直致力于维护代表性不足的工人的权利。我毕业于斯坦福大学，在为农业工人争取关键保护、确保提高工资和获得医疗保健方面发挥了关键作用。我的工作重点是倡导环境正义和改善农村社区的生活质量。我接手的每一个案件都是出于这样一个信念：每个人都应该在工作场所获得尊严和公平待遇。"

#: patterns/page-cv-bio.php:28
msgctxt "Example heading in pattern."
msgid "Hey,"
msgstr "嘿，"

#: patterns/page-cv-bio.php
msgctxt "Pattern description"
msgid "A pattern for a CV/Bio landing page."
msgstr "简历 / 生物着陆页面的样板。"

#: patterns/page-cv-bio.php
msgctxt "Pattern title"
msgid "CV/bio"
msgstr "简历 / 简介"

#: patterns/page-coming-soon.php:33
msgid "Subscribe to get notified when our website is ready."
msgstr "订阅，以便在我们的网站准备就绪时收到通知。"

#: patterns/page-coming-soon.php:29
msgid "Something great is coming soon"
msgstr "精彩即将呈现"

#: patterns/page-coming-soon.php:24
msgid "Event"
msgstr "事件"

#: patterns/page-coming-soon.php
msgctxt "Pattern description"
msgid "A full-width cover banner that can be applied to a page or it can work as a single landing page."
msgstr "一个完整的宽度封面横幅，可以申请成为一个页面，也可以作为一个单独的着陆页面。"

#: patterns/page-coming-soon.php
msgctxt "Pattern title"
msgid "Coming soon"
msgstr "即将推出"

#: patterns/page-business-home.php
msgctxt "Pattern description"
msgid "A business homepage pattern."
msgstr "企业主页样板。"

#: patterns/page-business-home.php
msgctxt "Pattern title"
msgid "Business homepage"
msgstr "企业主页"

#: patterns/overlapped-images.php
msgctxt "Pattern description"
msgid "A section with overlapping images, and a description."
msgstr "带有重叠图片和说明的部分。"

#: patterns/overlapped-images.php
msgctxt "Pattern title"
msgid "Overlapping images and paragraph on right"
msgstr "重叠的图片和右侧的段落"

#: patterns/more-posts.php:18
msgid "More posts"
msgstr "更多文章"

#: patterns/more-posts.php
msgctxt "Pattern description"
msgid "Displays a list of posts with title and date."
msgstr "显示带有标题和日期的标题列表。"

#: patterns/more-posts.php
msgctxt "Pattern title"
msgid "More posts"
msgstr "更多文章"

#: patterns/media-instagram-grid.php:56
msgid "Close up of two flowers on a dark background."
msgstr "深色背景上两朵花的特写。"

#: patterns/media-instagram-grid.php:48
msgid "Portrait of an African Woman dressed in traditional costume, wearing decorative jewelry."
msgstr "身着传统服装、佩戴装饰珠宝的非洲妇女肖像。"

#: patterns/media-instagram-grid.php:40
msgid "Profile portrait of a native person."
msgstr "一个当地人的侧面肖像。"

#: patterns/media-instagram-grid.php:28
msgctxt "Example username for social media account."
msgid "@example"
msgstr "@ 示例"

#: patterns/media-instagram-grid.php
msgctxt "Pattern description"
msgid "A grid section with photos and a link to an Instagram profile."
msgstr "带有照片的网格部分和 Instagram 个人资料的链接。"

#: patterns/media-instagram-grid.php
msgctxt "Pattern title"
msgid "Instagram grid"
msgstr "Instagram 网格"

#: patterns/logos.php:17
msgid "The Stories Podcast is sponsored by"
msgstr "故事播客由以下赞助商赞助"

#: patterns/logos.php
msgctxt "Pattern description"
msgid "Showcasing the podcast's clients with a heading and a series of client logos."
msgstr "用标题和一系列客户 Logos 展示播客的客户。"

#: patterns/logos.php
msgctxt "Pattern title"
msgid "Logos"
msgstr "Logos"

#: patterns/hidden-written-by.php:20
msgid "in"
msgstr "在"

#: patterns/hidden-written-by.php:16
msgid "Written by "
msgstr "作者："

#: patterns/hidden-written-by.php
msgctxt "Pattern title"
msgid "Written by"
msgstr "作者"

#: patterns/hidden-sidebar.php:38 patterns/page-portfolio-home.php:65
#: patterns/page-portfolio-home.php:87 patterns/page-portfolio-home.php:121
#: patterns/page-portfolio-home.php:154 patterns/page-portfolio-home.php:176
#: patterns/page-portfolio-home.php:203 patterns/template-home-news-blog.php:40
#: patterns/template-home-posts-grid-news-blog.php:35
#: patterns/template-home-posts-grid-news-blog.php:60
#: patterns/template-home-posts-grid-news-blog.php:78
#: patterns/template-home-posts-grid-news-blog.php:103
#: patterns/template-home-with-sidebar-news-blog.php:62
#: patterns/template-home-with-sidebar-news-blog.php:119
#: patterns/template-query-loop-news-blog.php:55
#: patterns/template-query-loop-photo-blog.php:22
#: patterns/template-query-loop-text-blog.php:19
#: patterns/template-query-loop-vertical-header-blog.php:47
#: patterns/template-query-loop.php:31
msgctxt "Message explaining that there are no results returned from a search."
msgid "Sorry, but nothing was found. Please try a search with different keywords."
msgstr "抱歉，未找到任何内容。请尝试使用不同的关键字进行搜索。"

#: patterns/hidden-sidebar.php:37
#: patterns/template-home-posts-grid-news-blog.php:34
#: patterns/template-home-with-sidebar-news-blog.php:61
msgid "Add text or blocks that will display when a query returns no results."
msgstr "添加查询无结果时显示的文本或区块。"

#: patterns/hidden-sidebar.php:14
msgid "Other Posts"
msgstr "其他文章"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "侧边栏"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Button text. Verb."
msgid "Search"
msgstr "搜索"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "搜索"

#: patterns/hidden-blog-heading.php
msgctxt "Pattern description"
msgid "Hidden heading for the home page and index template."
msgstr "隐藏首页标题和索引模板。"

#: patterns/hidden-blog-heading.php
msgctxt "Pattern title"
msgid "Hidden blog heading"
msgstr "隐藏的博客标题"

#: patterns/hidden-404.php:36
msgctxt "404 error message"
msgid "The page you are looking for doesn't exist, or it has been moved. Please try searching using the form below."
msgstr "您要查找的页面不存在，或已被移动。请尝试使用下面的表格进行搜索。"

#: patterns/hidden-404.php:32
msgctxt "404 error message"
msgid "Page not found"
msgstr "页面未找到"

#: patterns/hidden-404.php:21
msgctxt "image description"
msgid "Small totara tree on ridge above Long Point"
msgstr "长角山脊上的小图塔拉树"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/hero-podcast.php:65
msgctxt "Button text"
msgid "RSS"
msgstr "RSS"

#: patterns/hero-podcast.php:61
msgctxt "Button text"
msgid "Pocket Casts"
msgstr "袖珍播放器"

#: patterns/hero-podcast.php:57
msgctxt "Button text"
msgid "Spotify"
msgstr "Spotify"

#: patterns/hero-podcast.php:53
msgctxt "Button text"
msgid "Apple Podcasts"
msgstr "苹果播客"

#: patterns/hero-podcast.php:49
msgctxt "Button text"
msgid "YouTube"
msgstr "YouTube"

#: patterns/hero-podcast.php:43
msgid "Subscribe on your favorite platform"
msgstr "在您喜欢的平台上订阅"

#: patterns/hero-podcast.php:36
msgctxt "Podcast description"
msgid "Storytelling, expert analysis, and vivid descriptions. The Stories Podcast brings history to life, making it accessible and engaging for a global audience."
msgstr "故事讲述、专家分析和生动描述。故事播客将历史变得栩栩如生，让全球受众都能了解历史、参与历史。"

#: patterns/hero-podcast.php:32
msgid "The Stories Podcast"
msgstr "故事播客"

#: patterns/hero-podcast.php:22
msgctxt "Alt text for hero image."
msgid "Picture of a person"
msgstr "人物图片"

#: patterns/hero-podcast.php
msgctxt "Pattern title"
msgid "Hero podcast"
msgstr "主人公播客"

#: patterns/hero-overlapped-book-cover-with-links.php:113
msgid "Book Image"
msgstr "图书图片"

#: patterns/hero-overlapped-book-cover-with-links.php:34
msgctxt "Hero - Overlapped book cover pattern subline text"
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "这本精美的时光集收录了 Louis Fleckenstein 、 Paul Strand 和 Asahachi Kōno 的摄影作品。"

#: patterns/hero-overlapped-book-cover-with-links.php:28
msgctxt "Hero - Overlapped book cover pattern headline text"
msgid "The Stories Book"
msgstr "《故事集》"

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern description"
msgid "A hero with an overlapped book cover and links."
msgstr "一个拥有重叠书皮和链接的主人公。"

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern title"
msgid "Hero, overlapped book cover with links"
msgstr "主人公，带链接的重叠书皮"

#: patterns/hero-full-width-image.php:33
msgctxt "Sample hero button"
msgid "Learn More"
msgstr "了解更多"

#: patterns/hero-full-width-image.php:27
msgctxt "Sample hero paragraph"
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "就像花朵在意想不到的地方绽放，每个故事都以美丽和坚韧的姿态展开，展现出不为人知的奇迹。"

#: patterns/hero-full-width-image.php:23
msgctxt "Sample hero heading"
msgid "Tell your story"
msgstr "讲述您的故事"

#: patterns/hero-full-width-image.php:18
msgctxt "Alt text for cover image."
msgid "Picture of a flower"
msgstr "花朵图片"

#: patterns/hero-full-width-image.php
msgctxt "Pattern description"
msgid "A hero with a full width image, heading, short paragraph and button."
msgstr "一个带有全宽图片、标题、简短段落和按钮的主人公。"

#: patterns/hero-full-width-image.php
msgctxt "Pattern title"
msgid "Hero, full width image"
msgstr "Hero, full 宽度图片"

#: patterns/hero-book.php:46
msgctxt "CTA text of the hero section."
msgid "Available for pre-order now."
msgstr "现已接受预订。"

#: patterns/hero-book.php:42
msgctxt "Content of the hero section."
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "路易斯-弗莱肯施泰因、保罗-斯特兰德和浅八光之的摄影作品，展现了时间的精彩瞬间。"

#: patterns/hero-book.php:38
msgctxt "Heading of the hero section."
msgid "The Stories Book"
msgstr "《故事集》"

#: patterns/hero-book.php:24
msgid "Image of the book"
msgstr "本书图片"

#: patterns/hero-book.php
msgctxt "Pattern description"
msgid "A hero section for the book with a description and pre-order link."
msgstr "本书的主人公部分，附有说明和预购链接。"

#: patterns/hero-book.php
msgctxt "Pattern title"
msgid "Hero book"
msgstr "主人公之书"

#: patterns/heading-and-paragraph-with-image.php:36
msgctxt "Alt text for Overview picture."
msgid "Cliff Palace, Colorado"
msgstr "科罗拉多州悬崖宫殿"

#: patterns/heading-and-paragraph-with-image.php:27
msgctxt "Event Overview Text."
msgid "Held over a weekend, the event is structured around a series of exhibitions, workshops, and panel discussions. The exhibitions showcase a curated selection of photographs that tell compelling stories from various corners of the globe, each image accompanied by detailed narratives that provide context and deeper insight into the historical significance of the scenes depicted. These photographs are drawn from the archives of renowned photographers, as well as emerging talents, ensuring a blend of both classical and contemporary perspectives."
msgstr "活动在周末举行，围绕一系列展览、研讨会和小组讨论展开。展览展示了经过精心策划的精选摄影作品，这些作品讲述了来自世界各个角落的引人入胜的故事，每张图片都配有详细的解说，提供了背景资料，让人们更深入地了解所描绘场景的历史意义。这些照片来自知名摄影师和新秀的档案，确保了古典和现代视角的融合。"

#: patterns/heading-and-paragraph-with-image.php:23
msgid "About the event"
msgstr "关于活动"

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern description"
msgid "A two-column section with a heading and paragraph on the left, and an image on the right."
msgstr "标题和段落位于左侧，图片位于右侧。"

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern title"
msgid "Heading and paragraph with image on the right"
msgstr "标题和段落右侧有图片"

#: patterns/header.php
msgctxt "Pattern description"
msgid "Header with site title and navigation."
msgstr "标题带网站标题和导航。"

#: patterns/header.php
msgctxt "Pattern title"
msgid "Header"
msgstr "标题"

#: patterns/header-large-title.php
msgctxt "Pattern description"
msgid "Header with large site title and right-aligned navigation."
msgstr "带有大标题的标题和右对齐导航。"

#: patterns/header-large-title.php
msgctxt "Pattern title"
msgid "Header with large title"
msgstr "带大标题的页眉"

#: patterns/header-columns.php
msgctxt "Pattern description"
msgid "Header with site title and navigation in columns."
msgstr "带网站标题和分栏导航的页眉。"

#: patterns/header-columns.php
msgctxt "Pattern title"
msgid "Header with columns"
msgstr "带列的页眉"

#: patterns/header-centered.php
msgctxt "Pattern description"
msgid "Header with centered site title and navigation."
msgstr "标题居中的标题和导航。"

#: patterns/header-centered.php
msgctxt "Pattern title"
msgid "Centered header"
msgstr "居中标题"

#: patterns/grid-with-categories.php:64
msgid "Sunflowers"
msgstr "向日葵"

#: patterns/grid-with-categories.php:50
msgid "Cactus"
msgstr "仙人掌"

#: patterns/grid-with-categories.php:36
msgid "Anthuriums"
msgstr "红掌"

#: patterns/grid-with-categories.php:29
msgid "Close up of a red anthurium."
msgstr "红掌特写"

#: patterns/grid-with-categories.php:22
msgid "Top Categories"
msgstr "顶级分类"

#: patterns/grid-with-categories.php
msgctxt "Pattern description"
msgid "A grid section with different categories."
msgstr "带有不同分类的网格部分。"

#: patterns/grid-with-categories.php
msgctxt "Pattern title"
msgid "Grid with categories"
msgstr "带有分类的网格"

#: patterns/grid-videos.php:23
msgid "Podcast"
msgstr "播客"

#: patterns/grid-videos.php:19
msgid "Explore the episodes"
msgstr "探索剧集"

#: patterns/grid-videos.php
msgctxt "Pattern description"
msgid "A grid with videos."
msgstr "带视频的网格"

#: patterns/grid-videos.php
msgctxt "Pattern title"
msgid "Grid with videos"
msgstr "带视频的网格"

#: patterns/format-link.php:23
msgid "https://example.com"
msgstr "https://example.com"

#: patterns/format-link.php:17
msgid "The Stories Book, a fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno, is available for pre-order"
msgstr "由路易斯-弗莱肯斯坦（Louis Fleckenstein）、保罗-斯特兰德（Paul Strand）和旭八郎（Asahachi Kōno）拍摄的《故事集》（The Stories Book）是一本时间瞬间的精美合集，现已接受预订。"

#: patterns/format-link.php
msgctxt "Pattern description"
msgid "A link post format with a description and an emphasized link for key content."
msgstr "链接文章格式，包含关键内容的描述和强调的链接。"

#: patterns/format-link.php
msgctxt "Pattern title"
msgid "Link format"
msgstr "链接格式"

#: patterns/format-audio.php:30
msgid "Acoma Pueblo, in New Mexico, stands as a testament to the resilience and cultural heritage of the Acoma people"
msgstr "新墨西哥州的阿科马普韦布洛见证了阿科马人坚韧不拔的精神和文化遗产。"

#: patterns/format-audio.php:26
msgid "Episode 1: Acoma Pueblo with Prof. Fiona Presley"
msgstr "第 1 集：阿科马普韦布洛与菲奥娜-普雷斯利教授的对话"

#: patterns/format-audio.php
msgctxt "Pattern description"
msgid "An audio post format with an image, title, audio player, and description."
msgstr "音频文章格式，附带图片、标题、音频播放器和说明。"

#: patterns/format-audio.php
msgctxt "Pattern title"
msgid "Audio format"
msgstr "音频格式"

#: patterns/footer.php
msgctxt "Pattern description"
msgid "Footer columns with logo, title, tagline and links."
msgstr "带有徽标、标题、网站标语和链接的页脚栏。"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer"
msgstr "页脚"

#: patterns/footer-social.php
msgctxt "Pattern description"
msgid "Footer with centered site title and social links."
msgstr "带居中网站标题和社交链接的页脚。"

#: patterns/footer-social.php
msgctxt "Pattern title"
msgid "Centered footer with social links"
msgstr "以页脚为中心的社交链接"

#: patterns/footer-newsletter.php:24
msgid "Receive our articles in your inbox."
msgstr "在您的收件箱中接收我们的文章。"

#: patterns/footer-newsletter.php
msgctxt "Pattern description"
msgid "Footer with large site title and newsletter signup."
msgstr "带有大型网站标题和时事通讯注册的页脚。"

#: patterns/footer-newsletter.php
msgctxt "Pattern title"
msgid "Footer with newsletter signup"
msgstr "带电子报注册的页脚"

#: patterns/footer-columns.php:52 patterns/footer.php:61
msgid "Themes"
msgstr "主题"

#: patterns/footer-columns.php:51 patterns/footer.php:59
msgid "Patterns"
msgstr "样板"

#: patterns/footer-columns.php:50 patterns/footer.php:57
msgid "Shop"
msgstr "商店"

#: patterns/footer-columns.php:48
msgid "Featured"
msgstr "精选"

#: patterns/footer-columns.php:39 patterns/footer.php:51
msgid "Authors"
msgstr "作者"

#: patterns/footer-columns.php:38 patterns/footer.php:49
msgid "FAQs"
msgstr "常见问题"

#: patterns/footer-columns.php:37 patterns/footer.php:47
msgid "About"
msgstr "关于"

#: patterns/footer-columns.php:36 patterns/footer.php:45
#: patterns/hidden-blog-heading.php:15 patterns/template-home-text-blog.php:20
msgid "Blog"
msgstr "博客"

#: patterns/footer-columns.php
msgctxt "Pattern description"
msgid "Footer columns with title, tagline and links."
msgstr "带有标题，网站标语和链接的页脚列。"

#: patterns/footer-columns.php
msgctxt "Pattern title"
msgid "Footer with columns"
msgstr "带列的页脚"

#. translators: Designed with WordPress. %s: WordPress link.
#: patterns/footer-centered.php:33 patterns/footer-columns.php:73
#: patterns/footer-newsletter.php:49 patterns/footer-social.php:35
#: patterns/footer.php:82
msgid "Designed with %s"
msgstr "以 %s 设计"

#: patterns/footer-centered.php
msgctxt "Pattern description"
msgid "Footer with centered site title and tagline."
msgstr "带有居中网站标题和网站标语的页脚。"

#: patterns/footer-centered.php
msgctxt "Pattern title"
msgid "Centered footer"
msgstr "中心页脚"

#: patterns/event-schedule.php:174
msgid "An introduction to African dialects"
msgstr "非洲方言简介"

#: patterns/event-schedule.php:163
msgid "Black and white photo of an African woman."
msgstr "非洲妇女的黑白照片。"

#: patterns/event-schedule.php:142
msgid "Ancient buildings and symbols"
msgstr "古代建筑和标志"

#: patterns/event-schedule.php:132 patterns/media-instagram-grid.php:52
msgid "The Acropolis of Athens."
msgstr "雅典卫城"

#: patterns/event-schedule.php:89
msgid "Things you didn’t know about the deep ocean"
msgstr "你不知道的深海趣事"

#: patterns/event-schedule.php:78 patterns/media-instagram-grid.php:44
msgid "View of the deep ocean."
msgstr "深海景观。"

#: patterns/event-schedule.php:65 patterns/event-schedule.php:97
#: patterns/event-schedule.php:150 patterns/event-schedule.php:182
msgctxt "Pattern placeholder text with link."
msgid "Lecture by <a href=\"#\">Prof. Fiona Presley</a>"
msgstr "<a href=\"#\">菲奥娜-普雷斯利（Fiona Presley）</a>教授的讲座"

#: patterns/event-schedule.php:60 patterns/event-schedule.php:92
#: patterns/event-schedule.php:145 patterns/event-schedule.php:177
msgctxt "Example event time in pattern."
msgid "9 AM — 11 AM"
msgstr "上午 9 时至 11 时"

#: patterns/event-schedule.php:57
msgid "Fauna from North America and its characteristics"
msgstr "北美洲的动物及其特征"

#: patterns/event-schedule.php:46 patterns/media-instagram-grid.php:60
msgid "Birds on a lake."
msgstr "湖上的鸟类"

#: patterns/event-schedule.php:20
msgid "Agenda"
msgstr "日程安排"

#: patterns/event-schedule.php
msgctxt "Pattern description"
msgid "A section with specified dates and times for an event."
msgstr "为事件指定日期和时间的部分。"

#: patterns/event-schedule.php
msgctxt "Pattern title"
msgid "Event schedule"
msgstr "活动日程"

#: patterns/event-rsvp.php:91
msgid "Close up photo of white flowers on a grey background"
msgstr "灰色背景上白色花朵的照片特写"

#: patterns/event-rsvp.php:81
msgctxt "Abbreviation for \"Please respond\"."
msgid "RSVP"
msgstr "回复"

#: patterns/event-rsvp.php:73
msgid "This immersive event celebrates the universal human experience through the lenses of history and ancestry, featuring a diverse array of photographers whose works capture the essence of different cultures and historical moments."
msgstr "这个身临其境的事件通过历史记录和祖先的镜头来颂扬人类的经验，展示不同摄影师的作品，他们的作品捕捉了不同文化和历史时刻的精髓。"

#: patterns/event-rsvp.php:57
msgid "Free Workshop"
msgstr "免费工作坊"

#: patterns/event-rsvp.php
msgctxt "Pattern description"
msgid "RSVP for an upcoming event with a cover image and event details."
msgstr "为即将举行的事件 RSVP，请提供封面图片和事件详情。"

#: patterns/event-rsvp.php
msgctxt "Pattern title"
msgid "Event RSVP"
msgstr "事件 RSVP"

#: patterns/event-3-col.php:50 patterns/event-3-col.php:74
#: patterns/event-3-col.php:98
msgid "Event details"
msgstr "事件详情"

#: patterns/event-3-col.php:34 patterns/event-3-col.php:58
#: patterns/event-3-col.php:82 patterns/format-audio.php:20
msgid "Event image"
msgstr "事件图片"

#: patterns/event-3-col.php:24 patterns/event-schedule.php:23
msgid "These are some of the upcoming events."
msgstr "这些是一些即将发生的事件。"

#: patterns/event-3-col.php:20 patterns/footer-columns.php:49
#: patterns/footer.php:55
msgid "Events"
msgstr "事件"

#: patterns/event-3-col.php
msgctxt "Pattern description"
msgid "A header with title and text and three columns that show 3 events with their images and titles."
msgstr "标题和文字，三栏显示 3 个事件及其图片和标题。"

#: patterns/event-3-col.php
msgctxt "Pattern title"
msgid "Events, 3 columns with event images and titles"
msgstr "事件，3 栏，含事件图片和标题"

#: patterns/cta-newsletter.php:32 patterns/footer-newsletter.php:30
#: patterns/page-coming-soon.php:39
#: patterns/services-subscriber-only-section.php:51
msgid "Subscribe"
msgstr "订阅"

#: patterns/cta-newsletter.php:23
msgid "Get access to a curated collection of moments in time featuring photographs from historical relevance."
msgstr "访问由历史相关照片组成的「时间瞬间」精选集。"

#: patterns/cta-newsletter.php:19
msgid "Sign up to get daily stories"
msgstr "注册获取每日故事"

#: patterns/cta-newsletter.php
msgctxt "Pattern title"
msgid "Newsletter sign-up"
msgstr "订阅电子报"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search input field placeholder text."
msgid "Type here..."
msgstr "在此输入..."

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search form label."
msgid "Search"
msgstr "搜索"

#: patterns/cta-heading-search.php:18
msgid "What are you looking for?"
msgstr "您在寻找什么？"

#: patterns/cta-heading-search.php
msgctxt "Pattern description"
msgid "Large heading with a search form for quick navigation."
msgstr "大标题带搜索表单，用于快速导航。"

#: patterns/cta-heading-search.php
msgctxt "Pattern title"
msgid "Heading and search form"
msgstr "标题和搜索表单"

#: patterns/cta-grid-products-link.php:134
msgid "Shop now"
msgstr "现在购物"

#: patterns/cta-grid-products-link.php:114
msgid "Botany flowers"
msgstr "植物花卉"

#: patterns/cta-grid-products-link.php:100
msgid "Cancel anytime"
msgstr "随时取消"

#: patterns/cta-grid-products-link.php:84
msgid "Free shipping"
msgstr "免费送货"

#: patterns/cta-grid-products-link.php:76
msgid "Tailored to your needs"
msgstr "根据您的需求量身定制"

#: patterns/cta-grid-products-link.php:70
msgid "Flora of Akaka Falls State Park"
msgstr "阿卡卡瀑布州立公园的植物"

#: patterns/cta-grid-products-link.php:59
msgid "30€"
msgstr "30€"

#. translators: %s: Starting price, split into three rows using HTML <br> tags.
#. The price value has a font size set.
#: patterns/cta-grid-products-link.php:58
msgid "Starting at%s/month"
msgstr "起价 %s/月"

#: patterns/cta-grid-products-link.php:38
msgid "Closeup of plantlife in the Malibu Canyon area"
msgstr "马里布峡谷地区的植物特写"

#: patterns/cta-grid-products-link.php:32
msgid "Delivered every week"
msgstr "每周发送"

#: patterns/cta-grid-products-link.php:26
#: patterns/cta-grid-products-link.php:126
msgid "Black and white flower"
msgstr "黑白花"

#: patterns/cta-grid-products-link.php:20
msgid "Our online store."
msgstr "我们的网上商店。"

#: patterns/cta-grid-products-link.php
msgctxt "Pattern description"
msgid "A call to action featuring product images."
msgstr "以产品为特色的行动号召图片."

#: patterns/cta-grid-products-link.php
msgctxt "Pattern title"
msgid "Call to action with grid layout with products and link"
msgstr "以产品和链接为特色的网格布局行动号召"

#: patterns/cta-events-list.php:106 patterns/cta-events-list.php:144
msgid "Thornville, OH, USA"
msgstr "美国俄亥俄州索恩维尔"

#: patterns/cta-events-list.php:75
msgid "Mexico City, Mexico"
msgstr "墨西哥墨西哥城"

#: patterns/cta-events-list.php:51 patterns/cta-events-list.php:89
#: patterns/cta-events-list.php:120 patterns/cta-events-list.php:158
msgid "Buy Tickets"
msgstr "购买门票"

#: patterns/cta-events-list.php:45 patterns/cta-events-list.php:83
#: patterns/cta-events-list.php:114 patterns/cta-events-list.php:152
#: patterns/event-3-col.php:44 patterns/event-3-col.php:68
#: patterns/event-3-col.php:92 patterns/event-rsvp.php:37
#: patterns/event-schedule.php:35 patterns/event-schedule.php:121
msgctxt "Example event date in pattern."
msgid "Mon, Jan 1"
msgstr "1月1日，周一"

#: patterns/cta-events-list.php:37
msgid "Atlanta, GA, USA"
msgstr "美国佐治亚州亚特兰大"

#: patterns/cta-events-list.php:23
msgid "These are some of the upcoming events"
msgstr "这些是即将举行的部分事件"

#: patterns/cta-events-list.php:19
msgid "Upcoming events"
msgstr "即将举行的活动"

#: patterns/cta-events-list.php
msgctxt "Pattern description"
msgid "A list of events with call to action."
msgstr "带行动号召的事件列表"

#: patterns/cta-events-list.php
msgctxt "Pattern title"
msgid "Events list"
msgstr "活动列表"

#: patterns/cta-centered-heading.php:28
msgid "Learn more"
msgstr "了解更多"

#: patterns/cta-centered-heading.php:22
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "就像花朵在意想不到的地方绽放，每一个故事都以美丽和坚韧的姿态展开，揭示隐藏的奇迹。"

#: patterns/cta-centered-heading.php:19 patterns/cta-events-list.php:33
#: patterns/cta-events-list.php:102 patterns/event-3-col.php:40
#: patterns/event-3-col.php:64 patterns/event-3-col.php:88
#: patterns/template-home-photo-blog.php:27
msgid "Tell your story"
msgstr "讲述你的故事"

#: patterns/cta-centered-heading.php
msgctxt "Pattern description"
msgid "A hero with a centered heading, paragraph and button."
msgstr "一个有居中标题、段落和按钮的主人公。"

#: patterns/cta-centered-heading.php
msgctxt "Pattern title"
msgid "Centered heading"
msgstr "居中的标题"

#: patterns/cta-book-locations.php:131
msgid "United Kingdom"
msgstr "英国"

#: patterns/cta-book-locations.php:119
msgid "United States"
msgstr "美国"

#: patterns/cta-book-locations.php:107
msgid "Switzerland"
msgstr "新西兰"

#: patterns/cta-book-locations.php:95
msgid "New Zealand"
msgstr "新西兰"

#: patterns/cta-book-locations.php:79
msgid "Japan"
msgstr "日本"

#: patterns/cta-book-locations.php:67
msgid "Canada"
msgstr "加拿大"

#: patterns/cta-book-locations.php:55
msgid "Brazil"
msgstr "巴西"

#: patterns/cta-book-locations.php:47 patterns/cta-book-locations.php:59
#: patterns/cta-book-locations.php:71 patterns/cta-book-locations.php:83
#: patterns/cta-book-locations.php:99 patterns/cta-book-locations.php:111
#: patterns/cta-book-locations.php:123 patterns/cta-book-locations.php:135
msgid "Book Store"
msgstr "书店"

#: patterns/cta-book-locations.php:43
msgid "Australia"
msgstr "澳大利亚"

#: patterns/cta-book-locations.php:27
msgid "The Stories Book will be available from these international retailers."
msgstr "《故事集》将在这些国际零售商处发售。"

#: patterns/cta-book-locations.php:23
msgid "International editions"
msgstr "国际版本"

#: patterns/cta-book-locations.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in the most popular locations."
msgstr "附有「链接」的「行动号召」部分，可在最受欢迎的地点获取此书。"

#: patterns/cta-book-locations.php
msgctxt "Pattern title"
msgid "Call to action with locations"
msgstr "带地点的行动号召"

#: patterns/cta-book-links.php:57
#: patterns/hero-overlapped-book-cover-with-links.php:100
msgctxt "Pattern placeholder text with link."
msgid "Outside Europe? View <a href=\"#\" rel=\"nofollow\">international editions</a>."
msgstr "欧洲以外？查看<a href=\"#\" rel=\"nofollow\">国际版本</a>。"

#: patterns/cta-book-links.php:51
msgctxt "Example brand name."
msgid "Simon &amp; Schuster"
msgstr "西蒙与舒斯特"

#: patterns/cta-book-links.php:47
msgctxt "Example brand name."
msgid "BAM!"
msgstr "BAM！"

#: patterns/cta-book-links.php:43
msgctxt "Example brand name."
msgid "Spotify"
msgstr "Spotify"

#: patterns/cta-book-links.php:39
msgctxt "Example brand name."
msgid "Bookshop.org"
msgstr "Bookshop.org"

#: patterns/cta-book-links.php:35
#: patterns/hero-overlapped-book-cover-with-links.php:62
msgctxt "Example brand name."
msgid "Apple Books"
msgstr "苹果图书"

#: patterns/cta-book-links.php:31
#: patterns/hero-overlapped-book-cover-with-links.php:84
msgctxt "Example brand name."
msgid "Barnes &amp; Noble"
msgstr "巴诺书店"

#: patterns/cta-book-links.php:27
#: patterns/hero-overlapped-book-cover-with-links.php:77
msgctxt "Example brand name."
msgid "Audible"
msgstr "有声读物"

#: patterns/cta-book-links.php:23
#: patterns/hero-overlapped-book-cover-with-links.php:55
msgctxt "Example brand name."
msgid "Amazon"
msgstr "亚马逊"

#: patterns/cta-book-links.php:17
msgid "Buy your copy of The Stories Book"
msgstr "购买《故事集》"

#: patterns/cta-book-links.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in different websites."
msgstr "附有行动呼吁的链接部分，可在不同网站获取此书。"

#: patterns/cta-book-links.php
msgctxt "Pattern title"
msgid "Call to action with book links"
msgstr "与书链接的行动号召"

#: patterns/contact-location-and-link.php:36
msgid "The business location"
msgstr "业务地点"

#: patterns/contact-location-and-link.php:26
msgid "Get directions"
msgstr "获取路线"

#: patterns/contact-location-and-link.php:22
msgid "Visit us at 123 Example St. Manhattan, NY 10300, United States"
msgstr "地址：美国纽约州曼哈顿示例街 123 号 10300"

#: patterns/contact-location-and-link.php
msgctxt "Pattern description"
msgid "Contact section with a location address, a directions link, and an image of the location."
msgstr "联系部分包括地址、方向链接和位置图片。"

#: patterns/contact-location-and-link.php
msgctxt "Pattern title"
msgid "Contact location and link"
msgstr "联系地点和链接"

#: patterns/contact-info-locations.php:86
msgid "Portland"
msgstr "波特兰"

#: patterns/contact-info-locations.php:74
msgid "Salt Lake City"
msgstr "盐湖城"

#: patterns/contact-info-locations.php:62
msgid "San Diego"
msgstr "圣地亚哥"

#: patterns/contact-info-locations.php:54
#: patterns/contact-info-locations.php:66
#: patterns/contact-info-locations.php:78
#: patterns/contact-info-locations.php:90
msgid "123 Example St. Manhattan, NY 10300 United States"
msgstr "美国纽约州曼哈顿示例街 123 号 10300"

#: patterns/contact-info-locations.php:51
msgid "New York"
msgstr "纽约"

#: patterns/contact-info-locations.php:41
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/contact-info-locations.php:38
msgid "Email"
msgstr "邮箱"

#: patterns/contact-info-locations.php:35
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:44
#: patterns/page-link-in-bio-with-tight-margins.php:56
msgid "TikTok"
msgstr "TikTok"

#: patterns/contact-info-locations.php:34 patterns/footer-social.php:21
msgid "Facebook"
msgstr "Facebook"

#: patterns/contact-info-locations.php:33 patterns/footer-social.php:22
#: patterns/media-instagram-grid.php:24 patterns/page-cv-bio.php:47
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:36
#: patterns/page-link-in-bio-with-tight-margins.php:48
msgid "Instagram"
msgstr "Instagram"

#: patterns/contact-info-locations.php:32 patterns/footer-social.php:23
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:40
#: patterns/page-link-in-bio-with-tight-margins.php:52
msgctxt "Refers to the social media platform formerly known as Twitter."
msgid "X"
msgstr "X"

#: patterns/contact-info-locations.php:29
#: patterns/contact-info-locations.php:31 patterns/footer-social.php:20
msgid "Social media"
msgstr "社交媒体"

#: patterns/contact-info-locations.php:21
msgid "How to get in touch with us"
msgstr "如何与我们联系"

#: patterns/contact-info-locations.php
msgctxt "Pattern description"
msgid "Contact section with social media links, email, and multiple location details."
msgstr "联系方式部分包含社交媒体链接, 邮箱和多个地点的详细信息。"

#: patterns/contact-info-locations.php
msgctxt "Pattern title"
msgid "Contact, info and locations"
msgstr "联系方式、信息和地点"

#: patterns/contact-centered-social-link.php:21
msgctxt "Heading of the Contact social link pattern"
msgid "Got questions? <br><a href=\"#\" rel=\"nofollow\">Feel free to reach out.</a>"
msgstr "有问题吗？<br><a href=\"#\" rel=\"nofollow\">请随时联系我们。</a>"

#: patterns/contact-centered-social-link.php
msgctxt "Pattern description"
msgid "Centered contact section with a prominent message and social media links."
msgstr "居中的联系部分，带有醒目的信息和社交媒体链接。"

#: patterns/contact-centered-social-link.php
msgctxt "Pattern title"
msgid "Centered link and social links"
msgstr "中心链接和社会链接"

#: patterns/comments.php:18
msgid "Comments"
msgstr "评论"

#: patterns/comments.php
msgctxt "Pattern description"
msgid "Comments area with comments list, pagination, and comment form."
msgstr "带有评论列表、分页和评论表单的评论区。"

#: patterns/comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "评论"

#: patterns/binding-format.php
msgctxt "Pattern description"
msgid "Prints the name of the post format with the help of the Block Bindings API."
msgstr "借助区块绑定 API 打印文章格式名称。"

#: patterns/binding-format.php
msgctxt "Pattern title"
msgid "Post format name"
msgstr "文章格式名称"

#: patterns/banner-with-description-and-images-grid.php:48
#: patterns/overlapped-images.php:26
msgid "Black and white photography close up of a flower."
msgstr "一朵花的黑白摄影特写。"

#: patterns/banner-with-description-and-images-grid.php:42
#: patterns/overlapped-images.php:21
msgid "Photography close up of a red flower."
msgstr "红色花朵的摄影特写。"

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-with-description-and-images-grid.php:31
#: patterns/overlapped-images.php:47
msgid "%s is a flower delivery and subscription business. Based in the EU, our mission is not only to deliver stunning flower arrangements across but also foster knowledge and enthusiasm on the beautiful gift of nature: flowers."
msgstr "%s 是一家鲜花配送和订购公司。我们的总部位于欧盟，我们的使命不仅是向全世界提供精美的鲜花，还致力于培养人们对大自然馈赠的美好礼物——鲜花的了解和热情。"

#: patterns/banner-with-description-and-images-grid.php:23
#: patterns/overlapped-images.php:37
msgid "About Us"
msgstr "关于我们"

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern description"
msgid "A banner with a short paragraph, and two images displayed in a grid layout."
msgstr "一个带有简短段落的横幅和两个图片显示在一个网格布局中。"

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern title"
msgid "Banner with description and images grid"
msgstr "带有说明和图片网格的横幅"

#: patterns/banner-poster.php:59
msgid "#stories"
msgstr "# 故事"

#: patterns/banner-poster.php:51
msgid "Let’s hear them."
msgstr "让我们听听他们的故事。"

#: patterns/banner-poster.php:39
msgid "Fuego Bar, Mexico City"
msgstr "墨西哥城 Fuego 酒吧"

#: patterns/banner-poster.php:39
msgctxt "Example event date in pattern."
msgid "Aug 08—10 2025"
msgstr "2025年8月8日—10日"

#. translators: This string contains the word "Stories" in four different
#. languages with the first item in the locale's language.
#: patterns/banner-poster.php:28 patterns/cta-events-list.php:68
#: patterns/cta-events-list.php:137 patterns/event-rsvp.php:30
msgctxt "Placeholder heading in four languages."
msgid "“Stories, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"
msgstr "「故事、<span lang=\"es\">历史</span>、<span lang=\"uk\">ictorії</span>、<span lang=\"el\">iστορίες</span>」"

#: patterns/banner-poster.php:15
msgid "Picture of a historical building in ruins."
msgstr "废墟中的历史建筑图片。"

#: patterns/banner-poster.php
msgctxt "Pattern description"
msgid "A section that can be used as a banner or a landing page to announce an event."
msgstr "可用作横幅或着陆页面以宣布事件的部分。"

#: patterns/banner-poster.php
msgctxt "Pattern title"
msgid "Poster-like section"
msgstr "类似海报的部分"

#: patterns/banner-intro.php:22
#: patterns/banner-with-description-and-images-grid.php:32
#: patterns/footer-columns.php:46 patterns/overlapped-images.php:48
msgctxt "Example brand name."
msgid "Fleurs"
msgstr "花"

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-intro.php:21
msgctxt "Pattern placeholder text."
msgid "We're %s, our mission is to deliver exquisite flower arrangements that not only adorn living spaces but also inspire a deeper appreciation for natural beauty."
msgstr "我们是 %s，我们的使命是提供精致的插花作品，不仅点缀生活空间，而且激发人们对自然美更深刻的欣赏。"

#: patterns/banner-intro.php
msgctxt "Pattern description"
msgid "A large left-aligned heading with a brand name emphasized in bold."
msgstr "左对齐的大标题，用粗体字强调品牌名称。"

#: patterns/banner-intro.php
msgctxt "Pattern title"
msgid "Intro with left-aligned description"
msgstr "左对齐的介绍说明"

#: patterns/banner-intro-image.php:42
msgctxt "Button text of intro section."
msgid "Learn More"
msgstr "了解更多"

#: patterns/banner-intro-image.php:35
msgctxt "Sample description for banner with flower."
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "就像花朵在意想不到的地方绽放一样，每个故事都以美丽和坚韧的姿态展开，揭示隐藏的奇迹。"

#: patterns/banner-intro-image.php:31
msgctxt "Heading for banner pattern."
msgid "New arrivals"
msgstr "新到产品"

#: patterns/banner-intro-image.php:22
msgctxt "Alt text for intro picture."
msgid "Picture of a flower"
msgstr "花卉图片"

#: patterns/banner-intro-image.php
msgctxt "Pattern description"
msgid "A Intro pattern with Short heading, paragraph and image on the left."
msgstr "带短标题、段落和图片的介绍样板位于左侧。"

#: patterns/banner-intro-image.php
msgctxt "Pattern title"
msgid "Short heading and paragraph and image on the left"
msgstr "左侧为短标题、段落和图片"

#: patterns/banner-cover-big-heading.php:27 patterns/footer-columns.php:33
#: patterns/footer-columns.php:35 patterns/footer-newsletter.php:20
#: patterns/template-home-photo-blog.php:22
msgid "Stories"
msgstr "故事"

#: patterns/banner-cover-big-heading.php:20
#: patterns/media-instagram-grid.php:36 patterns/page-coming-soon.php:19
msgid "Photo of a field full of flowers, a blue sky and a tree."
msgstr "一张开满鲜花、蓝天和大树的字段照片。"

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern description"
msgid "A full-width cover section with a large background image and an oversized heading."
msgstr "全幅封面，大背景图片和超大标题。"

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern title"
msgid "Cover with big heading"
msgstr "带有大标题的封面"

#: patterns/banner-about-book.php:34
msgid "Image of a book"
msgstr "一本书的图片"

#: patterns/banner-about-book.php:26
msgctxt "Pattern placeholder text."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist. Fleckenstein’s evocative imagery, Strand’s groundbreaking modernist approach, and Kōno’s meticulous documentation of Japanese life come together in a harmonious blend that celebrates the art of photography. Each image in “The Stories Book” is accompanied by insightful commentary, providing historical context and revealing the stories behind the photographs. This collection is not only a visual feast but also a tribute to the power of photography to preserve and narrate the multifaceted experiences of humanity."
msgstr "这本精美的合集展示了一系列不同的照片，捕捉了不同时代和文化的精髓，反映了每位艺术家的唯一样式和视角。弗莱肯斯坦令人回味的图像、斯特兰德开创性的现代主义手法，以及弘野对日本生活细致入微的记录，三者和谐地融合在一起，弘扬了摄影艺术。《故事集》中的每张图片都配有深刻的评论，提供历史背景并揭示照片背后的故事。这套书不仅是一场视觉盛宴，也是对摄影保存和叙述人类多方面经历的力量的致敬。"

#: patterns/banner-about-book.php:22
msgid "About the book"
msgstr "关于本书"

#: patterns/banner-about-book.php
msgctxt "Pattern description"
msgid "Banner with book description and accompanying image for promotion."
msgstr "带有图书介绍和附图的横幅，用于宣传。"

#: patterns/banner-about-book.php
msgctxt "Pattern title"
msgid "Banner with book description"
msgstr "横幅与图书介绍"

#: functions.php:134
msgctxt "Label for the block binding placeholder in the editor"
msgid "Post format name"
msgstr "文章格式名称"

#: functions.php:114
msgid "A collection of post format patterns."
msgstr "文章格式样板集。"

#: functions.php:113
msgid "Post formats"
msgstr "文章格式"

#: functions.php:106
msgid "A collection of full page layouts."
msgstr "全部页面布局集合"

#: functions.php:105
msgid "Pages"
msgstr "页面"

#: functions.php:76
msgid "Checkmark"
msgstr "复选标记"

#. Author URI of the theme
#: style.css patterns/footer-centered.php:34 patterns/footer-columns.php:74
#: patterns/footer-newsletter.php:50 patterns/footer-social.php:36
#: patterns/footer.php:83
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://cn.wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress 团队"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfive/"
msgstr "https://cn.wordpress.org/themes/twentytwentyfive/"
