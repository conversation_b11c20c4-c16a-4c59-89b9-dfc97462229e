# Translation of Themes - Blocksy in Chinese (China)
# This file is distributed under the same license as the Themes - Blocksy package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-04-16 04:15:42+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Blocksy\n"

#: inc/options/woocommerce/related-upsells.php:130
msgid "Module Title Color"
msgstr "模块标题颜色"

#: inc/panel-builder/header/search/options.php:498
msgid "Search through taxonomies from selected custom post types."
msgstr "从选定的自定义文章类型中搜索分类法。"

#: inc/panel-builder/header/search/options.php:494
msgid "Search Through Taxonomies"
msgstr "通过分类法搜索"

#: inc/options/woocommerce/single-product-gallery.php:346
msgid "Spacing"
msgstr "间距"

#: inc/options/woocommerce/single-product-gallery.php:300
msgid "Gallery Thumbnails"
msgstr "画廊缩略图"

#: inc/options/woocommerce/single-product-elements.php:574
msgid "Container Border"
msgstr "容器边框"

#: inc/options/woocommerce/card-product-elements.php:480
msgid "Automatically hide \"Add to cart\" button after adding the product to cart."
msgstr "将产品添加到购物车后自动隐藏“添加到购物车”按钮。"

#: inc/options/woocommerce/card-product-elements.php:476
msgid "Auto Hide"
msgstr "自动隐藏"

#: inc/options/general/posts-listing.php:225
#: inc/options/woocommerce/card-product-elements.php:335
msgid "Heading Tag"
msgstr "标题标签"

#: inc/integrations/coauthors.php:32
msgid " and "
msgstr " 和 "

#: inc/panel-builder/header/mobile-menu/options.php:93
msgid "Submenu Dots"
msgstr "子菜单点"

#: inc/panel-builder/header/button/options.php:528
msgid "Secondary Label Text Color"
msgstr "次要标签文本颜色"

#: inc/panel-builder/header/button/options.php:519
msgid "Secondary Label Text Font"
msgstr "次要标签文本字体"

#: inc/panel-builder/header/button/options.php:54
msgid "Secondary Label"
msgstr "次要标签"

#: inc/panel-builder/header/menu/options.php:396
#: inc/panel-builder/header/menu/options.php:437
#: inc/panel-builder/header/menu/options.php:476
#: inc/panel-builder/header/menu/options.php:514
msgid "Indicator Color"
msgstr "指示灯颜色"

#: inc/components/blocks/blocks-fallback.php:39
msgid "Advanced Search"
msgstr "高级搜索"

#: inc/components/blocks/blocks-fallback.php:42
msgid "Dynamic Data"
msgstr "动态数据"

#: inc/components/blocks/blocks-fallback.php:35
msgid "About Me"
msgstr "关于我"

#: inc/components/blocks/blocks-fallback.php:38
msgid "Advanced Posts"
msgstr "高级文章"

#: searchform.php:280
msgid "Search in category"
msgstr "搜索分类"

#: inc/panel-builder/header/text/options.php:319
#: inc/panel-builder/header/text/options.php:348
#: inc/panel-builder/header/text/options.php:370
#: inc/panel-builder/header/text/options.php:391
msgid "Heading Color"
msgstr "标题颜色"

#: inc/panel-builder/header/offcanvas/options.php:95
msgid "Panel Heading"
msgstr "面板标题"

#: inc/panel-builder/header/logo/options.php:246
#: inc/panel-builder/header/offcanvas-logo/options.php:60
msgid "Inline SVG File"
msgstr "内联 SVG 文件"

#: inc/panel-builder/header/button/options.php:174
msgid "Create Popup"
msgstr "创建弹窗"

#: inc/options/woocommerce/single-product-elements.php:589
#: inc/panel-builder/footer/options.php:157
msgid "Container Border Radius"
msgstr "容器边框半径"

#: inc/panel-builder/footer/options.php:24
msgid "Container Bottom Offset"
msgstr "容器底部间距"

#: inc/options/woocommerce/single-product-tabs.php:141
msgid "Description Heading "
msgstr "描述标题 "

#: inc/options/woocommerce/single-product-tabs.php:122
msgid "Close Adjacent Tabs"
msgstr "关闭相邻选项卡"

#: inc/options/woocommerce/single-product-tabs.php:110
msgid "First Tab Expanded"
msgstr "展开第一个选项卡"

#: inc/options/woocommerce/single-product-tabs.php:92
msgid "Summary"
msgstr "摘要"

#: inc/options/woocommerce/single-product-layers.php:408
msgid "Items"
msgstr "项目"

#: inc/options/woocommerce/single-product-layers.php:396
msgid "Additional Info"
msgstr "其他信息"

#: inc/options/woocommerce/single-product-layers.php:302
msgid "Payment Methods"
msgstr "支付方式"

#: inc/options/woocommerce/single-product-layers.php:50
msgid "Item Label"
msgstr "项目标签"

#: inc/options/woocommerce/single-product-layers.php:31
msgid "Google Pay"
msgstr "Google Pay"

#: inc/options/woocommerce/single-product-layers.php:27
msgid "Apple Pay"
msgstr "Apple Pay"

#: inc/options/woocommerce/single-product-layers.php:23
msgid "PayPal"
msgstr "PayPal"

#: inc/options/woocommerce/single-product-layers.php:19
#: inc/options/woocommerce/single-product-layers.php:345
msgid "Discover"
msgstr "发现"

#: inc/options/woocommerce/single-product-layers.php:15
#: inc/options/woocommerce/single-product-layers.php:339
msgid "Amex"
msgstr "美国运通"

#: inc/options/woocommerce/single-product-layers.php:11
#: inc/options/woocommerce/single-product-layers.php:333
msgid "Mastercard"
msgstr "万事达卡"

#: inc/options/woocommerce/single-product-layers.php:7
#: inc/options/woocommerce/single-product-layers.php:327
msgid "Visa"
msgstr "Visa"

#: inc/options/woocommerce/single-product-gallery.php:339
msgid "Image size used for the gallery thumbnails on single product pages."
msgstr "用于产品详情页上的相册缩略图的图像大小。"

#: inc/options/woocommerce/single-product-elements.php:535
msgid "Payment Methods Icons Color"
msgstr "支付方式图标颜色"

#: inc/options/woocommerce/card-product-elements.php:727
#: inc/options/woocommerce/single-product-elements.php:190
msgid "Price Font Color"
msgstr "价格字体颜色"

#: inc/options/woocommerce/single-product-elements.php:118
msgid "Sticky Container"
msgstr "置顶容器"

#: inc/options/woocommerce/general/product-badges.php:104
#: inc/options/woocommerce/general/product-badges.php:118
#: inc/options/woocommerce/general/product-badges.php:163
msgid "Badge Label"
msgstr "徽章标签"

#: inc/options/woocommerce/general/product-badges.php:53
msgid "Show Sale Badge"
msgstr "显示促销徽章"

#: inc/options/woocommerce/general/product-badges.php:13
msgid "Product Badges"
msgstr "产品徽章"

#: inc/options/woocommerce/card-product-elements.php:904
msgid "SKU Color"
msgstr "SKU 颜色"

#: inc/options/woocommerce/card-product-elements.php:897
msgid "SKU Font"
msgstr "SKU 字体"

#: inc/options/woocommerce/card-product-elements.php:853
msgid "Categories Button Color"
msgstr "分类按钮颜色"

#: inc/options/woocommerce/card-product-elements.php:564
msgid "Rows Gap"
msgstr "行间距"

#: inc/options/woocommerce/card-product-elements.php:548
msgid "Columns Gap"
msgstr "列间距"

#: inc/options/woocommerce/card-product-elements.php:499
msgid "Add to Cart and Price"
msgstr "添加到购物车和价格"

#: inc/options/woocommerce/card-product-elements.php:238
msgid "Product Image"
msgstr "产品图片"

#: inc/options/woocommerce/archive-main.php:223
msgid "Products Sorting"
msgstr "产品排序"

#: inc/options/woocommerce/archive-main.php:213
msgid "Results Count"
msgstr "结果数量"

#: inc/options/single-elements/related-posts.php:615
msgid "Posts Meta Font"
msgstr "文章元数据字体"

#: inc/options/single-elements/related-posts.php:547
msgid "Posts Title Font"
msgstr "文章标题字体"

#: inc/options/single-elements/post-share-box.php:479
#: inc/options/single-elements/post-tags.php:116
#: inc/options/single-elements/related-posts.php:493
#: inc/options/woocommerce/related-upsells.php:122
msgid "Module Title Font"
msgstr "模块标题字体"

#: inc/options/pages/author-page.php:47 inc/options/pages/search-page.php:47
#: inc/options/posts/blog.php:54 inc/options/posts/categories.php:40
#: inc/options/posts/custom-post-type-archive.php:50
msgid "Posts Listing"
msgstr "文章列表"

#: inc/options/general/typography.php:143
msgid "Figcaption"
msgstr "图片描述"

#: inc/options/general/posts-listing.php:490
msgid "Button Type"
msgstr "按钮类型"

#: inc/options/general/posts-listing.php:445
msgid "Excerpt Type"
msgstr "摘要类型"

#: inc/options/general/layout.php:58
msgid "Adjusts horizontal spacing between main content area and edges of the screen."
msgstr "调整主要内容区域和屏幕边缘之间的水平间距。"

#: inc/options/general/layout.php:45
msgid "Content Edge Spacing"
msgstr "内容边缘间距"

#: inc/options/general/layout.php:41
msgid "Adjusts vertical spacing between main content area and header/footer."
msgstr "调整主要内容区域和页眉/页脚之间的垂直间距。"

#: inc/options/general/breadcrumbs.php:161
msgid "Shop Page in Breadcrumbs"
msgstr "在面包屑导航中显示商店页面"

#: inc/options/general/breadcrumbs.php:148
msgid "Blog Page in Breadcrumbs"
msgstr "在面包屑导航中显示博客页面"

#: inc/helpers.php:302
msgid "WooCommerce Archive Thumbnail"
msgstr "WooCommerce 归档缩略图"

#: inc/helpers.php:298
msgid "WooCommerce Gallery Thumbnail"
msgstr "WooCommerce 相册缩略图"

#: inc/helpers.php:297
msgid "WooCommerce Single"
msgstr "WooCommerce 产品详情页"

#: inc/helpers.php:296
msgid "WooCommerce Thumbnail"
msgstr "WooCommerce 缩略图"

#: inc/components/woocommerce/single/single.php:365
#: inc/options/woocommerce/single-product-layers.php:402
msgid "Extra Features"
msgstr "其他功能"

#: inc/components/woocommerce/single/single.php:349
#: inc/options/woocommerce/single-product-layers.php:437
msgid "Money Back Guarantee"
msgstr "退款保证"

#: inc/components/woocommerce/single/single.php:344
#: inc/options/woocommerce/single-product-layers.php:432
msgid "Worldwide Shipping"
msgstr "全球运输"

#: inc/components/woocommerce/single/single.php:339
#: inc/options/woocommerce/single-product-layers.php:427
msgid "Satisfaction Guarantee"
msgstr "满意保证"

#: inc/components/woocommerce/single/single.php:334
#: inc/options/woocommerce/single-product-layers.php:422
msgid "Secure Payments"
msgstr "安全支付"

#: inc/components/woocommerce/single/single.php:329
#: inc/options/woocommerce/single-product-layers.php:417
msgid "Premium Quality"
msgstr "优质"

#: inc/components/woocommerce/single/single.php:233
#: inc/options/woocommerce/single-product-layers.php:312
msgid "Guaranteed Safe Checkout"
msgstr "安全结帐保证"

#: inc/components/woocommerce/single/additional-actions-layer.php:104
#: inc/components/woocommerce/single/single-modifications.php:101
#: inc/options/general/posts-listing.php:255
#: inc/options/general/posts-listing.php:428
#: inc/options/general/posts-listing.php:473
#: inc/options/general/posts-listing.php:525
#: inc/options/general/posts-listing.php:572
#: inc/options/general/posts-listing.php:593
#: inc/options/woocommerce/card-product-elements.php:316
#: inc/options/woocommerce/card-product-elements.php:355
#: inc/options/woocommerce/card-product-elements.php:375
#: inc/options/woocommerce/card-product-elements.php:392
#: inc/options/woocommerce/card-product-elements.php:428
#: inc/options/woocommerce/card-product-elements.php:455
#: inc/options/woocommerce/card-product-elements.php:484
#: inc/options/woocommerce/card-product-elements.php:506
#: inc/options/woocommerce/single-product-layers.php:136
#: inc/options/woocommerce/single-product-layers.php:153
#: inc/options/woocommerce/single-product-layers.php:170
#: inc/options/woocommerce/single-product-layers.php:190
#: inc/options/woocommerce/single-product-layers.php:210
#: inc/options/woocommerce/single-product-layers.php:228
#: inc/options/woocommerce/single-product-layers.php:271
#: inc/options/woocommerce/single-product-layers.php:288
#: inc/options/woocommerce/single-product-layers.php:381
#: inc/options/woocommerce/single-product-layers.php:444
msgid "Bottom Spacing"
msgstr "底部间距"

#: inc/components/woocommerce/single/additional-actions-layer.php:49
msgid "Buttons Type"
msgstr "按钮类型"

#: inc/components/woocommerce/single/additional-actions-layer.php:45
#: inc/options/woocommerce/card-product-elements.php:20
#: inc/options/woocommerce/card-product-elements.php:49
msgid "Additional Actions"
msgstr "其他操作"

#: inc/components/woocommerce/common/stock-badge.php:34
#: inc/options/woocommerce/general/product-badges.php:166
msgid "SOLD OUT"
msgstr "售罄"

#: inc/components/woocommerce/common/sale-flash.php:13
msgid "SALE"
msgstr "促销"

#: inc/components/woocommerce/common/rest-api.php:143
msgid "Product Status"
msgstr "产品状态"

#. translators: 1: the search query
#: inc/components/hero/elements.php:198
msgid "Search Results for %1$s"
msgstr "%1$s 的搜索结果"

#: inc/components/builder/header-elements.php:289
msgid "Close search modal"
msgstr "关闭搜索模式"

#: searchform.php:123
msgid "Select Category"
msgstr "选择分类"

#: inc/options/pages/search-page.php:111
#: inc/panel-builder/header/search/options.php:460
msgid "Live Results Product Status"
msgstr "实时结果产品状态"

#: inc/components/blocks/legacy/legacy-newsletter-subscribe.php:18
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "在下面输入您的电子邮件地址以订阅我们的新闻通讯"

#: inc/components/blocks/legacy/legacy-newsletter-subscribe.php:17
msgid "Newsletter"
msgstr "新闻通讯"

#: inc/components/blocks/legacy/legacy-advertisement-transformer.php:19
msgid "Insert ad code here"
msgstr "在这里插入广告代码"

#: inc/components/blocks/legacy/legacy-advertisement-transformer.php:17
msgid "Advertisement"
msgstr "广告"

#: inc/components/woocommerce/common/rest-api.php:138
msgid "In Stock"
msgstr "有货"

#: inc/options/general/meta.php:418
msgid "Terms accent color"
msgstr "分类项强调色"

#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:51
msgid "Mobile:"
msgstr "手机："

#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:43
msgid "Phone:"
msgstr "电话："

#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:35
msgid "Address:"
msgstr "地址："

#: inc/components/blocks/blocks-fallback.php:37
#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:29
msgid "Contact Info"
msgstr "联系信息"

#: inc/components/blocks/legacy/legacy-about-me-transformer.php:17
msgid "About me"
msgstr "关于我"

#: inc/classes/screen-manager.php:256 inc/classes/screen-manager.php:267
msgid "Singulars"
msgstr "详情页"

#: inc/classes/screen-manager.php:220 inc/classes/screen-manager.php:231
msgid "Archives"
msgstr "归档"

#: inc/classes/screen-manager.php:212
msgid "WooCommerce Categories"
msgstr "WooCommerce 分类"

#: admin/dashboard/plugins/config.php:21
msgid "Greenshift helps you create complex pages and animations without code skills and directly inside core editor."
msgstr "Greenshift 可以帮助您直接在核心编辑器中创建复杂的页面和动画，无需代码技能。"

#: inc/options/integrations/the-events-calendar/single.php:23
msgid "Events Calendar Single Structure"
msgstr "活动日历详情页结构"

#: inc/options/integrations/the-events-calendar/archive.php:21
msgid "Events Calendar Archive Structure"
msgstr "活动日历归档页结构"

#: inc/options/general/custom-post-types.php:76
msgid "The Events Calendar"
msgstr "活动日历"

#: inc/options/general/breadcrumbs.php:134
msgid "Archive Taxonomy Title"
msgstr "分类法归档标题"

#: inc/options/general/breadcrumbs.php:124
msgid "Single Page/Post Taxonomy Title"
msgstr "页面/文章分类法页面标题"

#: inc/options/general/breadcrumbs.php:114
msgid "Single Page/Post Title"
msgstr "页面/文章标题"

#: inc/options/single-elements/post-share-box.php:183
msgid "X (Twitter)"
msgstr "X（推特）"

#: inc/options/single-elements/author-box.php:106
msgid "Author Archive Link"
msgstr "作者归档链接"

#: inc/panel-builder/header/search/options.php:582
msgid "Input Border Color"
msgstr "输入边框颜色"

#. translators: %s is the product name
#: woocommerce/cart/cart.php:178 woocommerce/cart/cart.php:226
#: woocommerce/cart/mini-cart.php:53
msgid "Remove %s from cart"
msgstr "从购物车中删除 %s"

#: admin/dashboard/plugins/config.php:27
msgid "Build stunning lightbox galleries, masonry grids, custom grids with no more than a few clicks right from the WordPress dashboard."
msgstr "只需在 WordPress 仪表盘中单击几下，即可构建令人惊叹的灯箱画廊、砌体网格、自定义网格。"

#: inc/panel-builder/header/menu/options.php:145
msgid "Collapse menu items in to a dropdown if there is no enough room in the row. "
msgstr "如果行中没有足够的空间， 则将菜单项折叠到下拉列表中。 "

#: inc/panel-builder/header/menu/options.php:141
msgid "Collapse Non Fitting Items"
msgstr "折叠不适应的项目"

#. translators: 1: link to WP admin new post page open 2: link closing.
#: template-parts/content-none.php:16
msgid "Ready to publish your first post? %1$sGet started here%2$s."
msgstr "准备好发布您的第一篇文章了吗？ %1$s从这里开始%2$s。"

#. translators: 1: span opening 2: Post title 3: span closing.
#: inc/components/single/content-helpers.php:274
msgid "Edit%1$s \"%2$s\"%3$s"
msgstr "编辑%1$s \"%2$s\"%3$s"

#. translators: 1: span open 2: Name of current post. Only visible to screen
#. readers 3: span closing
#: inc/components/single/content-helpers.php:229 inc/template-tags.php:142
msgid "Continue reading%1$s \"%2$s\"%3$s"
msgstr "继续阅读%1$s \"%2$s\"%3$s"

#: inc/panel-builder/header/button/options.php:139
msgid "Set link to sponsored"
msgstr "设置赞助链接"

#. translators: 1: span opening 2: span closing 3: the number of results
#: inc/components/hero/elements.php:330
msgid "%1$sSearch Results for%2$s %3$s"
msgstr "%1$s%2$s %3$s 的搜索结果"

#: inc/options/general/typography.php:121
msgid "Pullquote"
msgstr "引述"

#: inc/options/general/typography.php:114
msgid "Quote"
msgstr "引用"

#: inc/panel-builder/header/menu/options.php:724
msgid "Please note, this option will affect only submenus on 3rd level and below."
msgstr "请注意，此选项只会影响第 3 级及以下的子菜单。"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/header/button/options.php:303
#: inc/panel-builder/header/logo/options.php:394
msgid "Add a custom %saria label%s attribute."
msgstr "添加自定义 %s咏叹调标签%s 属性。"

#: inc/panel-builder/header/button/options.php:296
#: inc/panel-builder/header/logo/options.php:387
msgid "Custom Aria Label"
msgstr "自定义咏叹调标签"

#: inc/panel-builder/header/search/options.php:429
msgid "Live Results"
msgstr "实时结果"

#: searchform.php:267
msgid "Search for..."
msgstr "搜索..."

#: inc/options/general/form-elements.php:231
msgid "Checkbox Border Radius"
msgstr "选择框圆角半径"

#: inc/options/general/comments-single.php:47
msgid "Outside"
msgstr "外部"

#: inc/options/general/comments-single.php:46
msgid "Inside"
msgstr "内部"

#: inc/options/general/comments-single.php:39
msgid "Inputs Label Position"
msgstr "输入框提示位置"

#: inc/options/general/back-to-top.php:31
#: inc/options/woocommerce/single-product-layers.php:55
#: inc/options/woocommerce/single-product-layers.php:100
msgid "Icon Source"
msgstr "图标来源"

#: inc/options/single-elements/author-box.php:300
msgid "Author Bio Font Color"
msgstr "作者简介字体颜色"

#: inc/options/single-elements/author-box.php:294
msgid "Author Bio Font"
msgstr "作者简介字体"

#: inc/options/single-elements/author-box.php:241
msgid "Author Name Font"
msgstr "作者姓名字体"

#: inc/options/pages/page.php:158
#: inc/options/posts/custom-post-type-single.php:218
#: inc/options/posts/post.php:149
msgid "This single page is overrided by a custom template, to edit it please access %sthis page%s."
msgstr "此单个页面已被自定义模板覆盖，要对其进行编辑，请访问%s此页面%s。"

#: inc/options/single-elements/post-nav.php:53
msgid "Navigate through posts that are from the same taxonomy."
msgstr "浏览来自相同分类的帖子。"

#: inc/options/single-elements/post-nav.php:34
msgid "Navigation Criteria"
msgstr "导航条件"

#: inc/options/woocommerce/card-product-elements.php:687
msgid "Short Description Color"
msgstr "简介颜色"

#: inc/options/woocommerce/card-product-elements.php:680
msgid "Short Description Font"
msgstr "简介字体"

#: inc/options/woocommerce/single-product-layers.php:207
msgid "Short Description"
msgstr "简介"

#: inc/options/pages/search-page.php:104
#: inc/panel-builder/header/search/options.php:452
msgid "Live Results Product Price"
msgstr "实时结果产品价格"

#: inc/options/integrations/tutorlms-single.php:141
msgid "Rating Font Color"
msgstr "评级字体颜色"

#: inc/options/integrations/tutorlms-single.php:129
msgid "Rating Font"
msgstr "评级字体"

#: inc/options/integrations/tutorlms-single.php:97
msgid "Course Actions Font Color"
msgstr "课程操作字体颜色"

#: inc/options/integrations/tutorlms-single.php:84
msgid "Course Actions Font"
msgstr "课程操作字体"

#: inc/options/integrations/tutorlms-single.php:52
#: inc/options/woocommerce/card-product-elements.php:781
#: inc/options/woocommerce/card-product-elements.php:821
msgid "Categories Font Color"
msgstr "分类字体颜色"

#: inc/options/integrations/tutorlms-single.php:39
#: inc/options/woocommerce/card-product-elements.php:762
msgid "Categories Font"
msgstr "分类字体"

#: inc/options/general/performance.php:92
msgid "Shop Archive Featured Image"
msgstr "商店归档特色图片"

#: inc/options/general/performance.php:85
msgid "Single Product Image"
msgstr "单品图片"

#: inc/options/general/custom-post-types.php:67
msgid "Course Single"
msgstr "当然单"

#: inc/options/general/custom-post-types.php:61
msgid "Course Archive"
msgstr "课程归档"

#: inc/options/general/comments-single.php:64
msgid "Above List"
msgstr "以上列表"

#: inc/options/general/comments-single.php:63
msgid "Below List"
msgstr "下面的列表"

#: inc/options/general/comments-single.php:56
msgid "Comment Form Position"
msgstr "评论表单位置"

#: inc/manager.php:238 inc/manager.php:244
msgid "You got %s result. Please press Tab to select it."
msgid_plural "You got %s results. Please press Tab to select one."
msgstr[0] "找到 %s 个结果，请按 Tab 键选择。"

#: inc/manager.php:235 inc/manager.php:237 searchform.php:345
msgid "No results"
msgstr "无结果"

#: inc/options/general/performance.php:76
msgid "Related Posts Featured Image"
msgstr "相关文章特色图片"

#: inc/options/general/performance.php:69
msgid "Archives Featured Image"
msgstr "归档特色图片"

#: inc/options/general/performance.php:62
msgid "Single Post/Page Featured Image"
msgstr "单个文章/页面特色图片"

#: inc/options/general/performance.php:55
msgid "Post/Page Title Featured Image"
msgstr "文章/页面标题特色图片"

#: inc/options/general/performance.php:45
msgid "Enable lazy loading to improve performance."
msgstr "启用延迟加载以提高性能。"

#: inc/options/single-elements/structure-design.php:88
msgid "Content Area Border"
msgstr "内容区域边框"

#: inc/options/pages/author-page.php:101 inc/options/pages/search-page.php:137
#: inc/options/posts/blog.php:128 inc/options/posts/categories.php:94
msgid "This archive page is overrided by a custom template, to edit it please access %sthis page%s."
msgstr "此归档页面已被自定义模板覆盖，要对其进行编辑，请访问 %s此页面%s。"

#: inc/panel-builder/footer/copyright/options.php:14
#: inc/panel-builder/footer/copyright/view.php:28
msgid "Copyright &copy; {current_year} - WordPress Theme by {theme_author}"
msgstr "版权所有 © {current_year} - {theme_author} 的 WordPress 主题"

#: inc/options/single-elements/post-nav.php:222
msgid "Thumbnail Border Radius"
msgstr "缩略图边框半径"

#: inc/options/woocommerce/general/product-badges.php:25
msgid "Badge Shape"
msgstr "徽章形状"

#: inc/components/breadcrumbs.php:632
msgctxt "breadcrumb"
msgid "Home"
msgstr "主页"

#: inc/panel-builder/header/middle-row/options.php:100
msgid "Render Empty Row"
msgstr "渲染空行"

#: inc/options/single-elements/related-posts.php:286
msgid "Posts Title Tag"
msgstr "文章标题标签"

#: inc/options/general/page-title.php:801
msgid "Image Parallax Effect"
msgstr "图片视差效果"

#: inc/options/woocommerce/general/messages.php:250
msgid "Error Messages"
msgstr "错误消息"

#: inc/options/woocommerce/general/messages.php:134
msgid "Success Messages"
msgstr "成功消息"

#: inc/options/woocommerce/general/messages.php:20
msgid "Info Messages"
msgstr "信息消息"

#: inc/panel-builder/header/cart/view.php:208
msgid "Shopping cart"
msgstr "购物车"

#: inc/panel-builder/header/offcanvas-logo/config.php:4
msgid "Off Canvas Logo"
msgstr "关闭帆布标志"

#: inc/panel-builder/header/menu/view.php:123
msgid "Header Menu"
msgstr "页眉菜单"

#: inc/panel-builder/header/button/options.php:290
#: inc/panel-builder/header/logo/options.php:383
msgid "Separate multiple classes with spaces."
msgstr "多个类请用空格分开。"

#: inc/panel-builder/header/button/options.php:285
#: inc/panel-builder/header/logo/options.php:378
msgid "CSS Class"
msgstr "CSS 类"

#: inc/options/single-elements/author-box.php:247
msgid "Author Name Font Color"
msgstr "作者姓名字体颜色"

#: inc/options/single-elements/author-box.php:55
msgid "Author Name Tag"
msgstr "作者姓名标签"

#: inc/css/fonts-manager.php:244 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13
#: static/js/options/options/typography/default-data.js:27
#: static/js/options/options/typography/helpers.js:118
msgid "System Default"
msgstr "系统默认"

#: inc/options/posts/woo-general.php:372
msgid "Single"
msgstr "单身的"

#: inc/options/posts/woo-general.php:360
msgid "Show Stock Badge"
msgstr "显示股票徽章"

#: inc/components/woocommerce/common/stock-badge.php:31
#: inc/options/posts/woo-general.php:393
msgid "OUT OF STOCK"
msgstr "缺货"

#: inc/components/woocommerce/common/sale-flash.php:8
msgid "SALE!"
msgstr "销售！"

#: inc/panel-builder/header/mobile-menu/options.php:307
msgid "Container Margin"
msgstr "容器边距"

#: inc/panel-builder/header/mobile-menu/options.php:246
msgid "Dropdown Font"
msgstr "下拉字体"

#: inc/panel-builder/header/mobile-menu/options.php:101
msgid "Items Vertical Spacing"
msgstr "项目垂直间距"

#: inc/panel-builder/header/mobile-menu/options.php:76
msgid "Dropdown Toggle Shape"
msgstr "下拉切换形状"

#: inc/panel-builder/header/mobile-menu/options.php:48
msgid "Dropdown Toggle Icon"
msgstr "下拉切换图标"

#: inc/panel-builder/header/menu/options.php:703
msgid "Top Offset (Sticky State)"
msgstr "顶部偏移（粘性状态）"

#: inc/panel-builder/header/menu/options.php:623
msgid "Only Arrow"
msgstr "只有箭头"

#: inc/panel-builder/header/menu/options.php:622
msgid "Entire Item"
msgstr "整个项目"

#: inc/panel-builder/header/menu/options.php:614
msgid "Click Area"
msgstr "点击区域"

#: inc/panel-builder/header/menu/options.php:605
msgid "Choose the interaction mode with the menu dropdown. "
msgstr "使用下拉菜单选择交互模式。"

#: inc/panel-builder/header/menu/options.php:603
msgid "Click"
msgstr "点击"

#: inc/panel-builder/header/menu/options.php:595
msgid "Interaction Type"
msgstr "交互类型"

#: inc/panel-builder/header/menu/options.php:559
msgid "Items Border Radius"
msgstr "项目圆角半径"

#: inc/options/woocommerce/general/quantity-input.php:24
msgid "Custom Quantity Input"
msgstr "自定义数量输入"

#: inc/manager.php:252
msgid "Collapse dropdown menu"
msgstr "折叠下拉菜单"

#: inc/components/menus.php:80 inc/components/menus.php:93
#: inc/components/menus.php:242 inc/manager.php:251
msgid "Expand dropdown menu"
msgstr "展开下拉菜单"

#: inc/components/builder/header-elements.php:138
msgid "Close drawer"
msgstr "关闭抽屉"

#: inc/panel-builder/header/button/options.php:278
#: inc/panel-builder/header/text/options.php:142
msgid "Logged Out"
msgstr "注销登录"

#: inc/panel-builder/header/button/options.php:277
#: inc/panel-builder/header/text/options.php:141
msgid "Logged In"
msgstr "登录"

#: inc/options/general/posts-listing.php:369
#: inc/options/single-elements/featured-image.php:55
#: inc/options/woocommerce/card-product-elements.php:305
msgid "Video Thumbnail"
msgstr "视频缩略图"

#: inc/panel-builder/header/cart/options.php:937
#: inc/panel-builder/header/offcanvas/options.php:223
#: inc/panel-builder/header/search/options.php:686
msgid "Close Button Type"
msgstr "关闭按钮类型"

#: inc/options/single-elements/post-nav.php:202
msgid "Thumbnail Overlay Color"
msgstr "缩略图叠加颜色"

#: inc/options/woocommerce/single-product-gallery.php:244
msgid "Lightbox Button Background"
msgstr "灯箱按钮背景"

#: inc/options/woocommerce/single-product-gallery.php:213
msgid "Lightbox Button Icon Color"
msgstr "灯箱按钮图标颜色"

#: inc/options/general/back-to-top.php:289
msgid "Shape Border Radius"
msgstr "形状圆角半径"

#: inc/panel-builder/header/text/options.php:25
msgid "Allow the item container to expand and fill in all the available space."
msgstr "允许项目容器扩展并填充所有可用空间。"

#: inc/panel-builder/header/text/options.php:24
msgid "Stretch Container"
msgstr "拉伸容器"

#: inc/options/general/buttons.php:130
#: inc/panel-builder/header/button/options.php:844
msgid "Padding"
msgstr "填充"

#: admin/dashboard/plugins/config.php:33
msgid "A better way to translate your WordPress site and go multilingual, directly from the front-end using a visual translation interface.s."
msgstr "使用可视化翻译界面直接从前端翻译 WordPress 网站并使用多语言的更好方法。"

#: inc/panel-builder/header/search/options.php:553
msgid "Input Font Color"
msgstr "输入字体颜色"

#: inc/options/general/posts-listing.php:1365
msgid "Card Overlay Color"
msgstr "卡片叠加颜色"

#: inc/options/general/posts-listing.php:653
msgid "Card Min Height"
msgstr "卡片最小高度"

#: inc/panel-builder/header/button/options.php:157
#: inc/panel-builder/header/button/options.php:171
msgid "Popup Template"
msgstr "弹出框模板"

#: inc/panel-builder/header/button/options.php:118
msgid "Link/URL"
msgstr "链接/URL"

#: inc/panel-builder/header/button/options.php:108
msgid "Open Popup"
msgstr "打开弹出窗口"

#: inc/panel-builder/header/button/options.php:107
msgid "Open Link"
msgstr "打开链接"

#: inc/panel-builder/header/button/options.php:97
msgid "Click Behavior"
msgstr "点击行为"

#: inc/options/single-elements/related-posts.php:148
msgid "Random"
msgstr "随机"

#: inc/options/single-elements/related-posts.php:147
msgid "Most Commented"
msgstr "最多留言"

#: inc/options/single-elements/related-posts.php:146
msgid "Recent"
msgstr "最新"

#: inc/options/single-elements/related-posts.php:140
msgid "Sort by"
msgstr "排序由"

#: inc/panel-builder/header/mobile-menu/options.php:39
msgid "This option will collapse/expand the sub menu items on click/touch."
msgstr "此选项将在单击/触摸时折叠/展开子菜单项。"

#: inc/panel-builder/header/mobile-menu/options.php:35
msgid "Interactive Collapse"
msgstr "互动式折叠"

#: inc/options/general/posts-listing.php:451
msgid "Full Post"
msgstr "全文"

#: inc/panel-builder/footer/widget-area-6/config.php:4
msgid "Widget Area 6"
msgstr "小工具区域 6"

#: inc/panel-builder/footer/widget-area-5/config.php:4
msgid "Widget Area 5"
msgstr "小工具区域 5"

#: inc/panel-builder/footer/widget-area-4/config.php:4
msgid "Widget Area 4"
msgstr "小工具区域 4"

#: inc/panel-builder/footer/widget-area-3/config.php:4
msgid "Widget Area 3"
msgstr "小工具区域 3"

#: inc/panel-builder/footer/widget-area-2/config.php:4
msgid "Widget Area 2"
msgstr "小工具区域 2"

#: inc/panel-builder/footer/widget-area-1/config.php:4
msgid "Widget Area 1"
msgstr "小工具区域 1"

#: inc/options/general/meta.php:354 inc/options/single-elements/post-nav.php:42
#: inc/options/single-elements/post-nav.php:52
msgid "Taxonomy"
msgstr "分类法"

#: inc/options/general/layout.php:72
msgid "This option applies only if the posts or pages are set to Narrow Width structure."
msgstr "仅当文章或页面设置为窄宽度结构时，此选项才适用。"

#: inc/options/general/content-elements.php:31
msgid "Adjusts the spacing between the Gutenberg blocks."
msgstr "调整古腾堡区块之间的间距。"

#: inc/options/engagement/social-accounts.php:36
msgid "Easily link your social media accounts and display them throughout your website, with the various elements provided in the customizer."
msgstr "使用定制器中提供的各种元素轻松链接您的社交媒体帐户并在整个网站中显示它们。"

#: inc/options/engagement/general.php:20
msgid "Enable Schema.org markup features for your website. You can disable this option if you use a SEO plugin and let it do the work."
msgstr "为您的网站启用 Schema.org 标记功能。如果您使用 SEO 插件来完成此工作，您可以禁用此选项。"

#: inc/panel-builder/header/button/options.php:133
msgid "Set link to nofollow"
msgstr "将链接设置为nofollow"

#: inc/components/woocommerce/single/additional-actions-layer.php:55
msgid "Link"
msgstr "链接"

#: inc/panel-builder/footer/widget-area-1/options.php:102
msgid "Links Decoration"
msgstr "链接装饰"

#: inc/options/single-elements/related-posts.php:202
msgid "Module Title Alignment"
msgstr "模块标题对齐"

#: inc/options/general/page-title.php:619
#: inc/options/single-elements/author-box.php:177
#: inc/options/single-elements/post-share-box.php:318
#: inc/panel-builder/footer/socials/options.php:59
#: inc/panel-builder/header/socials/options.php:58
msgid "Set links to nofollow"
msgstr "将链接设置为nofollow"

#: inc/options/single-elements/post-nav.php:148
msgid "Module Visibility"
msgstr "模块可见性"

#: inc/options/single-elements/post-nav.php:128
msgid "Title Visibility"
msgstr "标题可见性"

#: inc/options/woocommerce/single-product-gallery.php:54
msgid "Sticky Gallery"
msgstr "粘性画廊"

#: inc/options/woocommerce/single-product-gallery.php:178
msgid "Prev/Next Background"
msgstr "上一个/下一个背景"

#: inc/options/woocommerce/single-product-gallery.php:148
msgid "Prev/Next Arrow"
msgstr "上一个/下一个箭头"

#: inc/options/woocommerce/card-product-elements.php:718
#: inc/options/woocommerce/single-product-elements.php:180
msgid "Price Font"
msgstr "价格字体"

#: inc/options/woocommerce/card-product-elements.php:1104
msgid "Card Border Radius"
msgstr "卡片圆角半径"

#: inc/options/general/sidebar.php:210
msgid "Last Widgets"
msgstr "最后的小工具"

#: inc/options/general/sidebar.php:197
msgid "Last X Widgets"
msgstr "最后 X 个小工具"

#: inc/options/general/sidebar.php:196
msgid "Entire Sidebar"
msgstr "整个侧边栏"

#: inc/options/general/sidebar.php:189
msgid "Stick Behavior"
msgstr "黏附形式"

#: inc/options/general/sidebar.php:178
msgid "Sticky Top Offset"
msgstr "粘性顶部偏移"

#: inc/options/general/posts-listing.php:1609
#: inc/options/single-elements/related-posts.php:84
msgid "Number of posts"
msgstr "文章数量"

#: inc/options/general/posts-listing.php:1583
#: inc/options/single-elements/related-posts.php:51
msgid "Columns & Posts"
msgstr "专栏和文章"

#: inc/options/general/performance.php:27
msgid "Enable this option if you want to remove WordPress emojis script in order to improve the performance."
msgstr "如果您想移除 WordPress 表情符号脚本以提高性能，请启用此选项。"

#: inc/options/general/performance.php:23
msgid "Disable Emojis Script"
msgstr "禁用表情符号脚本"

#: inc/options/general/page-title.php:469
#: inc/panel-builder/header/text/options.php:37
msgid "Max Width"
msgstr "最大宽度"

#: inc/components/social-box.php:655 inc/components/social-box.php:1560
msgid "Facebook Messenger"
msgstr "Facebook Messenger"

#: admin/dashboard/plugins/config.php:15
msgid "JetEngine is a dynamic content plugin that lets you build a complex websites fast and cost-effectively."
msgstr "JetEngine 是一个动态内容插件，可让您快速且经济高效地构建复杂的网站。"

#: inc/options/woocommerce/general/account-page.php:62
msgid "Navigation Quick Links"
msgstr "导航快速链接"

#: inc/init.php:496
msgid "Footer Widget Area "
msgstr "页脚小工具区域 "

#: inc/panel-builder/header/cart/options.php:675
msgid "Display the quantity input field inside the off-canvas cart panel."
msgstr "在画布外的购物车面板内显示数量输入字段。"

#: inc/components/woocommerce/common/account.php:98
msgid "Log out"
msgstr "注销登录"

#: inc/options/woocommerce/single-product-gallery.php:114
msgid "Image size used for the main image on single product pages."
msgstr "用于单个产品页面上的主图片大小。"

#: inc/options/woocommerce/general/checkout-page.php:184
msgid "I have read and agree to the website %s"
msgstr "我已阅读并同意网站 %s"

#: inc/options/woocommerce/general/checkout-page.php:181
msgid "Optionally add some text for the terms checkbox that customers must accept."
msgstr "（可选）为客户必须接受的条款复选框添加一些文本。"

#: inc/options/woocommerce/general/checkout-page.php:180
msgid "Terms and conditions"
msgstr "条款和条件"

#: inc/options/woocommerce/general/checkout-page.php:167
msgid "Your personal data will be used to process your order, support your experience throughout this website, and for other purposes described in our [privacy_policy]."
msgstr "您的个人数据将用于处理您的订单、支持您在整个网站上的体验，以及我们在 [隐私政策] 中描述的其他目的。"

#: inc/options/woocommerce/general/checkout-page.php:165
msgid "Optionally add some text about your store privacy policy to show during checkout."
msgstr "（可选）添加一些关于您的商店隐私政策的文本以在结帐时显示。"

#: inc/options/woocommerce/general/checkout-page.php:164
msgid "Privacy policy"
msgstr "隐私政策"

#: inc/options/woocommerce/general/checkout-page.php:152
msgid "Terms And Conditions Page"
msgstr "条款和条件页面"

#: inc/options/woocommerce/general/checkout-page.php:139
msgid "Privacy Policy Page"
msgstr "隐私政策页面"

#: inc/options/woocommerce/general/checkout-page.php:121
msgid "Phone Field"
msgstr "电话字段"

#: inc/options/woocommerce/general/checkout-page.php:103
msgid "Address Line 2 Field"
msgstr "地址 2 字段"

#: inc/options/woocommerce/general/checkout-page.php:94
#: inc/options/woocommerce/general/checkout-page.php:112
#: inc/options/woocommerce/general/checkout-page.php:130
msgid "Required"
msgstr "必填"

#: inc/options/woocommerce/general/checkout-page.php:93
#: inc/options/woocommerce/general/checkout-page.php:111
#: inc/options/woocommerce/general/checkout-page.php:129
msgid "Optional"
msgstr "可选的"

#: inc/options/woocommerce/general/checkout-page.php:92
#: inc/options/woocommerce/general/checkout-page.php:110
#: inc/options/woocommerce/general/checkout-page.php:128
msgid "Hidden"
msgstr "隐藏"

#: inc/options/woocommerce/general/checkout-page.php:85
msgid "Company Name Field"
msgstr "公司名称字段"

#: inc/options/woocommerce/general/checkout-page.php:74
msgid "Highlight Required Fields"
msgstr "高亮显示必填字段"

#: inc/options/woocommerce/general/checkout-page.php:58
msgid "Coupon Form"
msgstr "优惠券表单"

#: inc/options/woocommerce/general/account-page.php:159
msgid "Navigation Shadow"
msgstr "导航阴影"

#: inc/options/woocommerce/general/account-page.php:139
msgid "Navigation Divider Color"
msgstr "导航分隔线颜色"

#: inc/options/woocommerce/general/account-page.php:109
msgid "Navigation Background Color"
msgstr "导航背景颜色"

#: inc/options/woocommerce/general/account-page.php:79
msgid "Navigation Text Color"
msgstr "导航文本颜色"

#: inc/options/woocommerce/general/account-page.php:52
msgid "User Name"
msgstr "用户名"

#: inc/options/woocommerce/general/account-page.php:24
msgid "User Avatar"
msgstr "用户头像"

#: inc/options/woocommerce/general/account-page.php:13
msgid "Account Page"
msgstr "账户页面"

#: inc/options/woocommerce/general/messages.php:13
msgid "Messages"
msgstr "消息"

#: inc/options/woocommerce/general/quantity-input.php:13
#: inc/panel-builder/header/cart/options.php:671
msgid "Quantity Input"
msgstr "数量输入"

#: inc/options/woocommerce/general/checkout-page.php:30
msgid "No page set"
msgstr "没有设置页面"

#: inc/options/customizer.php:33 static/bundle/customizer-controls.js:49
#: static/js/customizer/components/ProOverlay.js:21
#: static/js/customizer/components/ProOverlay.js:66
msgid "View Pro Features"
msgstr "查看专业版功能"

#: inc/options/general/posts-listing.php:351
#: inc/options/woocommerce/card-product-elements.php:288
msgid "Zoom Out"
msgstr "缩小"

#: inc/options/general/posts-listing.php:350
#: inc/options/woocommerce/card-product-elements.php:287
msgid "Zoom In"
msgstr "放大"

#: inc/options/general/form-elements.php:242
msgid "Select Dropdown"
msgstr "选择下拉菜单"

#: searchform.php:257
msgid "Search button"
msgstr "搜索按钮"

#: inc/options/general/page-title.php:613
#: inc/options/single-elements/author-box.php:160
#: inc/panel-builder/footer/socials/options.php:52
#: inc/panel-builder/header/socials/options.php:52
msgid "Open links in new tab"
msgstr "在新标签页中打开链接"

#: inc/panel-builder/header/search/options.php:642
msgid "Search Button Color"
msgstr "搜索按钮颜色"

#: inc/panel-builder/header/search/options.php:613
msgid "Search Icon Color"
msgstr "搜索图标颜色"

#: inc/panel-builder/footer/menu/options.php:38
#: inc/panel-builder/footer/socials/options.php:138
msgid "Items Direction"
msgstr "项目方向"

#: inc/panel-builder/footer/middle-row/options.php:687
msgid "Columns Divider"
msgstr "列分隔符"

#: inc/panel-builder/footer/middle-row/options.php:388
msgid "Widgets Spacing"
msgstr "小工具间距"

#: inc/panel-builder/footer/middle-row/options.php:28
msgid "Columns"
msgstr "列"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/footer/socials/options.php:37
#: inc/panel-builder/header/socials/options.php:37
msgid "Configure the social links in General ➝ %sSocial Network Accounts%s."
msgstr "在 常规 ➝ %s社交网络账户%s 中配置社交链接。"

#: inc/options/single-elements/related-posts.php:624
msgid "Posts Meta Font Color"
msgstr "帖子元信息字体颜色"

#: inc/options/single-elements/post-nav.php:76
#: inc/panel-builder/header/trigger/options.php:69
msgid "Container Spacing"
msgstr "容器间距"

#: inc/options/woocommerce/single-product-tabs.php:302
msgid "Active Tab Colors"
msgstr "活动标签页颜色"

#: inc/options/woocommerce/related-upsells.php:92
msgid "Upsell Products Visibility"
msgstr "追加销售产品可见性"

#: inc/options/woocommerce/related-upsells.php:68
msgid "Related Products Visibility"
msgstr "相关产品可见性"

#: inc/components/woocommerce/common/rest-api.php:118
msgid "Product Price"
msgstr "产品价格"

#: inc/options/general/content-elements.php:13
msgid "Entry Content"
msgstr "作品内容"

#: inc/options/general/general.php:89
msgid "Click this button if you want to reset all settings to their default values."
msgstr "如果要将所有设置重置为其默认值，请单击此按钮。"

#: inc/options/general/general.php:88 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13
#: static/js/options/options/ct-customizer-reset-options.js:18
msgid "Reset Options"
msgstr "重置选项"

#: inc/options/general/general.php:79
msgid "Manage Options"
msgstr "管理选项"

#: inc/options/general/meta.php:364
#: inc/options/woocommerce/card-product-elements.php:410
#: inc/panel-builder/header/trigger/options.php:50
msgid "Style"
msgstr "样式"

#: inc/options/general/sidebar.php:251
msgid "Mobile Sidebar Position"
msgstr "移动端侧边栏位置"

#: inc/options/general/sidebar.php:232
msgid "Sidebar Visibility"
msgstr "侧边栏可见性"

#. translators: %s entity of font color
#: inc/options/general/page-title.php:1075
msgid "%s Font Color"
msgstr "%s 字体颜色"

#. translators: %s entity of font
#: inc/options/general/page-title.php:1063
msgid "%s Font"
msgstr "%s 字体"

#: inc/options/general/page-title.php:1011
#: inc/options/general/posts-listing.php:1024
msgid "Meta Button Background"
msgstr "元信息按钮背景"

#: inc/options/general/page-title.php:979
#: inc/options/general/posts-listing.php:992
msgid "Meta Button Font"
msgstr "元信息按钮字体"

#: inc/options/general/colors.php:613
msgid "All Headings (H1 - H6)"
msgstr "所有标题 (H1 - H6)"

#: inc/options/general/colors.php:593
msgid "Borders"
msgstr "边框"

#: inc/options/general/colors.php:563
msgid "Text Selection"
msgstr "选中的文本"

#: inc/options/general/colors.php:533
msgid "Links"
msgstr "链接"

#: inc/options/general/colors.php:512
msgid "Base Text"
msgstr "基础文本"

#: inc/options/general/colors.php:18
msgid "Global Color Palette"
msgstr "全局调色板"

#: inc/options/meta/default.php:75 inc/options/meta/page.php:74
#: inc/options/meta/post.php:70
msgid "Content Area Style Source"
msgstr "内容区样式源"

#: inc/panel-builder/header/cart/options.php:172
msgid "Cart Total Font Color"
msgstr "购物车总额字体颜色"

#: inc/panel-builder/header/cart/options.php:161
msgid "Cart Total Font"
msgstr "购物车总额字体"

#: inc/panel-builder/header/search/options.php:151
#: inc/panel-builder/header/socials/options.php:219
#: inc/panel-builder/header/trigger/options.php:175
msgid "Label Font Color"
msgstr "标签字体颜色"

#: inc/panel-builder/footer/socials/options.php:248
#: inc/panel-builder/header/search/options.php:140
#: inc/panel-builder/header/socials/options.php:208
#: inc/panel-builder/header/trigger/options.php:164
msgid "Label Font"
msgstr "标签字体"

#: inc/panel-builder/header/cart/options.php:114
#: inc/panel-builder/header/search/options.php:93
#: inc/panel-builder/header/trigger/options.php:117
msgid "Label Position"
msgstr "标签位置"

#: inc/options/general/breadcrumbs.php:245
msgid "Breadcrumbs Source"
msgstr "面包屑导航来源"

#: inc/options/general/breadcrumbs.php:43
msgid "Breadcrumb NavXT"
msgstr "Breadcrumb NavXT"

#: inc/options/general/breadcrumbs.php:39
msgid "SeoPress"
msgstr "SeoPress"

#: inc/options/general/breadcrumbs.php:34
msgid "Yoast"
msgstr "Yoast"

#: inc/options/general/breadcrumbs.php:24
msgid "RankMath"
msgstr "排名数学"

#: inc/options/general/back-to-top.php:118
#: inc/panel-builder/header/cart/options.php:57
msgid "Type 6"
msgstr "类型 6"

#: inc/panel-builder/header/search/options.php:440
msgid "Live Results Images"
msgstr "实时结果图像"

#: inc/panel-builder/header/search/options.php:421
msgid "Placeholder Text"
msgstr "占位文本"

#: inc/options/pages/page.php:68
msgid "Page %s"
msgstr "页面 %s"

#: inc/options/posts/custom-post-type-single.php:113
msgid "%s %s"
msgstr "%s %s"

#: inc/options/woocommerce/single-product-gallery.php:38
msgid "Zoom Effect"
msgstr "缩放效果"

#: inc/options/woocommerce/single-product-gallery.php:27
msgid "Lightbox"
msgstr "灯箱"

#: inc/panel-builder/footer/middle-row/options.php:374
msgid "Columns Spacing"
msgstr "列间距"

#: inc/options/woocommerce/single-product-gallery.php:5
msgid "Product Gallery"
msgstr "产品图库"

#: inc/options/meta/default.php:188
msgid "Disable %s %s"
msgstr "禁用 %s %s"

#: inc/options/general/page-title.php:1212
#: inc/panel-builder/footer/options.php:112
#: inc/panel-builder/footer/options.php:129
msgid "Container Padding"
msgstr "容器填充"

#: inc/options/woocommerce/single-product-tabs.php:274
msgid "Active Tab Border"
msgstr "活动选项卡边框"

#: inc/components/woocommerce/single/single-modifications.php:92
#: inc/options/woocommerce/single-product-tabs.php:5
msgid "Product Tabs"
msgstr "产品选项卡"

#: inc/panel-builder/header/cart/options.php:823
msgid "Products Font Color"
msgstr "产品字体颜色"

#: inc/panel-builder/header/cart/options.php:802
#: inc/panel-builder/header/offcanvas/options.php:199
msgid "Panel Heading Font Color"
msgstr "面板标题字体颜色"

#: inc/panel-builder/header/cart/options.php:753
#: inc/panel-builder/header/cart/options.php:864
msgid "Subtotal Font Color"
msgstr "小计字体颜色"

#: inc/panel-builder/header/cart/options.php:902
#: inc/panel-builder/header/offcanvas/options.php:146
msgid "Panel Backdrop"
msgstr "面板背景"

#: inc/options/single-elements/post-share-box.php:283
msgid "Flipboard"
msgstr "Flipboard"

#: inc/options/woocommerce/single-product-elements.php:429
msgid "View Cart Button"
msgstr "查看购物车按钮"

#: inc/options/woocommerce/single-product-elements.php:356
msgid "Add To Cart Button"
msgstr "加入购物车按钮"

#: inc/options/woocommerce/general/quantity-input.php:97
#: inc/options/woocommerce/single-product-elements.php:310
msgid "Quantity Arrows Color"
msgstr "数量箭头颜色"

#: inc/options/woocommerce/general/quantity-input.php:66
msgid "Quantity Main Color"
msgstr "数量主色"

#: inc/options/general/posts-listing.php:1285
msgid "Card Divider"
msgstr "卡片分隔器"

#: inc/panel-builder/header/cart/options.php:666
msgid "Product Page"
msgstr "产品页面"

#: inc/panel-builder/header/cart/options.php:665
msgid "Archive Page"
msgstr "归档页面"

#: inc/panel-builder/header/cart/options.php:659
msgid "Automatically open the cart drawer after a product is added to cart."
msgstr "将产品加入购物车后自动打开购物车抽屉。"

#: inc/panel-builder/header/cart/options.php:652
msgid "Open Cart Automatically On"
msgstr "自动打开购物车"

#: inc/panel-builder/header/menu/options.php:86
msgid "Left to Right"
msgstr "从左到右"

#: inc/panel-builder/header/menu/options.php:85
msgid "Center to Sides"
msgstr "中心到侧面"

#: inc/panel-builder/header/menu/options.php:76
msgid "Indicator Effect"
msgstr "指示器效果"

#: inc/panel-builder/footer/copyright/options.php:67
#: inc/panel-builder/footer/menu/options.php:124
#: inc/panel-builder/footer/socials/options.php:205
#: inc/panel-builder/header/button/options.php:191
#: inc/panel-builder/header/button/options.php:247
#: inc/panel-builder/header/cart/options.php:1117
#: inc/panel-builder/header/logo/options.php:358
#: inc/panel-builder/header/mobile-menu/options.php:161
#: inc/panel-builder/header/search/options.php:855
#: inc/panel-builder/header/socials/options.php:168
#: inc/panel-builder/header/text/options.php:111
#: inc/panel-builder/header/text/options.php:442
#: inc/panel-builder/header/trigger/options.php:620
msgid "Element Visibility"
msgstr "元素可见性"

#: inc/components/woocommerce/common/rest-api.php:139
msgid "Out of Stock"
msgstr "缺货"

#: inc/options/single-elements/author-box.php:88
msgid "Posts Count"
msgstr "文章数"

#: inc/options/woocommerce/single-main.php:6
msgid "Product Title"
msgstr "产品标题"

#: inc/options/woocommerce/general/product-badges.php:219
msgid "Out of Stock Badge"
msgstr "缺货徽章"

#: inc/options/general/pagination.php:92
msgid "Arrows Visibility"
msgstr "箭头可见性"

#: inc/options/general/pagination.php:74
msgid "Numbers Visibility"
msgstr "数字可见性"

#: inc/options/general/page-title.php:657
msgid "Container Bottom Spacing"
msgstr "容器底部间距"

#: inc/options/integrations/tutorlms-archive.php:20
#: inc/options/integrations/tutorlms-single.php:203
msgid "Course Structure"
msgstr "课程结构"

#: inc/options/general/custom-post-types.php:154
msgid "LearnDash"
msgstr "LearnDash"

#: inc/options/general/custom-post-types.php:56
msgid "Tutor LMS"
msgstr "TutorLMS"

#: inc/helpers/cpt.php:11 inc/helpers/cpt.php:18
msgid "Tag"
msgstr "标签"

#: inc/helpers/cpt.php:10 inc/helpers/cpt.php:17
msgid "Category"
msgstr "分类"

#: inc/components/woocommerce/single/additional-actions-layer.php:82
#: inc/panel-builder/footer/socials/options.php:185
#: inc/panel-builder/header/cart/options.php:83
#: inc/panel-builder/header/search/options.php:62
#: inc/panel-builder/header/socials/options.php:140
#: inc/panel-builder/header/trigger/options.php:87
msgid "Label Visibility"
msgstr "标签可见度"

#: inc/integrations/beaver-themer.php:30
msgid "After Footer"
msgstr "页脚之后"

#: inc/integrations/beaver-themer.php:29
msgid "Before Footer"
msgstr "页脚之前"

#: inc/integrations/beaver-themer.php:21
msgid "After Content"
msgstr "内容之后"

#: inc/integrations/beaver-themer.php:20
msgid "Bottom Content"
msgstr "底部内容"

#: inc/integrations/beaver-themer.php:19
msgid "Top Content"
msgstr "顶部内容"

#: inc/integrations/beaver-themer.php:18
msgid "Before Content"
msgstr "内容之前"

#: inc/integrations/beaver-themer.php:16
msgid "Content"
msgstr "内容"

#: inc/integrations/beaver-themer.php:11
msgid "After Header"
msgstr "页眉之后"

#: inc/integrations/beaver-themer.php:10
msgid "Before Header"
msgstr "页眉之前"

#: inc/options/woocommerce/single-product-elements.php:279
msgid "Quantity Color"
msgstr "数量颜色"

#: inc/options/woocommerce/single-product-layers.php:257
msgid "Button Width"
msgstr "按钮宽度"

#: inc/options/woocommerce/card-product-elements.php:469
#: inc/options/woocommerce/single-product-layers.php:242
msgid "Add to Cart"
msgstr "加入购物车"

#: inc/panel-builder/header/cart/options.php:918
#: inc/panel-builder/header/offcanvas/options.php:172
msgid "Panel Shadow"
msgstr "面板阴影"

#: inc/panel-builder/header/cart/options.php:886
#: inc/panel-builder/header/offcanvas/options.php:126
msgid "Panel Background"
msgstr "面板背景"

#: inc/panel-builder/header/mobile-menu/views/offcanvas.php:88
msgid "Off Canvas Menu"
msgstr "关闭画布菜单"

#: inc/panel-builder/header/cart/options.php:623
#: inc/panel-builder/header/offcanvas/options.php:39
msgid "Panel Width"
msgstr "面板宽度"

#: inc/panel-builder/header/cart/options.php:592
msgid "Dropdown"
msgstr "下拉菜单"

#: inc/panel-builder/header/cart/options.php:581
msgid "Cart Drawer Type"
msgstr "购物车抽屉类型"

#: inc/panel-builder/header/cart/options.php:563
msgid "Cart Drawer"
msgstr "购物车抽屉"

#: inc/components/social-box.php:486 inc/components/social-box.php:1520
msgid "Apple Podcasts"
msgstr "苹果播客"

#: inc/options/general/back-to-top.php:169
msgid "Side Offset"
msgstr "侧边偏移"

#: inc/helpers.php:295 inc/helpers.php:311
msgid "Full Size"
msgstr "完整尺寸"

#: inc/panel-builder/header/logo/options.php:107
msgid "Sticky State Shrink"
msgstr "吸附状态收缩"

#: inc/options/general/general.php:35
msgid "Website Frame"
msgstr "网站框架"

#: inc/components/social-box.php:891 inc/components/social-box.php:1616
msgid "Phone"
msgstr "电话"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/header/menu/options.php:19
msgid "Manage your menu items in the %sMenus screen%s."
msgstr "在 %s菜单屏幕%s 中管理您的项目。"

#: inc/options/meta/page.php:192
msgid "Disable Page %s"
msgstr "禁用页面 %s"

#: inc/options/general/typography.php:158
msgid "The font used if the chosen font isn't available."
msgstr "如果所选字体不可用则使用的字体。"

#: inc/options/general/typography.php:157
msgid "Fallback Font Family"
msgstr "后备字体系列"

#: inc/options/single-elements/featured-image.php:114
#: inc/options/single-elements/post-nav.php:108
msgid "Image Visibility"
msgstr "图像可见性"

#: inc/panel-builder/header/menu/options.php:1063
msgid "Dropdown Border Radius"
msgstr "下拉框圆角半径"

#: inc/panel-builder/header/menu/options.php:1045
msgid "Dropdown Shadow"
msgstr "下拉阴影"

#: inc/panel-builder/header/menu/options.php:904
#: inc/panel-builder/header/menu/options.php:934
#: inc/panel-builder/header/menu/options.php:967
#: inc/panel-builder/header/menu/options.php:997
msgid "Items Background Color"
msgstr "项目背景颜色"

#: inc/panel-builder/header/menu/options.php:642
msgid "Boxed Color"
msgstr "盒状颜色"

#: inc/panel-builder/header/menu/options.php:641
msgid "Solid Color"
msgstr "纯色"

#: inc/panel-builder/header/menu/options.php:631
msgid "Items Hover Effect"
msgstr "项目悬停效果"

#: inc/options/single-elements/structure-design.php:104
msgid "Content Area Border Radius"
msgstr "内容区圆角半径"

#: inc/options/single-elements/structure-design.php:118
msgid "Content Area Padding"
msgstr "内容区域填充"

#: inc/options/general/comments-single.php:30
msgid "Website Input Field"
msgstr "网站输入字段"

#: inc/panel-builder/footer/middle-row/options.php:661
#: inc/panel-builder/header/middle-row/options.php:405
msgid "Bottom Border Width"
msgstr "底部边框宽度"

#: inc/panel-builder/footer/middle-row/options.php:620
#: inc/panel-builder/header/middle-row/options.php:298
msgid "Top Border Width"
msgstr "顶部边框宽度"

#: inc/panel-builder/header/middle-row/options.php:79
msgid "Row Max Height"
msgstr "行最大高度"

#: inc/panel-builder/header/middle-row/options.php:62
msgid "Sticky State Row Shrink"
msgstr "吸附状态行收缩"

#: inc/panel-builder/header/middle-row/options.php:52
msgid "Row Min Height"
msgstr "行最小高度"

#: inc/panel-builder/footer/middle-row/options.php:436
#: inc/panel-builder/footer/middle-row/options.php:629
#: inc/panel-builder/footer/middle-row/options.php:670
#: inc/panel-builder/footer/options.php:14
#: inc/panel-builder/header/middle-row/options.php:43
#: inc/panel-builder/header/middle-row/options.php:307
#: inc/panel-builder/header/middle-row/options.php:414
msgid "Full Width"
msgstr "全宽"

#: inc/panel-builder/header/middle-row/config.php:4
#: static/bundle/customizer-controls.js:1 static/bundle/options.js:13
#: static/js/customizer/panels-builder/placements/PlacementsBuilder/Row.js:18
#: static/js/customizer/sync/builder.js:125
msgid "Main Row"
msgstr "主要行"

#: inc/panel-builder/header/logo/options.php:275
msgid "Logo Image Position"
msgstr "标志图像位置"

#: inc/panel-builder/header/logo/options.php:168
msgid "Site Title Visibility"
msgstr "网站标题可见性"

#: inc/panel-builder/header/logo/options.php:87
#: inc/panel-builder/header/logo/options.php:128
#: inc/panel-builder/header/offcanvas-logo/options.php:43
msgid "Logo Height"
msgstr "标志高度"

#: inc/panel-builder/header/logo/options.php:60
msgid "Sticky State Logo"
msgstr "吸附状态标志"

#: inc/panel-builder/header/logo/options.php:27
msgid "Transparent State Logo"
msgstr "透明状态标志"

#: inc/panel-builder/header/button/options.php:348
#: inc/panel-builder/header/button/options.php:548
#: inc/panel-builder/header/button/options.php:734
#: inc/panel-builder/header/cart/options.php:191
#: inc/panel-builder/header/cart/options.php:317
#: inc/panel-builder/header/cart/options.php:442
#: inc/panel-builder/header/logo/options.php:447
#: inc/panel-builder/header/logo/options.php:593
#: inc/panel-builder/header/menu/options.php:194
#: inc/panel-builder/header/menu/options.php:425
#: inc/panel-builder/header/menu/options.php:771
#: inc/panel-builder/header/menu/options.php:924
#: inc/panel-builder/header/middle-row/options.php:163
#: inc/panel-builder/header/middle-row/options.php:231
#: inc/panel-builder/header/middle-row/options.php:338
#: inc/panel-builder/header/middle-row/options.php:444
#: inc/panel-builder/header/mobile-menu/options.php:367
#: inc/panel-builder/header/search/options.php:170
#: inc/panel-builder/header/search/options.php:295
#: inc/panel-builder/header/socials/options.php:242
#: inc/panel-builder/header/socials/options.php:368
#: inc/panel-builder/header/socials/options.php:512
#: inc/panel-builder/header/text/options.php:185
#: inc/panel-builder/header/text/options.php:338
#: inc/panel-builder/header/trigger/options.php:194
#: inc/panel-builder/header/trigger/options.php:320
#: inc/panel-builder/header/trigger/options.php:453
msgid "Sticky State"
msgstr "吸附状态"

#: inc/panel-builder/header/button/options.php:339
#: inc/panel-builder/header/button/options.php:539
#: inc/panel-builder/header/button/options.php:725
#: inc/panel-builder/header/cart/options.php:182
#: inc/panel-builder/header/cart/options.php:308
#: inc/panel-builder/header/cart/options.php:432
#: inc/panel-builder/header/logo/options.php:438
#: inc/panel-builder/header/logo/options.php:584
#: inc/panel-builder/header/menu/options.php:185
#: inc/panel-builder/header/menu/options.php:415
#: inc/panel-builder/header/menu/options.php:762
#: inc/panel-builder/header/menu/options.php:915
#: inc/panel-builder/header/middle-row/options.php:155
#: inc/panel-builder/header/middle-row/options.php:223
#: inc/panel-builder/header/middle-row/options.php:330
#: inc/panel-builder/header/middle-row/options.php:436
#: inc/panel-builder/header/mobile-menu/options.php:358
#: inc/panel-builder/header/search/options.php:161
#: inc/panel-builder/header/search/options.php:287
#: inc/panel-builder/header/socials/options.php:232
#: inc/panel-builder/header/socials/options.php:358
#: inc/panel-builder/header/socials/options.php:501
#: inc/panel-builder/header/text/options.php:176
#: inc/panel-builder/header/text/options.php:329
#: inc/panel-builder/header/trigger/options.php:185
#: inc/panel-builder/header/trigger/options.php:311
#: inc/panel-builder/header/trigger/options.php:443
msgid "Transparent State"
msgstr "透明状态"

#: inc/panel-builder/header/button/options.php:334
#: inc/panel-builder/header/button/options.php:534
#: inc/panel-builder/header/button/options.php:720
#: inc/panel-builder/header/cart/options.php:177
#: inc/panel-builder/header/cart/options.php:303
#: inc/panel-builder/header/cart/options.php:424
#: inc/panel-builder/header/logo/options.php:433
#: inc/panel-builder/header/logo/options.php:579
#: inc/panel-builder/header/menu/options.php:180
#: inc/panel-builder/header/menu/options.php:409
#: inc/panel-builder/header/menu/options.php:757
#: inc/panel-builder/header/menu/options.php:910
#: inc/panel-builder/header/middle-row/options.php:150
#: inc/panel-builder/header/middle-row/options.php:218
#: inc/panel-builder/header/middle-row/options.php:325
#: inc/panel-builder/header/middle-row/options.php:431
#: inc/panel-builder/header/mobile-menu/options.php:353
#: inc/panel-builder/header/search/options.php:156
#: inc/panel-builder/header/search/options.php:282
#: inc/panel-builder/header/socials/options.php:224
#: inc/panel-builder/header/socials/options.php:352
#: inc/panel-builder/header/socials/options.php:492
#: inc/panel-builder/header/text/options.php:171
#: inc/panel-builder/header/text/options.php:324
#: inc/panel-builder/header/trigger/options.php:180
#: inc/panel-builder/header/trigger/options.php:306
#: inc/panel-builder/header/trigger/options.php:437
msgid "Default State"
msgstr "默认状态"

#: inc/options/general/page-title.php:802
msgid "Choose for which devices you want to enable the parallax effect."
msgstr "选择您想要启用视差效果的设备。"

#: inc/options/general/page-title.php:749
msgid "Featured"
msgstr "特色"

#: inc/options/meta/blog.php:12 inc/options/meta/default.php:234
#: inc/options/meta/page.php:208 inc/options/meta/post.php:230
msgid "Disable Footer"
msgstr "禁用页脚"

#: inc/options/meta/blog.php:6 inc/options/meta/default.php:228
#: inc/options/meta/page.php:202 inc/options/meta/post.php:224
msgid "Disable Header"
msgstr "禁用页眉"

#: inc/classes/db-versioning/v2-0-31.php:26
#: inc/classes/db-versioning/v2-0-31.php:103 inc/components/social-box.php:84
#: inc/options/single-elements/post-share-box.php:133
msgid "Share your love"
msgstr "分享你的喜爱"

#: inc/options/woocommerce/general/product-badges.php:84
msgid "Sale Badge Value"
msgstr "销售徽章的值"

#: inc/options/customizer.php:169
msgid "Product Archives"
msgstr "产品归档"

#: inc/options/woocommerce/card-product-elements.php:286
msgid "Swap Images"
msgstr "交换图片"

#: inc/options/woocommerce/general/store-notice.php:83
msgid "Notice Background Color"
msgstr "通知背景色"

#: inc/options/woocommerce/general/store-notice.php:61
msgid "Notice Font Color"
msgstr "通知字体颜色"

#: inc/options/woocommerce/general/store-notice.php:41
msgid "Notice Position"
msgstr "通知位置"

#: admin/dashboard/plugins/config.php:45
msgid "The most advanced frontend drag & drop page builder. Create high-end, pixel perfect websites at record speeds. Any theme, any page, any design."
msgstr "最先进的前端拖放页面生成器。以创纪录的速度创建高端, 像素完美的网站。任何主题, 任何页面, 任何设计。"

#: admin/dashboard/plugins/config.php:57
msgid "Drag & Drop online form builder that helps you create beautiful contact forms with just a few clicks."
msgstr "拖放在线表单生成器, 帮助您创建漂亮的联系表单, 只需几次点击。"

#: admin/dashboard/plugins/config.php:51
msgid "Capture, organize and engage web visitors with free forms, live chat, CRM (contact management), email marketing, and analytics."
msgstr "利用免费的表单, 实时聊天, CRM(联系人管理), 电子邮件营销和分析, 捕捉, 组织和吸引网络访问者。"

#: admin/dashboard/plugins/config.php:8
msgid "A Library of Page Builder Gutenberg Blocks which will reimagine the way you use the WordPress Block Editor."
msgstr "网页生成器古腾堡块的一个库, 它将重新想象你使用 WordPress 块编辑器的方式。"

#: inc/components/blocks/blocks-fallback.php:41
#: inc/panel-builder/footer/socials/config.php:4
#: inc/panel-builder/header/socials/config.php:4
msgid "Socials"
msgstr "社交活动"

#: inc/options/posts/custom-post-type-single.php:193
msgid "%s Elements"
msgstr "%s 元素"

#. translators: placeholder here means the actual URL.
#: inc/options/general/posts-listing.php:1668 inc/options/meta/default.php:147
#: inc/options/meta/page.php:146 inc/options/meta/post.php:145
#: inc/options/single-elements/structure.php:118
#: inc/options/woocommerce/archive-main.php:152
msgid "You can customize the global spacing value in General ➝ Layout ➝ %sContent Area Spacing%s."
msgstr "你可以定制全局间距值在 常规 ➝ 布局 ➝%s内容区间距%s。"

#: inc/options/general/posts-listing.php:1661 inc/options/meta/default.php:140
#: inc/options/meta/page.php:139 inc/options/meta/post.php:138
#: inc/options/single-elements/structure.php:111
#: inc/options/woocommerce/archive-main.php:145
msgid "Only Bottom"
msgstr "只在底部"

#: inc/options/general/posts-listing.php:1658 inc/options/meta/default.php:137
#: inc/options/meta/page.php:136 inc/options/meta/post.php:135
#: inc/options/single-elements/structure.php:108
#: inc/options/woocommerce/archive-main.php:142
msgid "Only Top"
msgstr "只在顶部"

#: inc/options/general/posts-listing.php:1655 inc/options/meta/default.php:134
#: inc/options/meta/page.php:133 inc/options/meta/post.php:132
#: inc/options/single-elements/structure.php:105
#: inc/options/woocommerce/archive-main.php:139
msgid "Top & Bottom"
msgstr "顶部和底部"

#: inc/options/meta/default.php:123 inc/options/meta/page.php:122
#: inc/options/meta/post.php:121
msgid "You can customize the spacing value in general settings panel."
msgstr "您可以自定义间距值在常规设置面板。"

#: inc/options/general/posts-listing.php:1644 inc/options/meta/default.php:105
#: inc/options/meta/page.php:104 inc/options/meta/post.php:104
#: inc/options/single-elements/structure.php:94
#: inc/options/woocommerce/archive-main.php:128
msgid "Content Area Vertical Spacing"
msgstr "内容区垂直间距"

#: inc/options/general/breadcrumbs.php:101
msgid "Home Page Text"
msgstr "主页文字"

#: inc/options/general/back-to-top.php:55
#: inc/options/general/breadcrumbs.php:88
#: inc/options/woocommerce/single-product-layers.php:72
#: inc/options/woocommerce/single-product-layers.php:117
msgid "Icon"
msgstr "图标"

#: inc/options/general/breadcrumbs.php:82
msgid "Home Item"
msgstr "首页项目"

#: inc/options/general/breadcrumbs.php:53
msgid "Separator"
msgstr "分隔器"

#: inc/options/general/page-title.php:742
msgid "Container Background Image"
msgstr "容器背景图片"

#: inc/options/general/meta.php:349
msgid "Taxonomies"
msgstr "分类"

#: woocommerce/cart/cart.php:252
msgid "Update cart"
msgstr "更新购物车"

#: woocommerce/cart/cart.php:247
msgid "Apply coupon"
msgstr "应用优惠"

#: woocommerce/cart/cart.php:247
msgid "Coupon code"
msgstr "优惠码"

#: woocommerce/cart/cart.php:247
msgid "Coupon:"
msgstr "优惠:"

#: woocommerce/cart/cart.php:109
msgid "Available on backorder"
msgstr "可缺货"

#: woocommerce/cart/cart.php:216
msgid "Remove product"
msgstr "移除产品"

#: inc/options/woocommerce/card-product-elements.php:369
#: inc/options/woocommerce/single-product-layers.php:184
msgid "Price"
msgstr "价格"

#: woocommerce/cart/cart.php:40 woocommerce/cart/cart.php:89
msgid "Product"
msgstr "产品"

#: inc/options/single-elements/featured-image.php:81
msgid "Full"
msgstr "完整"

#: inc/helpers.php:293
msgid "Medium Large"
msgstr "中大型"

#: inc/options/single-elements/post-share-box.php:488
#: inc/options/single-elements/post-tags.php:125
#: inc/options/single-elements/related-posts.php:499
msgid "Module Title Font Color"
msgstr "模块标题字体颜色"

#: inc/options/single-elements/related-posts.php:403
msgid "Location"
msgstr "位置"

#: inc/options/single-elements/post-share-box.php:144
#: inc/options/single-elements/post-tags.php:46
#: inc/options/single-elements/related-posts.php:173
msgid "Module Title Tag"
msgstr "模块标题标签"

#: inc/options/single-elements/post-share-box.php:131
#: inc/options/single-elements/post-tags.php:34
#: inc/options/single-elements/related-posts.php:165
msgid "Module Title"
msgstr "模块名称"

#. translators: %s: Author's display name.
#: inc/components/post-meta.php:72
msgid "Posts by %s"
msgstr "由 %s 发布"

#: inc/options/integrations/the-events-calendar/archive.php:14
#: inc/options/integrations/the-events-calendar/single.php:15
#: inc/options/integrations/tutorlms-archive.php:12
#: inc/options/integrations/tutorlms-single.php:195
#: inc/options/posts/custom-post-type-archive.php:8
#: inc/options/posts/custom-post-type-single.php:21
msgid "%s Title"
msgstr "%s 标题"

#: inc/options/woocommerce/archive-main.php:82
#: inc/options/woocommerce/related-upsells.php:33
msgid "Columns & Rows"
msgstr "列和行"

#: inc/options/woocommerce/archive-main.php:44
msgid "Shop Settings"
msgstr "商店设置"

#: inc/options/general/custom-post-types.php:124
msgid "Single %s"
msgstr "%s 详情页"

#: inc/options/general/custom-post-types.php:38
#: inc/options/general/custom-post-types.php:44
msgid "BuddyPress"
msgstr "BuddyPress"

#: inc/options/general/custom-post-types.php:15
#: inc/options/general/custom-post-types.php:21
msgid "bbPress"
msgstr "bbPress"

#: inc/options/general/meta.php:453 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13 static/js/options/options/ct-border.js:47
msgid "none"
msgstr "无"

#: inc/options/general/meta.php:440
msgid "Icons"
msgstr "图标"

#: inc/options/general/meta.php:439
msgid "Labels"
msgstr "标签"

#: inc/options/general/meta.php:433
msgid "Items Style"
msgstr "项目样式"

#: inc/components/post-meta.php:207 inc/components/post-meta.php:793
#: inc/options/general/meta.php:388
#: static/js/customizer/sync/helpers/entry-meta.js:107
msgid "In"
msgstr "在"

#: inc/options/general/meta.php:205
msgid "Author Avatar"
msgstr "作者头像"

#. translators: The interpolations addes a html link around the word.
#: inc/options/general/meta.php:181
msgid "Date format %sinstructions%s."
msgstr "日期格式%s说明%s。"

#. translators: The interpolations addes a html link around the word.
#: inc/options/general/colors.php:23
msgid "Learn more about palettes and colors %shere%s."
msgstr "%s在这里%s了解更多关于调色板和颜色。"

#: inc/components/breadcrumbs.php:293
msgid "No title"
msgstr "无标题"

#: inc/components/breadcrumbs.php:72
msgid "Searching for:"
msgstr "搜索:"

#: inc/components/breadcrumbs.php:64
msgid "404 Not found"
msgstr "404 未找到"

#: inc/classes/screen-manager.php:208 inc/components/breadcrumbs.php:409
#: inc/options/general/posts-listing.php:14 inc/options/posts/blog.php:19
msgid "Blog"
msgstr "博客"

#. translators: placeholder here means the actual URL.
#: inc/options/general/posts-listing.php:1701
#: inc/options/single-elements/structure-design.php:36
#: inc/options/woocommerce/archive-main.php:184
msgid "Please note, by default this option is inherited from Colors ➝ %sSite Background%s."
msgstr "请注意, 默认情况下这个选项是继承自 颜色 ➝ %s站点背景%s。"

#: inc/options/general/breadcrumbs.php:193
#: inc/options/general/page-title.php:1115
#: inc/options/woocommerce/single-product-elements.php:229
msgid "Breadcrumbs Font Color"
msgstr "面包屑导航字体颜色"

#: inc/options/general/breadcrumbs.php:181
#: inc/options/general/page-title.php:1107
#: inc/options/woocommerce/single-product-elements.php:221
msgid "Breadcrumbs Font"
msgstr "面包屑导航字体"

#: inc/options/general/page-title.php:591
msgid "Social Channels"
msgstr "社交频道"

#: inc/options/general/page-title.php:528
msgid "Author Meta"
msgstr "作者元数据"

#: inc/options/general/page-title.php:327
msgid "Name & Avatar"
msgstr "名字与头像"

#: inc/options/general/page-title.php:287
#: inc/options/general/page-title.php:424
#: inc/options/general/page-title.php:489
#: inc/options/general/page-title.php:575
#: inc/options/general/page-title.php:598
msgid "Top Spacing"
msgstr "顶部间距"

#: inc/options/general/page-title.php:110
msgid "Subtitle"
msgstr "副标题"

#: inc/options/general/page-title.php:98
msgid "Bio"
msgstr "个人简历"

#: inc/options/general/page-title.php:788
#: inc/options/general/posts-listing.php:327
#: inc/options/single-elements/featured-image.php:36
#: inc/options/single-elements/post-nav.php:89
#: inc/options/single-elements/related-posts.php:248
#: inc/options/woocommerce/card-product-elements.php:258
#: inc/options/woocommerce/general/cart-page.php:40
#: inc/options/woocommerce/single-product-gallery.php:113
#: inc/options/woocommerce/single-product-gallery.php:338
#: inc/panel-builder/header/cart/options.php:690
msgid "Image Size"
msgstr "图像大小"

#: inc/options/woocommerce/card-product-elements.php:165
#: inc/options/woocommerce/card-product-elements.php:266
msgid "Image height will be automatically calculated based on the image ratio."
msgstr "图像高度将根据图像比率自动计算。"

#: inc/helpers.php:291
msgid "Thumbnail"
msgstr "缩略图"

#: inc/components/woocommerce/common/account.php:98
msgid "Account"
msgstr "账户"

#: inc/panel-builder/header/search/options.php:398
msgid "Icon Margin"
msgstr "图标边距"

#: inc/components/woocommerce/boot.php:97
msgid "WooCommerce Sidebar"
msgstr "WooCommerce 侧边栏"

#: inc/panel-builder/header/trigger/options.php:464
#: inc/panel-builder/header/trigger/options.php:504
#: inc/panel-builder/header/trigger/options.php:542
msgid "Trigger Border Color"
msgstr "触发器边框颜色"

#: inc/panel-builder/header/button/options.php:46
#: inc/panel-builder/header/button/options.php:66
#: inc/panel-builder/header/cart/options.php:130
#: inc/panel-builder/header/search/options.php:107
#: inc/panel-builder/header/trigger/options.php:131
msgid "Label Text"
msgstr "标签文本"

#: inc/options/woocommerce/related-upsells.php:5
msgid "Related & Upsells"
msgstr "相关与追加销售"

#: inc/options/woocommerce/general/star-rating.php:42
msgid "Inactive"
msgstr "未活动"

#: inc/options/woocommerce/card-product-elements.php:937
#: inc/options/woocommerce/card-product-elements.php:1012
msgid "Button Text Color"
msgstr "按钮文本颜色"

#: inc/options/general/posts-listing.php:1590
#: inc/options/single-elements/related-posts.php:69
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-woocommerce-columns-and-rows.js:51
msgid "Number of columns"
msgstr "列数"

#: inc/options/single-elements/related-posts.php:373
msgid "Separate or unify the related posts module from or with the entry content area."
msgstr "将相关的文章模块与入口内容区域分开或统一。"

#: inc/options/general/content-elements.php:28
msgid "Spacious"
msgstr "宽敞"

#: inc/options/general/content-elements.php:27
msgid "Comfortable"
msgstr "舒适"

#: inc/options/general/content-elements.php:26
msgid "Compact"
msgstr "紧凑"

#: inc/options/general/content-elements.php:19
msgid "Content Spacing"
msgstr "内容间隔"

#: inc/options/general/comments-single.php:85
#: inc/options/single-elements/related-posts.php:376
msgid "Contained"
msgstr "包含"

#: inc/options/general/comments-single.php:84
#: inc/options/single-elements/related-posts.php:375
msgid "Separated"
msgstr "分开"

#: inc/options/general/comments-single.php:82
msgid "Separate or unify the comments module from or with the entry content area."
msgstr "将评论模块与条目内容区域分开或统一。"

#: inc/options/general/comments-single.php:77
#: inc/options/single-elements/related-posts.php:368
#: inc/options/woocommerce/single-product-tabs.php:85
msgid "Module Placement"
msgstr "模块位置"

#: inc/options/general/breadcrumbs.php:226
#: inc/options/general/page-title.php:1148 inc/options/general/sidebar.php:371
#: inc/options/single-elements/author-box.php:334
#: inc/options/woocommerce/single-product-elements.php:262
#: inc/panel-builder/footer/copyright/options.php:139
#: inc/panel-builder/footer/middle-row/options.php:573
#: inc/panel-builder/footer/widget-area-1/options.php:94
#: inc/panel-builder/header/cart/options.php:745
#: inc/panel-builder/header/cart/options.php:856
#: inc/panel-builder/header/text/options.php:229
#: inc/panel-builder/header/text/options.php:269
#: inc/panel-builder/header/text/options.php:308
msgid "Link Hover"
msgstr "链接悬停"

#: inc/options/general/breadcrumbs.php:220
#: inc/options/general/page-title.php:1142 inc/options/general/sidebar.php:366
#: inc/options/single-elements/author-box.php:328
#: inc/options/woocommerce/single-product-elements.php:256
#: inc/panel-builder/footer/copyright/options.php:133
#: inc/panel-builder/footer/middle-row/options.php:567
#: inc/panel-builder/footer/widget-area-1/options.php:89
#: inc/panel-builder/header/cart/options.php:739
#: inc/panel-builder/header/cart/options.php:851
#: inc/panel-builder/header/text/options.php:223
#: inc/panel-builder/header/text/options.php:264
#: inc/panel-builder/header/text/options.php:303
msgid "Link Initial"
msgstr "链接初始"

#: inc/components/social-box.php:642 inc/components/social-box.php:1556
msgid "Facebook Group"
msgstr "Facebook 群组"

#: inc/options/general/page-title.php:543
msgid "Joined Date"
msgstr "加入日期"

#: inc/init.php:478
msgid "Main Sidebar"
msgstr "主侧边栏"

#: inc/components/single/comments.php:255
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/sync/builder.js:110
#: static/js/options/options/ct-image-uploader.js:385
msgid "Edit"
msgstr "编辑"

#: inc/options/general/page-title.php:1345
msgid "Options will appear here only if you will set Custom in Page Title option."
msgstr "选项将出现在这里, 只有你将设置自定义页标题选项。"

#: inc/options/general/page-title.php:1285
msgid "By default these options are inherited from Customizer options."
msgstr "默认情况下, 这些选项是从定制程序选项继承的。"

#: inc/options/general/comments-single.php:126
#: inc/options/single-elements/related-posts.php:453
msgid "Container Max Width"
msgstr "容器最大宽度"

#: inc/options/general/comments-single.php:110
#: inc/options/single-elements/related-posts.php:436
msgid "Normal"
msgstr "常规"

#: inc/options/general/comments-single.php:109
#: inc/options/general/page-title.php:733
#: inc/options/single-elements/related-posts.php:437
msgid "Narrow"
msgstr "更窄"

#: inc/options/general/comments-single.php:103
#: inc/options/single-elements/related-posts.php:429
#: inc/panel-builder/footer/options.php:6
#: inc/panel-builder/header/middle-row/options.php:32
msgid "Container Structure"
msgstr "容器结构"

#: inc/panel-builder/footer/options.php:38
msgid "Enable reveal effect on"
msgstr "启用显示效果在"

#: inc/panel-builder/footer/socials/options.php:121
#: inc/panel-builder/header/socials/options.php:120
msgid "Shape Fill Type"
msgstr "形状填充类型"

#: inc/panel-builder/footer/socials/options.php:102
#: inc/panel-builder/header/socials/options.php:101
msgid "Icons Shape Type"
msgstr "图标形状类型"

#: inc/options/general/page-title.php:694
#: inc/options/general/posts-listing.php:792
#: inc/panel-builder/footer/copyright/options.php:47
#: inc/panel-builder/footer/menu/options.php:105
#: inc/panel-builder/footer/middle-row/options.php:411
#: inc/panel-builder/footer/socials/options.php:169
#: inc/panel-builder/footer/widget-area-1/options.php:39
#: inc/panel-builder/header/button/options.php:231
#: inc/panel-builder/header/logo/options.php:342
#: inc/panel-builder/header/offcanvas/options.php:63
#: inc/panel-builder/header/text/options.php:95
msgid "Vertical Alignment"
msgstr "垂直对齐方式"

#: inc/options/general/page-title.php:638
#: inc/options/general/page-title.php:678
#: inc/options/general/posts-listing.php:739
#: inc/options/woocommerce/single-product-tabs.php:60
#: inc/panel-builder/footer/copyright/options.php:31
#: inc/panel-builder/footer/menu/options.php:84
#: inc/panel-builder/footer/socials/options.php:152
#: inc/panel-builder/footer/widget-area-1/options.php:23
#: inc/panel-builder/header/button/options.php:214
#: inc/panel-builder/header/logo/options.php:326
#: inc/panel-builder/header/mobile-menu/options.php:143
#: inc/panel-builder/header/offcanvas/options.php:79
#: inc/panel-builder/header/text/options.php:78
msgid "Horizontal Alignment"
msgstr "水平对齐方式"

#: inc/integrations/elementor.php:131
msgid "Full Width Section"
msgstr "全宽章节"

#: inc/options/general/sidebar.php:118
msgid "Widget Title Tag"
msgstr "小工具标题标签"

#: inc/options/meta/default.php:222 inc/options/meta/post.php:218
msgid "Disable Related Posts"
msgstr "禁用相关文章"

#: inc/options/meta/default.php:211 inc/options/meta/post.php:200
msgid "Disable Posts Navigation"
msgstr "禁用文章导航"

#: inc/options/meta/default.php:205 inc/options/meta/post.php:194
msgid "Disable Author Box"
msgstr "禁用作者框"

#: inc/options/meta/default.php:199 inc/options/meta/page.php:183
#: inc/options/meta/post.php:188
msgid "Disable Share Box"
msgstr "禁用分享框"

#: inc/options/meta/post.php:182
msgid "Disable Post Tags"
msgstr "禁用文章标签"

#: inc/options/meta/default.php:179 inc/options/meta/page.php:177
#: inc/options/meta/post.php:176
msgid "Disable Featured Image"
msgstr "禁用特色图片"

#: inc/options/general/sidebar.php:338
#: inc/panel-builder/footer/middle-row/options.php:539
msgid "Widgets Font Color"
msgstr "小工具字体颜色"

#: inc/options/general/sidebar.php:329
#: inc/panel-builder/footer/middle-row/options.php:530
msgid "Widgets Font"
msgstr "小工具字体"

#: inc/options/general/page-title.php:388
msgid "Author avatar"
msgstr "作者头像"

#: inc/options/general/page-title.php:221
#: static/bundle/customizer-controls.js:1
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/panels-builder/columns/AvailableItems.js:74
#: static/js/customizer/panels-builder/placements/AvailableItems.js:78
msgid "Elements"
msgstr "元素"

#: inc/panel-builder/footer/copyright/options.php:16
msgid "You can insert some arbitrary HTML code tags: {current_year}, {site_title} and {theme_author}"
msgstr "你可以插入一些任意的HTML代码标签: {current_year}, {site_title} 和 {theme_author}"

#: inc/panel-builder/footer/copyright/options.php:10
msgid "Copyright Text"
msgstr "版权文本"

#: inc/panel-builder/footer/copyright/config.php:3
msgid "Copyright"
msgstr "版权"

#: inc/panel-builder/footer/options.php:103
msgid "Please note, you can also change the background color for each row individually."
msgstr "请注意, 您还可以单独更改每一行的背景颜色。"

#: inc/panel-builder/footer/options.php:43
msgid "Enables a nice reveal effect as you scroll down."
msgstr "向下滚动时启用良好的显示效果。"

#: inc/options/general/sidebar.php:282
#: inc/panel-builder/footer/middle-row/options.php:482
msgid "Widgets Title Font Color"
msgstr "小工具标题字体颜色"

#: inc/options/general/sidebar.php:274
#: inc/panel-builder/footer/middle-row/options.php:474
msgid "Widgets Title Font"
msgstr "小工具标题字体"

#: inc/panel-builder/footer/middle-row/options.php:641
msgid "Row Bottom Divider"
msgstr "行底部分隔器"

#: inc/panel-builder/footer/middle-row/options.php:600
msgid "Row Top Divider"
msgstr "行顶部分隔器"

#: inc/panel-builder/footer/middle-row/options.php:587
msgid "Row Background"
msgstr "行背景"

#: inc/panel-builder/footer/middle-row/options.php:441
#: inc/panel-builder/header/middle-row/options.php:117
msgid "Row Visibility"
msgstr "行可见性"

#: inc/panel-builder/footer/middle-row/options.php:399
msgid "Row Vertical Spacing"
msgstr "行垂直间距"

#: inc/panel-builder/footer/middle-row/options.php:92
#: inc/panel-builder/footer/middle-row/options.php:104
#: inc/panel-builder/footer/middle-row/options.php:155
#: inc/panel-builder/footer/middle-row/options.php:167
#: inc/panel-builder/footer/middle-row/options.php:218
#: inc/panel-builder/footer/middle-row/options.php:230
#: inc/panel-builder/footer/middle-row/options.php:281
#: inc/panel-builder/footer/middle-row/options.php:293
#: inc/panel-builder/footer/middle-row/options.php:344
#: inc/panel-builder/footer/middle-row/options.php:356
msgid "Two Columns"
msgstr "两栏"

#: inc/panel-builder/footer/middle-row/options.php:87
#: inc/panel-builder/footer/middle-row/options.php:99
#: inc/panel-builder/footer/middle-row/options.php:150
#: inc/panel-builder/footer/middle-row/options.php:162
#: inc/panel-builder/footer/middle-row/options.php:213
#: inc/panel-builder/footer/middle-row/options.php:225
#: inc/panel-builder/footer/middle-row/options.php:276
#: inc/panel-builder/footer/middle-row/options.php:288
#: inc/panel-builder/footer/middle-row/options.php:339
#: inc/panel-builder/footer/middle-row/options.php:351
msgid "Stacked"
msgstr "堆叠"

#: inc/panel-builder/footer/middle-row/options.php:51
#: inc/panel-builder/footer/middle-row/options.php:118
#: inc/panel-builder/footer/middle-row/options.php:181
#: inc/panel-builder/footer/middle-row/options.php:244
#: inc/panel-builder/footer/middle-row/options.php:307
msgid "Columns Layout"
msgstr "列布局"

#: inc/options/general/general.php:53
msgid "Frame Color"
msgstr "框架颜色"

#: inc/options/general/general.php:43
msgid "Frame Size"
msgstr "框架大小"

#: inc/options/general/layout.php:84
msgid "This option will apply only to those elements that have a wide alignment option."
msgstr "此选项将仅适用于具有宽对齐选项的元素。"

#: inc/options/woocommerce/card-product-elements.php:81
#: inc/options/woocommerce/card-product-elements.php:92
#: inc/options/woocommerce/card-product-elements.php:128
#: inc/options/woocommerce/card-product-elements.php:139
#: inc/panel-builder/header/menu/options.php:958
#: inc/panel-builder/header/menu/options.php:989
#: inc/panel-builder/header/menu/options.php:1019
msgid "Hover/Active"
msgstr "悬停/活动"

#: inc/options/general/buttons.php:13 inc/options/general/typography.php:104
msgid "Buttons"
msgstr "按钮"

#: inc/components/single/single-helpers.php:380
#: inc/components/single/single-helpers.php:516
#: inc/options/general/page-title.php:544
msgid "Articles"
msgstr "文章"

#: inc/components/single/single-helpers.php:376
msgid "Joined"
msgstr "已加入"

#: inc/init.php:415
msgid "Header Menu 1"
msgstr "页眉菜单1"

#: inc/options/general/posts-listing.php:1257
msgid "Card Bottom Divider"
msgstr "卡片底部分隔器"

#: inc/options/customizer.php:91
msgid "Performance"
msgstr "性能"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/footer/menu/options.php:19
#: inc/panel-builder/header/mobile-menu/options.php:15
msgid "Manage your menus in the %sMenus screen%s."
msgstr "在 %s菜单屏幕%s 中管理您的菜单。"

#: inc/components/media/simple.php:48
msgid "Default image"
msgstr "默认图片"

#: inc/components/gallery.php:276 inc/components/gallery.php:288
msgid "Slide %s"
msgstr "幻灯片 %s"

#: inc/components/pagination.php:68
msgid "No more products to load"
msgstr "没有更多的产品加载"

#: inc/panel-builder/header/search/options.php:475
msgid "Search Through Criteria"
msgstr "搜索标准"

#: inc/options/woocommerce/card-product-elements.php:970
#: inc/options/woocommerce/general/messages.php:103
#: inc/options/woocommerce/general/messages.php:219
#: inc/options/woocommerce/general/messages.php:331
#: inc/options/woocommerce/single-product-elements.php:391
#: inc/options/woocommerce/single-product-elements.php:464
msgid "Button Background Color"
msgstr "按钮背景颜色"

#: inc/options/general/back-to-top.php:113
#: inc/options/general/content-elements.php:51
#: inc/panel-builder/header/cart/options.php:52
msgid "Type 5"
msgstr "类型 5"

#: inc/options/general/content-elements.php:39
msgid "Links Type"
msgstr "链接类型"

#: inc/options/general/posts-listing.php:1249
msgid "Featured Image Radius"
msgstr "特色图片圆角半径"

#: inc/options/single-elements/related-posts.php:654
#: inc/options/woocommerce/card-product-elements.php:1100
msgid "Image Border Radius"
msgstr "图片圆角半径"

#: inc/options/woocommerce/general/store-notice.php:31
msgid "This is a demo store for testing purposes &mdash; no orders shall be fulfilled."
msgstr "这是一个用于测试的演示商店 - 没有订单能完成哦。"

#: inc/options/woocommerce/general/store-notice.php:13
msgid "Store Notice"
msgstr "商店通告"

#: inc/options/woocommerce/card-product-elements.php:211
msgid "Height"
msgstr "高度"

#: inc/options/woocommerce/card-product-elements.php:185
#: inc/options/woocommerce/card-product-elements.php:198
#: inc/panel-builder/header/menu/options.php:675
msgid "Width"
msgstr "宽"

#: inc/options/posts/categories.php:71
msgid "Categories Elements"
msgstr "分类元素"

#: inc/options/pages/search-page.php:89 inc/options/posts/blog.php:98
#: inc/options/posts/custom-post-type-archive.php:94
#: inc/options/woocommerce/archive-main.php:244
#: inc/options/woocommerce/single-main.php:99
msgid "Functionality Options"
msgstr "功能选项"

#: inc/options/woocommerce/single-product-elements.php:9
msgid "Product Elements"
msgstr "产品元素"

#: inc/options/single-elements/related-posts.php:556
msgid "Posts Title Font Color"
msgstr "文章标题字体颜色"

#: inc/options/general/posts-listing.php:408
msgid "Boundless Image"
msgstr "无限图像"

#: inc/options/single-elements/structure-design.php:69
msgid "Content Area Shadow"
msgstr "内容区域阴影"

#: inc/options/general/pagination.php:178
msgid "Accent"
msgstr "强调"

#: inc/options/general/pagination.php:171
msgid "Text Active"
msgstr "文本活动"

#: inc/options/general/pagination.php:165 inc/options/general/sidebar.php:360
#: inc/panel-builder/footer/middle-row/options.php:561
#: inc/panel-builder/header/cart/options.php:734
#: inc/panel-builder/header/cart/options.php:815
#: inc/panel-builder/header/cart/options.php:845
#: inc/panel-builder/header/cart/options.php:878
#: inc/panel-builder/header/offcanvas/options.php:213
#: inc/panel-builder/header/text/options.php:217
#: inc/panel-builder/header/text/options.php:259
#: inc/panel-builder/header/text/options.php:298
msgid "Text Initial"
msgstr "文本首字母"

#: inc/options/woocommerce/general/messages.php:24
#: inc/options/woocommerce/general/messages.php:138
#: inc/options/woocommerce/general/messages.php:254
msgid "Text Color"
msgstr "文本颜色"

#: inc/options/general/page-title.php:1073
msgid "Excerpt Font Color"
msgstr "摘要字体颜色"

#: inc/options/general/page-title.php:1061
#: inc/options/general/posts-listing.php:908
msgid "Excerpt Font"
msgstr "摘要字体"

#: inc/options/general/page-title.php:944
#: inc/options/general/posts-listing.php:957
msgid "Meta Font Color"
msgstr "元信息字体颜色"

#: inc/panel-builder/header/menu/options.php:717
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/box-shadow/box-shadow-modal.js:111
msgid "Horizontal Offset"
msgstr "水平偏移"

#: inc/options/general/buttons.php:110 inc/options/general/sidebar.php:413
#: inc/options/single-elements/author-box.php:492
#: inc/options/single-elements/post-share-box.php:580
#: inc/options/woocommerce/single-product-tabs.php:324
#: inc/panel-builder/header/cart/options.php:947
#: inc/panel-builder/header/mobile-menu/options.php:85
#: inc/panel-builder/header/offcanvas/options.php:233
#: inc/panel-builder/header/search/options.php:696
msgid "Border"
msgstr "边界"

#: inc/panel-builder/header/menu/options.php:685
msgid "Top Offset"
msgstr "顶部偏移"

#: inc/options/general/posts-listing.php:1416
#: inc/options/woocommerce/card-product-elements.php:1073
msgid "Card Shadow"
msgstr "卡片阴影"

#: inc/options/general/back-to-top.php:158
msgid "Bottom Offset"
msgstr "底部偏移"

#. translators: % refers to the number of reviews, when more than 1
#: inc/components/post-meta.php:505
msgid "% Reviews"
msgstr "% 评论"

#. translators: text for one review
#: inc/components/post-meta.php:503
msgid "1 Review"
msgstr "1 评价"

#. translators: text for one review
#: inc/components/post-meta.php:497
msgid "1 Comment"
msgstr "一条评论"

#: inc/manager.php:233
msgid "More"
msgstr "更多"

#: inc/options/general/back-to-top.php:306 inc/options/general/sidebar.php:460
#: inc/options/single-elements/author-box.php:473
#: inc/panel-builder/footer/options.php:65
#: inc/panel-builder/header/middle-row/options.php:426
#: inc/panel-builder/header/middle-row/options.php:453
#: inc/panel-builder/header/middle-row/options.php:471
#: inc/panel-builder/header/middle-row/options.php:489
msgid "Shadow"
msgstr "阴影"

#: inc/components/single/comments.php:263
msgid "Reply"
msgstr "回复"

#: inc/init.php:417 inc/panel-builder/header/mobile-menu/config.php:4
#: inc/panel-builder/header/mobile-menu/views/inline.php:62
msgid "Mobile Menu"
msgstr "移动设备菜单"

#: inc/panel-builder/header/cart/config.php:4
msgid "Cart"
msgstr "购物车"

#: inc/panel-builder/header/cart/options.php:75
msgid "Icon Badge"
msgstr "图标徽章"

#: inc/options/woocommerce/general/checkout-page.php:46
msgid "Checkout Page"
msgstr "结算页面"

#: inc/panel-builder/header/text/config.php:4
msgid "HTML"
msgstr "HTML"

#: inc/panel-builder/header/text/options.php:13
msgid "You can add here some arbitrary HTML code."
msgstr "您可以在此处添加一些任意的 HTML 代码。"

#: inc/panel-builder/footer/socials/options.php:341
#: inc/panel-builder/header/socials/options.php:483
#: inc/panel-builder/header/socials/options.php:529
#: inc/panel-builder/header/socials/options.php:568
#: inc/panel-builder/header/socials/options.php:607
msgid "Icons Border Color"
msgstr "图标边框颜色"

#: inc/options/single-elements/author-box.php:380
#: inc/panel-builder/footer/socials/options.php:337
#: inc/panel-builder/header/socials/options.php:479
#: inc/panel-builder/header/socials/options.php:525
#: inc/panel-builder/header/socials/options.php:564
#: inc/panel-builder/header/socials/options.php:603
msgid "Icons Background Color"
msgstr "图标背景色"

#: inc/panel-builder/footer/socials/options.php:128
#: inc/panel-builder/header/socials/options.php:128
#: inc/panel-builder/header/trigger/options.php:59
msgid "Solid"
msgstr "立方体"

#: inc/panel-builder/footer/socials/options.php:110
#: inc/panel-builder/header/socials/options.php:109
msgid "Rounded"
msgstr "圆角"

#: inc/options/single-elements/post-share-box.php:101
#: inc/panel-builder/footer/socials/options.php:97
#: inc/panel-builder/header/socials/options.php:96
msgid "Official"
msgstr "官方"

#: inc/options/general/back-to-top.php:38 inc/options/general/meta.php:164
#: inc/options/general/page-title.php:751
#: inc/options/general/page-title.php:1273
#: inc/options/general/page-title.php:1314
#: inc/options/general/posts-listing.php:450 inc/options/meta/default.php:81
#: inc/options/meta/default.php:112 inc/options/meta/page.php:80
#: inc/options/meta/page.php:111 inc/options/meta/post.php:76
#: inc/options/meta/post.php:110
#: inc/options/single-elements/post-share-box.php:100
#: inc/options/woocommerce/general/product-badges.php:91
#: inc/options/woocommerce/single-product-layers.php:41
#: inc/options/woocommerce/single-product-layers.php:62
#: inc/options/woocommerce/single-product-layers.php:107
#: inc/options/woocommerce/single-product-layers.php:373
#: inc/panel-builder/footer/socials/options.php:96
#: inc/panel-builder/header/socials/options.php:95
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/bundle/options.js:49 static/js/backend/woo-variation.js:39
#: static/js/options/options/color-palettes/ColorPalettesModal.js:41
#: static/js/options/options/ct-ratio.js:108
#: static/js/options/options/ct-ratio.js:257
#: static/js/options/options/ct-slider.js:93
#: static/js/options/options/ct-slider.js:118
#: static/js/options/options/ct-spacing.js:150
#: static/js/options/options/ct-spacing.js:209
#: static/js/options/options/ct-typography.js:151
msgid "Custom"
msgstr "自定义"

#: inc/options/single-elements/post-share-box.php:354
#: inc/panel-builder/footer/socials/options.php:78
#: inc/panel-builder/header/socials/options.php:77
msgid "Icons Spacing"
msgstr "图标间距"

#: inc/panel-builder/footer/socials/options.php:68
#: inc/panel-builder/header/socials/options.php:67
msgid "Icons Size"
msgstr "图标大小"

#: inc/panel-builder/header/button/options.php:267
#: inc/panel-builder/header/text/options.php:131
msgid "User Visibility"
msgstr "用户可见性"

#: inc/panel-builder/header/search/options.php:671
msgid "Modal Background"
msgstr "模态背景"

#: inc/options/pages/search-page.php:93
msgid "Live results"
msgstr "实时结果"

#: inc/options/general/back-to-top.php:147
#: inc/options/single-elements/post-share-box.php:327
#: inc/options/woocommerce/single-product-layers.php:354
#: inc/panel-builder/header/cart/options.php:65
#: inc/panel-builder/header/cart/options.php:1064
#: inc/panel-builder/header/offcanvas/options.php:240
#: inc/panel-builder/header/search/options.php:52
#: inc/panel-builder/header/search/options.php:703
#: inc/panel-builder/header/trigger/options.php:38
msgid "Icon Size"
msgstr "图标大小"

#: inc/panel-builder/header/trigger/config.php:4
msgid "Trigger"
msgstr "触发器"

#: inc/components/woocommerce/single/additional-actions-layer.php:56
#: inc/options/general/meta.php:373
#: inc/options/woocommerce/card-product-elements.php:418
#: inc/panel-builder/header/button/config.php:4 static/bundle/options.js:49
#: static/js/backend/woo-attributes/AfterHeading.js:15
msgid "Button"
msgstr "按钮"

#: inc/options/general/buttons.php:34 inc/options/general/posts-listing.php:341
#: inc/options/woocommerce/card-product-elements.php:277
msgid "Hover Effect"
msgstr "悬停效果"

#: inc/panel-builder/header/button/options.php:29
msgid "Size"
msgstr "尺寸"

#: inc/panel-builder/header/button/options.php:22
msgid "Ghost"
msgstr "幽灵"

#: inc/panel-builder/header/cart/options.php:647
#: inc/panel-builder/header/offcanvas/options.php:34
msgid "Right Side"
msgstr "右侧"

#: inc/panel-builder/header/cart/options.php:646
#: inc/panel-builder/header/offcanvas/options.php:33
msgid "Left Side"
msgstr "左侧"

#: inc/panel-builder/header/cart/options.php:638
#: inc/panel-builder/header/offcanvas/options.php:27
msgid "Reveal From"
msgstr "显示从"

#: inc/panel-builder/header/offcanvas/options.php:17
msgid "Side Panel"
msgstr "侧边面板"

#: inc/panel-builder/header/offcanvas/options.php:10
msgid "Reveal as"
msgstr "显示为"

#: inc/options/general/buttons.php:24
msgid "Min Height"
msgstr "最小高度"

#: inc/panel-builder/header/menu-secondary/config.php:4
msgid "Menu 2"
msgstr "菜单 2"

#: inc/panel-builder/header/menu/config.php:4
msgid "Menu 1"
msgstr "菜单 1"

#: inc/panel-builder/footer/copyright/options.php:147
#: inc/panel-builder/footer/menu/options.php:204
#: inc/panel-builder/footer/socials/options.php:378
#: inc/panel-builder/footer/widget-area-1/options.php:116
#: inc/panel-builder/header/button/options.php:852
#: inc/panel-builder/header/cart/options.php:547
#: inc/panel-builder/header/logo/options.php:677
#: inc/panel-builder/header/menu/options.php:570
#: inc/panel-builder/header/mobile-menu/options.php:498
#: inc/panel-builder/header/offcanvas-logo/options.php:76
#: inc/panel-builder/header/socials/options.php:644
#: inc/panel-builder/header/text/options.php:419
#: inc/panel-builder/header/trigger/options.php:603
msgid "Margin"
msgstr "边距"

#: inc/panel-builder/footer/menu/options.php:71
#: inc/panel-builder/header/menu/options.php:136
#: inc/panel-builder/header/mobile-menu/options.php:137
msgid "Enabling this option will make the menu to stretch and fit the width of its parent column. "
msgstr "启用此选项将使菜单拉伸并适合其上级列的宽度"

#: inc/panel-builder/footer/menu/options.php:67
#: inc/panel-builder/header/menu/options.php:132
#: inc/panel-builder/header/mobile-menu/options.php:133
msgid "Stretch Menu"
msgstr "拉伸菜单"

#: inc/panel-builder/header/menu/options.php:119
msgid "Items Height"
msgstr "项目高度"

#: inc/panel-builder/footer/menu/options.php:15
#: inc/panel-builder/header/menu/options.php:15
#: inc/panel-builder/header/mobile-menu/options.php:11
msgid "Select menu..."
msgstr "选择菜单..."

#: inc/panel-builder/footer/menu/options.php:9
#: inc/panel-builder/header/menu/options.php:9
#: inc/panel-builder/header/mobile-menu/options.php:5
msgid "Select Menu"
msgstr "选择菜单"

#: inc/helpers.php:69
msgid "You don't have a menu yet, please create one here &rarr;"
msgstr "您还没有菜单, 请在此处创建一个 &rarr;"

#: inc/options/general/posts-listing.php:1400
msgid "Card Border"
msgstr "卡片边框"

#: inc/options/general/page-title.php:931
#: inc/options/general/posts-listing.php:943
msgid "Meta Font"
msgstr "元信息字体"

#: inc/options/general/meta.php:313
msgid "Updated Date"
msgstr "更新日期"

#: inc/options/general/meta.php:276
msgid "Published Date"
msgstr "发布日期"

#: admin/notices/templates.php:42
msgid "We strongly recommend you to activate the"
msgstr "我们强烈建议您激活"

#: admin/notices/templates.php:40 static/bundle/274.717c716a87ab50d3b9d0.js:1
#: static/js/notification/Notification.js:68
msgid "Thanks for installing Blocksy, you rock!"
msgstr "谢谢安装 Blocksy, 你太棒了!"

#: inc/options/single-elements/post-share-box.php:273
msgid "WhatsApp"
msgstr "WhatsApp"

#: inc/options/single-elements/post-share-box.php:263
msgid "Viber"
msgstr "Viber"

#: inc/options/single-elements/post-share-box.php:223
msgid "Hacker News"
msgstr "黑客新闻"

#: header.php:34
msgid "Skip to content"
msgstr "跳过内容"

#: inc/options/general/meta.php:193 inc/options/general/page-title.php:536
msgid "Meta Elements"
msgstr "元要素"

#: inc/panel-builder/header/offcanvas/config.php:4
msgid "Offcanvas"
msgstr "Offcanvas"

#: inc/panel-builder/footer/bottom-row/config.php:4
#: inc/panel-builder/header/bottom-row/config.php:4
#: static/bundle/customizer-controls.js:1
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/panels-builder/placements/PlacementsBuilder/Row.js:19
#: static/js/customizer/sync/builder.js:133
msgid "Bottom Row"
msgstr "底部行"

#: inc/panel-builder/footer/middle-row/config.php:4
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
msgid "Middle Row"
msgstr "中间行"

#: inc/panel-builder/footer/top-row/config.php:4
#: inc/panel-builder/header/top-row/config.php:4
#: static/bundle/customizer-controls.js:1
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/panels-builder/placements/PlacementsBuilder/Row.js:17
#: static/js/customizer/sync/builder.js:117
msgid "Top Row"
msgstr "顶部行"

#: woocommerce/content-widget-reviews.php:48
msgid "by %s"
msgstr "作者 %s"

#: inc/components/pagination.php:196
msgid "Next"
msgstr "下一个"

#: inc/components/pagination.php:194
msgid "Prev"
msgstr "上一个"

#: inc/classes/colors.php:112
msgid "Palette Color %s"
msgstr "调色板颜色 %s"

#: inc/classes/screen-manager.php:246
#: inc/panel-builder/header/search/options.php:6
msgid "Posts"
msgstr "文章"

#: inc/panel-builder/header/search/options.php:476
msgid "Chose in which post types do you want to perform searches."
msgstr "选择要在哪个文章类型中执行搜索。"

#: inc/panel-builder/header/logo/options.php:574
#: inc/panel-builder/header/logo/options.php:603
#: inc/panel-builder/header/logo/options.php:625
#: inc/panel-builder/header/logo/options.php:646
msgid "Site Tagline Color"
msgstr "网站标语颜色"

#: inc/panel-builder/header/logo/options.php:564
msgid "Site Tagline Font"
msgstr "网站标语字体"

#: inc/panel-builder/header/logo/options.php:428
#: inc/panel-builder/header/logo/options.php:457
#: inc/panel-builder/header/logo/options.php:488
#: inc/panel-builder/header/logo/options.php:518
msgid "Site Title Color"
msgstr "网站标题颜色"

#: inc/panel-builder/header/logo/options.php:216
msgid "Site Tagline Visibility"
msgstr "网站标语可见性"

#: inc/panel-builder/header/logo/options.php:196
msgid "Site Tagline"
msgstr "站点副标题"

#: inc/options/general/comments-single.php:181
#: inc/options/general/page-title.php:1197
#: inc/options/single-elements/related-posts.php:689
#: inc/panel-builder/footer/options.php:91
msgid "Container Background"
msgstr "容器背景"

#: inc/options/meta/default.php:25 inc/options/meta/post.php:20
#: inc/options/posts/post.php:12
msgid "Post Structure"
msgstr "文章结构"

#: inc/options/posts/blog.php:13
msgid "Blog Title"
msgstr "博客标题"

#: inc/options/general/meta.php:233 inc/options/general/meta.php:287
#: inc/options/general/meta.php:324 inc/options/general/meta.php:385
#: inc/options/general/pagination.php:58
msgid "Label"
msgstr "标签"

#: inc/options/general/posts-listing.php:312
#: inc/options/single-elements/featured-image.php:72
#: inc/options/woocommerce/card-product-elements.php:164
msgid "Image Width"
msgstr "图像宽度"

#: inc/options/single-elements/featured-image.php:104
msgid "Below Title"
msgstr "标题下面"

#: inc/options/single-elements/featured-image.php:103
msgid "Above Title"
msgstr "标题上面"

#: inc/options/single-elements/featured-image.php:96
msgid "Image Location"
msgstr "图像位置"

#: inc/options/single-elements/structure-design.php:54
msgid "Content Area Background"
msgstr "内容区域背景"

#: inc/options/general/posts-listing.php:1686
#: inc/options/single-elements/structure-design.php:21
#: inc/options/woocommerce/archive-main.php:169
msgid "Page Background"
msgstr "页面背景"

#: inc/options/meta/default.php:97 inc/options/meta/page.php:96
#: inc/options/meta/post.php:92
#: inc/options/single-elements/featured-image.php:80
#: inc/options/single-elements/structure.php:85
msgid "Wide"
msgstr "宽阔"

#: inc/options/meta/default.php:90 inc/options/meta/page.php:89
#: inc/options/meta/post.php:85 inc/options/single-elements/structure.php:77
msgid "Content Area Style"
msgstr "内容区域风格"

#: inc/options/general/page-title.php:1272
#: inc/options/general/page-title.php:1313 inc/options/meta/default.php:80
#: inc/options/meta/default.php:111 inc/options/meta/page.php:79
#: inc/options/meta/page.php:110 inc/options/meta/post.php:75
#: inc/options/meta/post.php:109
#: inc/panel-builder/footer/widget-area-1/options.php:110
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/bundle/options.js:49
#: static/js/backend/woo-attributes/AfterHeading.js:30
#: static/js/options/options/background/ImagePicker.js:119
#: static/js/options/options/ct-border.js:45
#: static/js/options/options/ct-box-shadow.js:66
msgid "Inherit"
msgstr "继承"

#: inc/options/general/posts-listing.php:68
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:93
msgid "Cover"
msgstr "覆盖"

#: inc/options/general/posts-listing.php:1069
#: inc/options/general/posts-listing.php:1115
#: inc/options/general/posts-listing.php:1161
#: inc/options/woocommerce/general/messages.php:73
#: inc/options/woocommerce/general/messages.php:189
#: inc/options/woocommerce/general/messages.php:303
#: inc/options/woocommerce/single-product-elements.php:360
#: inc/options/woocommerce/single-product-elements.php:433
msgid "Button Font Color"
msgstr "按钮字体颜色"

#: inc/options/general/back-to-top.php:179
msgid "Alignment"
msgstr "对齐"

#: inc/options/general/posts-listing.php:516
msgid "Show Arrow"
msgstr "显示箭头"

#: inc/options/general/posts-listing.php:497
#: inc/panel-builder/footer/socials/options.php:129
#: inc/panel-builder/header/socials/options.php:127
#: inc/panel-builder/header/trigger/options.php:58
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/box-shadow/box-shadow-modal.js:221
msgid "Outline"
msgstr "大纲"

#: inc/options/general/posts-listing.php:487
msgid "Read More Button"
msgstr "继续阅读按钮"

#: inc/options/single-elements/author-box.php:347
#: inc/options/single-elements/post-share-box.php:93
#: inc/options/single-elements/post-share-box.php:546
#: inc/options/single-elements/post-share-box.php:606
#: inc/options/woocommerce/single-product-layers.php:366
#: inc/panel-builder/footer/socials/options.php:89
#: inc/panel-builder/footer/socials/options.php:295
#: inc/panel-builder/header/socials/options.php:88
#: inc/panel-builder/header/socials/options.php:346
#: inc/panel-builder/header/socials/options.php:379
#: inc/panel-builder/header/socials/options.php:412
#: inc/panel-builder/header/socials/options.php:443
msgid "Icons Color"
msgstr "图标颜色"

#: inc/panel-builder/header/cart/options.php:124
#: inc/panel-builder/header/logo/options.php:285
#: inc/panel-builder/header/search/options.php:101
#: inc/panel-builder/header/trigger/options.php:125
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:112
msgid "Right"
msgstr "右"

#: inc/panel-builder/header/cart/options.php:123
#: inc/panel-builder/header/logo/options.php:284
#: inc/panel-builder/header/search/options.php:100
#: inc/panel-builder/header/trigger/options.php:124
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:111
msgid "Left"
msgstr "左"

#: inc/options/general/typography.php:17
msgid "Base Font"
msgstr "基本字体"

#: inc/panel-builder/header/logo/options.php:148
#: inc/panel-builder/header/logo/options.php:417
msgid "Site Title"
msgstr "站点标题"

#. translators: placeholder here is the link URL.
#: inc/options/single-elements/author-box.php:129
msgid "You can set the author social channels %shere%s."
msgstr "您可以%s在此处%s设置作者的社交频道。"

#: inc/options/customizer.php:85
msgid "Typography"
msgstr "字体样式"

#: inc/options/general/content-elements.php:25
#: inc/options/general/page-title.php:752
#: inc/options/general/posts-listing.php:349
#: inc/options/woocommerce/card-product-elements.php:285
#: inc/panel-builder/footer/socials/options.php:109
#: inc/panel-builder/footer/widget-area-1/options.php:109
#: inc/panel-builder/header/button/options.php:163
#: inc/panel-builder/header/socials/options.php:108
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-border.js:75
#: static/js/options/options/ct-box-shadow.js:69
msgid "None"
msgstr "无"

#: inc/options/general/pagination.php:266
#: inc/options/general/posts-listing.php:586
#: inc/options/general/sidebar.php:437
#: inc/options/woocommerce/single-product-elements.php:506
#: inc/options/woocommerce/single-product-layers.php:224
msgid "Divider"
msgstr "分割线"

#: inc/options/general/typography.php:132
msgid "Preformatted"
msgstr "预格式化"

#: inc/options/general/colors.php:745 inc/options/general/typography.php:88
msgid "Heading 6 (H6)"
msgstr "标题6 (H6)"

#: inc/options/general/colors.php:723 inc/options/general/typography.php:77
msgid "Heading 5 (H5)"
msgstr "标题5 (H5)"

#: inc/options/general/colors.php:701 inc/options/general/typography.php:66
msgid "Heading 4 (H4)"
msgstr "标题4 (H4)"

#: inc/options/general/colors.php:679 inc/options/general/typography.php:55
msgid "Heading 3 (H3)"
msgstr "标题3 (H3)"

#: inc/options/general/colors.php:657 inc/options/general/typography.php:44
msgid "Heading 2 (H2)"
msgstr "标题2 (H2)"

#: inc/options/general/colors.php:635 inc/options/general/typography.php:33
msgid "Heading 1 (H1)"
msgstr "标题1 (H1)"

#: inc/options/general/meta.php:157
msgid "Date Format"
msgstr "日期格式"

#: inc/options/general/posts-listing.php:1502
msgid "Gutenberg"
msgstr "古腾堡"

#: inc/options/general/posts-listing.php:1497
msgid "Enhanced Grid"
msgstr "增强网格"

#: inc/options/general/page-title.php:854
#: inc/options/general/posts-listing.php:825
#: inc/options/integrations/tutorlms-single.php:8
#: inc/options/woocommerce/card-product-elements.php:602
#: inc/options/woocommerce/single-product-elements.php:141
msgid "Title Font"
msgstr "标题字体"

#: inc/options/woocommerce/single-product-tabs.php:188
#: inc/panel-builder/footer/copyright/options.php:95
#: inc/panel-builder/footer/menu/options.php:153
#: inc/panel-builder/header/menu/options.php:158
#: inc/panel-builder/header/menu/options.php:736
#: inc/panel-builder/header/mobile-menu/options.php:197
#: inc/panel-builder/header/mobile-menu/options.php:330
#: inc/panel-builder/header/search/options.php:512
#: inc/panel-builder/header/text/options.php:156
msgid "Font"
msgstr "字体"

#: inc/components/single/comments.php:73
msgid "Save my name, email, and website in this browser for the next time I comment."
msgstr "将我的姓名, 邮箱地址和网站信息保存在此浏览器中, 以便下次发表评论。"

#: inc/options/single-elements/post-share-box.php:253
msgid "Telegram"
msgstr "Telegram"

#: inc/options/single-elements/post-share-box.php:233
msgid "VKontakte"
msgstr "VKontakte"

#: admin/dashboard/plugins/config.php:39
msgid "A new and innovative way of building WordPress pages visually. No designer or developer skills required. The only tools you'll need to master are clicks and drags."
msgstr "一种新的、创新的可视化构建 WordPress 页面的方法。不需要设计师或开发人员的技能。您唯一需要掌握的工具就是点击和拖动。"

#: admin/dashboard/api.php:55
msgid "Theme"
msgstr "主题"

#: inc/options/woocommerce/single-product-layers.php:40
msgid "%s"
msgstr "%s"

#: comments.php:81
msgid "Comments are closed."
msgstr "已关闭评论。"

#: comments.php:73
msgid "Newer Comments &rarr;"
msgstr "最新评论 &rarr;"

#: comments.php:69
msgid "&larr; Older Comments"
msgstr "&larr; 较早评论"

#: comments.php:64
msgid "Comment navigation"
msgstr "评论导航"

#. translators: % refers to the number of comments, when more than 1
#: comments.php:36 inc/components/post-meta.php:499
msgid "% Comments"
msgstr "% 评论"

#: comments.php:36
msgid "One comment"
msgstr "一条评论"

#: comments.php:36
msgid "No comments yet"
msgstr "暂时没有评论"

#: inc/components/single/comments.php:56
msgid "Add Comment"
msgstr "添加评论"

#: inc/components/single/comments.php:127 inc/components/social-box.php:904
#: inc/components/social-box.php:1548
#: inc/components/woocommerce/single/review-form.php:16
#: inc/options/single-elements/post-share-box.php:303
msgid "Email"
msgstr "邮箱"

#: inc/components/single/comments.php:120
#: inc/components/woocommerce/single/review-form.php:14
msgid "Name"
msgstr "名称"

#: inc/components/single/comments.php:43
msgid "Cancel Reply"
msgstr "取消回复"

#: inc/components/single/comments.php:42
msgid "Leave a Reply"
msgstr "留下评论"

#. translators: %s are the opening and closing of the html tags
#: inc/components/hero/elements.php:342
msgid "%sSorry, but nothing matched your search terms. Please try again with some different keywords.%s"
msgstr "%s抱歉, 但没有与您的搜索字词匹配的内容。 请使用其他不同的关键字再试一次。%s"

#: inc/components/single/comments.php:279
msgid "Your comment is awaiting moderation."
msgstr "您的评论正在等待审核。"

#. translators: 1: date, 2: time
#: inc/components/single/comments.php:233
msgid "%1$s / %2$s"
msgstr "%1$s / %2$s"

#. translators: post title
#: inc/template-tags.php:366
msgid "Next %s"
msgstr "下一页 %s"

#. translators: post title
#: inc/template-tags.php:338
msgid "Previous %s"
msgstr "上一页 %s"

#: inc/components/social-box.php:616 inc/components/social-box.php:1604
#: inc/options/single-elements/post-share-box.php:243
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: inc/options/single-elements/post-share-box.php:213
msgid "Reddit"
msgstr "Reddit"

#: inc/customizer/init.php:63
msgid "Core"
msgstr "核心"

#: inc/options/customizer.php:156
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/manager.php:232
msgid "Show more"
msgstr "显示更多"

#: inc/components/woocommerce/boot.php:99 inc/init.php:480
msgid "Add widgets here."
msgstr "在此添加小工具。"

#: inc/components/post-meta.php:199 inc/components/post-meta.php:777
#: inc/components/post-meta.php:785 inc/options/general/meta.php:290
#: inc/options/general/meta.php:327
#: static/js/customizer/sync/helpers/entry-meta.js:82
#: static/js/customizer/sync/helpers/entry-meta.js:93
msgid "On"
msgstr "在"

#: inc/components/post-meta.php:189 inc/components/post-meta.php:769
#: inc/options/general/meta.php:236
#: static/js/customizer/sync/helpers/entry-meta.js:71
msgid "By"
msgstr "来自"

#. translators: placeholder here is the actual PHP version.
#: inc/php-fallback.php:16 inc/php-fallback.php:28 inc/php-fallback.php:42
msgid "Blocksy requires at least PHP version 5.7.0. You are running version %s. Please upgrade and try again."
msgstr "Blocksy 至少需要 PHP 5.7.0 版本。您正在运行版本 %s。请升级后重试。"

#: inc/options/engagement/general.php:16
msgid "Schema.org Markup"
msgstr "Schema.org 标记"

#: inc/options/customizer.php:202
msgid "Extensions"
msgstr "扩展"

#: inc/options/general/form-elements.php:13
msgid "Form Elements"
msgstr "表单元素"

#: inc/options/general/pagination.php:13 inc/options/pages/author-page.php:59
#: inc/options/pages/search-page.php:59 inc/options/posts/blog.php:66
#: inc/options/posts/categories.php:52
#: inc/options/posts/custom-post-type-archive.php:62
msgid "Pagination"
msgstr "页码"

#: inc/options/engagement/social-accounts.php:28
#: inc/options/engagement/social-accounts.php:34
msgid "Social Network Accounts"
msgstr "社交网络帐户"

#: inc/options/engagement/general.php:5
msgid "Visitor Engagement"
msgstr "访客统计"

#: inc/options/customizer.php:177
msgid "Single Product"
msgstr "产品详情页"

#: inc/options/general/custom-post-types.php:81
#: inc/options/woocommerce/general/product-badges.php:64
#: inc/options/woocommerce/general/product-badges.php:144
msgid "Archive"
msgstr "归档"

#: inc/components/breadcrumbs.php:351
msgid "Shop"
msgstr "商店"

#: inc/options/customizer.php:139 inc/options/pages/author-page.php:17
msgid "Author Page"
msgstr "作者页面"

#: inc/options/customizer.php:103
msgid "Blog Posts"
msgstr "博客文章"

#: inc/options/customizer.php:98
msgid "Post types"
msgstr "文章类型"

#: inc/integrations/beaver-themer.php:27 inc/options/customizer.php:67
msgid "Footer"
msgstr "页脚"

#: inc/options/general/layout.php:13
msgid "Layout"
msgstr "布局"

#: inc/manager.php:234
msgid "Search results"
msgstr "搜索结果"

#: inc/options/woocommerce/single-main.php:103
msgid "AJAX Add To Cart"
msgstr "异步方式加入购物车"

#: inc/options/woocommerce/single-product-gallery.php:324
#: inc/panel-builder/footer/menu/options.php:46
#: inc/panel-builder/footer/socials/options.php:146
msgid "Vertical"
msgstr "垂直"

#: inc/options/woocommerce/single-product-gallery.php:323
#: inc/panel-builder/footer/menu/options.php:45
#: inc/panel-builder/footer/socials/options.php:145
msgid "Horizontal"
msgstr "水平"

#: inc/components/blocks/blocks-fallback.php:36
#: inc/options/general/breadcrumbs.php:240
#: inc/options/general/page-title.php:279
#: inc/options/woocommerce/single-product-layers.php:133
msgid "Breadcrumbs"
msgstr "面包屑导航"

#: inc/options/meta/default.php:218 inc/options/meta/page.php:173
#: inc/options/meta/post.php:214 inc/options/pages/author-page.php:78
#: inc/options/pages/page.php:52 inc/options/pages/page.php:129
#: inc/options/pages/search-page.php:78 inc/options/posts/blog.php:85
#: inc/options/posts/custom-post-type-archive.php:81
#: inc/options/posts/custom-post-type-single.php:149
#: inc/options/posts/custom-post-type-single.php:171
#: inc/options/posts/post.php:67 inc/options/posts/post.php:121
#: inc/options/woocommerce/archive-main.php:197
#: inc/options/woocommerce/single-main.php:79
msgid "Page Elements"
msgstr "页面元素"

#: inc/options/integrations/tutorlms-single.php:162
#: inc/options/woocommerce/general/star-rating.php:19
msgid "Star Rating Color"
msgstr "星级颜色"

#: inc/options/woocommerce/general/product-badges.php:188
msgid "Sale Badge"
msgstr "销售徽章"

#: inc/options/woocommerce/card-product-elements.php:389
#: inc/options/woocommerce/general/star-rating.php:13
#: inc/options/woocommerce/single-product-layers.php:167
msgid "Star Rating"
msgstr "星级"

#: inc/options/woocommerce/archive-main.php:320
msgid "Sort by price (desc)"
msgstr "按价格排序 (降序)"

#: inc/options/woocommerce/archive-main.php:319
msgid "Sort by price (asc)"
msgstr "按价格排序 (升序)"

#: inc/options/woocommerce/archive-main.php:318
msgid "Sort by most recent"
msgstr "按最新排序"

#: inc/options/woocommerce/archive-main.php:317
msgid "Average rating"
msgstr "平均评分"

#: inc/options/woocommerce/archive-main.php:316
msgid "Popularity (sales)"
msgstr "人气 (销售)"

#: inc/options/woocommerce/archive-main.php:315
msgid "Default sorting (custom ordering + name)"
msgstr "默认排序 (自定义排序+名称)"

#: inc/options/woocommerce/archive-main.php:307
msgid "How should products be sorted in the catalog by default?"
msgstr "在默认情况下, 产品应该如何在目录页面中排序?"

#: inc/options/woocommerce/archive-main.php:302
msgid "Default product sorting"
msgstr "默认产品排序"

#: inc/options/woocommerce/archive-main.php:296
msgid "Show subcategories & products"
msgstr "显示子分类和产品"

#: inc/options/woocommerce/archive-main.php:295
msgid "Show subcategories"
msgstr "显示子分类"

#: inc/options/woocommerce/archive-main.php:291
msgid "Choose what to display on product category pages."
msgstr "选择在产品分类页面显示什么。"

#: inc/options/woocommerce/archive-main.php:282
msgid "Category display"
msgstr "分类显示"

#: inc/options/woocommerce/archive-main.php:276
msgid "Show categories & products"
msgstr "显示品类及产品"

#: inc/options/woocommerce/archive-main.php:275
msgid "Show categories"
msgstr "显示分类"

#: inc/options/woocommerce/archive-main.php:271
msgid "Choose what to display on the main shop page."
msgstr "选择要在主商店页面上显示的内容。"

#: inc/options/woocommerce/archive-main.php:266
#: inc/options/woocommerce/archive-main.php:274
#: inc/options/woocommerce/archive-main.php:286
#: inc/options/woocommerce/archive-main.php:294
msgid "Show products"
msgstr "显示产品"

#: inc/options/woocommerce/archive-main.php:262
msgid "Shop page display"
msgstr "店铺页面显示"

#: inc/options/woocommerce/archive-main.php:255
msgid "Product Catalog"
msgstr "产品目录"

#: inc/options/general/posts-listing.php:1542
msgid "Number of Posts"
msgstr "文章数量"

#: inc/options/single-elements/related-posts.php:411
msgid "After Comments"
msgstr "评论之后"

#: inc/options/single-elements/related-posts.php:410
msgid "Before Comments"
msgstr "评论之前"

#: inc/components/single/content-helpers.php:306
#: inc/options/single-elements/post-tags.php:37
msgid "Tags"
msgstr "标签"

#: inc/options/single-elements/related-posts.php:118
msgid "Related Criteria"
msgstr "相关的标准"

#: inc/options/single-elements/related-posts.php:19
#: inc/options/single-elements/related-posts.php:168 inc/template-tags.php:499
msgid "Related Posts"
msgstr "相关文章"

#: inc/options/meta/default.php:8 inc/options/meta/post.php:6
#: inc/options/posts/post.php:6
msgid "Post Title"
msgstr "文章标题"

#: inc/options/single-elements/post-nav.php:19
msgid "Posts Navigation"
msgstr "文章导航"

#: inc/options/single-elements/author-box.php:26
msgid "Box Type"
msgstr "盒子类型"

#: inc/options/single-elements/author-box.php:11
msgid "Author Box"
msgstr "作者盒子"

#: inc/options/single-elements/post-share-box.php:405
msgid "Bottom Box Spacing"
msgstr "底部盒子间距"

#: inc/options/single-elements/post-share-box.php:382
msgid "Top Box Spacing"
msgstr "顶部盒子间距"

#: inc/options/single-elements/post-share-box.php:169
msgid "Share Networks"
msgstr "分享网络"

#: inc/options/general/sidebar.php:259
#: inc/options/single-elements/post-share-box.php:123
#: inc/options/woocommerce/general/store-notice.php:48
#: inc/panel-builder/header/cart/options.php:125
#: inc/panel-builder/header/search/options.php:102
#: inc/panel-builder/header/trigger/options.php:126
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:110
msgid "Bottom"
msgstr "底部"

#: inc/options/general/sidebar.php:258
#: inc/options/single-elements/post-share-box.php:122
#: inc/options/woocommerce/general/store-notice.php:47
#: inc/panel-builder/header/logo/options.php:286
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:109
msgid "Top"
msgstr "顶部"

#: inc/options/single-elements/post-share-box.php:113
msgid "Box Location"
msgstr "盒子位置"

#: inc/components/blocks/blocks-fallback.php:40
#: inc/options/single-elements/post-share-box.php:683
#: inc/options/single-elements/post-share-box.php:699
msgid "Share Box"
msgstr "分享盒子"

#: inc/options/single-elements/post-tags.php:19
msgid "Post Tags"
msgstr "文章标签"

#: inc/options/meta/default.php:175 inc/options/meta/post.php:172
#: inc/options/posts/post.php:40
msgid "Post Elements"
msgstr "文章元素"

#: inc/options/general/sidebar-particular.php:30
msgid "Sidebar Position"
msgstr "侧边栏位置"

#: inc/options/customizer.php:73 inc/options/general/sidebar-particular.php:20
msgid "Sidebar"
msgstr "侧边栏"

#: inc/init.php:416
msgid "Header Menu 2"
msgstr "页眉菜单 2"

#: inc/components/social-box.php:603 inc/components/social-box.php:1692
msgid "VK"
msgstr "VK"

#: inc/options/single-elements/post-share-box.php:193
msgid "Pinterest"
msgstr "Pinterest"

#: inc/panel-builder/header/text/options.php:12
#: inc/panel-builder/header/text/view.php:46
msgid "Sample text"
msgstr "样本文字"

#: inc/components/blocks/legacy/legacy-socials-transformer.php:29
#: inc/options/single-elements/author-box.php:124
msgid "Social Icons"
msgstr "社交图标"

#: inc/init.php:414 inc/panel-builder/footer/menu/view.php:86
msgid "Footer Menu"
msgstr "页脚菜单"

#: inc/options/customizer.php:145
msgid "Search Page"
msgstr "搜索页"

#: inc/classes/screen-manager.php:247
#: inc/components/single/content-helpers.php:284 inc/options/customizer.php:133
#: inc/panel-builder/header/search/options.php:7
msgid "Pages"
msgstr "页面"

#: inc/options/customizer.php:109
msgid "Single Post"
msgstr "文章详情页"

#: inc/options/general/general.php:108
msgid "Color scheme"
msgstr "配色方案"

#: inc/options/general/layout.php:76
msgid "Wide Alignment Offset"
msgstr "宽阔调整偏移"

#: inc/options/general/layout.php:66
msgid "Narrow Container Max Width"
msgstr "狭窄容器的最大宽度"

#: inc/options/general/layout.php:28
msgid "Content Area Spacing"
msgstr "内容区域间距"

#: inc/options/general/layout.php:19
msgid "Maximum Site Width"
msgstr "最大网站宽度"

#: inc/options/general/sidebar.php:91
#: inc/options/single-elements/author-box.php:196
#: inc/options/single-elements/related-posts.php:677
msgid "Container Inner Spacing"
msgstr "容器内间距"

#: inc/options/general/back-to-top.php:253
msgid "Shape Background Color"
msgstr "形状背景颜色"

#: inc/options/general/back-to-top.php:142
msgid "Circle"
msgstr "圆形"

#: inc/options/general/back-to-top.php:141
#: inc/panel-builder/footer/socials/options.php:111
#: inc/panel-builder/header/socials/options.php:110
msgid "Square"
msgstr "正方形"

#: inc/options/general/back-to-top.php:133
msgid "Button Shape"
msgstr "按钮形状"

#: inc/options/general/back-to-top.php:19
msgid "Scroll to Top"
msgstr "滚动到顶部"

#: inc/panel-builder/header/menu/options.php:658
msgid "Reveal Effect"
msgstr "显示效果"

#: inc/options/general/page-title.php:863
#: inc/options/general/posts-listing.php:838
#: inc/options/integrations/tutorlms-single.php:17
#: inc/options/woocommerce/card-product-elements.php:611
#: inc/options/woocommerce/single-product-elements.php:149
msgid "Title Font Color"
msgstr "标题字体颜色"

#: inc/options/general/sidebar.php:163
msgid "Sticky Sidebar"
msgstr "吸附侧边栏"

#: inc/options/general/sidebar.php:149
msgid "Separate Widgets"
msgstr "分隔小工具"

#: inc/options/general/sidebar.php:108
msgid "Widgets Vertical Spacing"
msgstr "小工具垂直间距"

#: inc/options/general/sidebar.php:64
msgid "Sidebar Gap"
msgstr "侧边栏间隙"

#: inc/options/general/sidebar.php:53
msgid "Sidebar Width"
msgstr "侧边栏宽度"

#: inc/options/general/page-title.php:1177
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:184
msgid "Image Overlay Color"
msgstr "图像叠加颜色"

#: inc/panel-builder/header/text/options.php:362
#: inc/panel-builder/header/text/options.php:384
#: inc/panel-builder/header/text/options.php:405
msgid "Initial Color"
msgstr "初始颜色"

#: inc/options/general/page-title.php:772
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:28
msgid "Change Image"
msgstr "更改图像"

#: inc/options/general/page-title.php:771
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:27
msgid "Select Image"
msgstr "选择图像"

#: inc/options/general/page-title.php:766
msgid "Custom Image"
msgstr "自定义图像"

#: inc/options/general/page-title.php:828
msgid "Container Min Height"
msgstr "容器的最小高度"

#: inc/components/hero/elements.php:361
msgid "This is where you can add new products to your store."
msgstr "这就是您可以添加新产品到商铺的地方。"

#: inc/options/general/page-title.php:95
msgid "Description"
msgstr "描述"

#: inc/components/breadcrumbs.php:41 inc/components/hero/elements.php:213
#: inc/options/general/breadcrumbs.php:104
#: inc/options/general/page-title.php:119
#: inc/options/general/page-title.php:362
#: admin/dashboard/static/bundle/main.js:6
#: admin/dashboard/static/js/Navigation.js:21
msgid "Home"
msgstr "首页"

#: inc/classes/screen-manager.php:248 inc/components/hero/elements.php:213
#: inc/panel-builder/header/search/options.php:16
msgid "Products"
msgstr "产品"

#: inc/options/woocommerce/single-product-layers.php:285
msgid "Meta"
msgstr "元数据"

#: inc/options/single-elements/post-share-box.php:430
#: inc/options/single-elements/post-tags.php:71
#: inc/options/woocommerce/card-product-elements.php:530
#: inc/panel-builder/header/logo/options.php:304
#: inc/panel-builder/header/text/options.php:57
msgid "Content Alignment"
msgstr "内容对齐"

#: inc/options/general/page-title.php:192 static/bundle/options.js:49
#: static/js/backend/woo-attributes/AfterHeading.js:53
msgid "Type"
msgstr "类型"

#: inc/options/general/buttons.php:144
#: inc/options/general/form-elements.php:185
#: inc/options/general/pagination.php:281
#: inc/options/general/posts-listing.php:1435
#: inc/options/general/sidebar.php:479
#: inc/options/single-elements/author-box.php:508
#: inc/options/single-elements/featured-image.php:143
#: inc/options/single-elements/post-tags.php:172
#: inc/options/woocommerce/single-product-gallery.php:277
#: inc/options/woocommerce/single-product-gallery.php:372
#: inc/panel-builder/header/button/options.php:837
#: inc/panel-builder/header/cart/options.php:1080
#: inc/panel-builder/header/middle-row/options.php:514
#: inc/panel-builder/header/offcanvas/options.php:371
#: inc/panel-builder/header/search/options.php:829
#: inc/panel-builder/header/trigger/options.php:588
msgid "Border Radius"
msgstr "圆角半径"

#: inc/panel-builder/header/button/options.php:125
msgid "Open in new tab"
msgstr "在新标签页打开"

#: inc/panel-builder/header/button/options.php:50
#: inc/panel-builder/header/button/view.php:82
#: admin/dashboard/static/bundle/main.js:6
#: admin/dashboard/static/js/components/SinglePremiumPlugin.js:110
msgid "Download"
msgstr "下载"

#: inc/helpers.php:294 inc/panel-builder/header/button/options.php:40
msgid "Large"
msgstr "大"

#: inc/helpers.php:292 inc/panel-builder/header/button/options.php:39
msgid "Medium"
msgstr "中"

#: inc/panel-builder/header/button/options.php:38
msgid "Small"
msgstr "小"

#: inc/options/general/breadcrumbs.php:87
#: inc/options/general/breadcrumbs.php:214 inc/options/general/colors.php:581
#: inc/options/general/page-title.php:1136
#: inc/options/general/posts-listing.php:506
#: inc/options/single-elements/author-box.php:322
#: inc/options/woocommerce/general/product-badges.php:205
#: inc/options/woocommerce/general/product-badges.php:236
#: inc/options/woocommerce/single-product-elements.php:250
#: inc/panel-builder/header/cart/options.php:476
#: inc/panel-builder/header/cart/options.php:507
#: inc/panel-builder/header/cart/options.php:537
msgid "Text"
msgstr "文本"

#: inc/options/general/colors.php:586 inc/options/general/posts-listing.php:496
#: inc/options/woocommerce/general/product-badges.php:211
#: inc/options/woocommerce/general/product-badges.php:241
#: inc/options/woocommerce/single-product-tabs.php:319
#: inc/panel-builder/header/cart/options.php:470
#: inc/panel-builder/header/cart/options.php:502
#: inc/panel-builder/header/cart/options.php:532
#: inc/panel-builder/header/cart/options.php:948
#: inc/panel-builder/header/middle-row/options.php:145
#: inc/panel-builder/header/middle-row/options.php:172
#: inc/panel-builder/header/middle-row/options.php:181
#: inc/panel-builder/header/middle-row/options.php:196
#: inc/panel-builder/header/offcanvas/options.php:234
#: inc/panel-builder/header/search/options.php:697
msgid "Background"
msgstr "背景"

#: inc/panel-builder/header/cart/options.php:418
#: inc/panel-builder/header/cart/options.php:453
#: inc/panel-builder/header/cart/options.php:484
#: inc/panel-builder/header/cart/options.php:514
msgid "Badge Color"
msgstr "徽章颜色"

#: inc/options/pages/search-page.php:17
#: inc/panel-builder/header/search/options.php:411
msgid "Search Results"
msgstr "搜索结果"

#: inc/classes/screen-manager.php:211
#: inc/components/builder/header-elements.php:196
#: inc/panel-builder/header/search/config.php:4
#: inc/panel-builder/header/search/options.php:111
#: inc/panel-builder/header/search/options.php:424
#: inc/panel-builder/header/search/view.php:28
#: woocommerce/product-searchform.php:28
msgid "Search"
msgstr "搜索"

#: inc/options/general/back-to-top.php:224
#: inc/options/woocommerce/card-product-elements.php:53
#: inc/panel-builder/header/cart/options.php:298
#: inc/panel-builder/header/cart/options.php:326
#: inc/panel-builder/header/cart/options.php:356
#: inc/panel-builder/header/cart/options.php:385
#: inc/panel-builder/header/cart/options.php:954
#: inc/panel-builder/header/offcanvas/options.php:251
#: inc/panel-builder/header/search/options.php:277
#: inc/panel-builder/header/search/options.php:304
#: inc/panel-builder/header/search/options.php:336
#: inc/panel-builder/header/search/options.php:366
#: inc/panel-builder/header/search/options.php:714
#: inc/panel-builder/header/trigger/options.php:301
#: inc/panel-builder/header/trigger/options.php:330
#: inc/panel-builder/header/trigger/options.php:361
#: inc/panel-builder/header/trigger/options.php:390
msgid "Icon Color"
msgstr "图标颜色"

#: inc/panel-builder/header/cart/options.php:597
msgid "Off Canvas"
msgstr "Off Canvas"

#: inc/panel-builder/header/offcanvas/options.php:16
msgid "Modal"
msgstr "模态"

#: inc/panel-builder/header/cart/options.php:607
msgid "Dropdown Top Offset"
msgstr "下拉顶部偏移量"

#: inc/panel-builder/header/menu/options.php:669
msgid "Opacity"
msgstr "不透明度"

#: inc/panel-builder/header/menu/options.php:668
msgid "Inner Reveal"
msgstr "内部显示"

#: inc/panel-builder/header/menu/options.php:586
msgid "Dropdown Options"
msgstr "下拉选项"

#: inc/components/menus.php:527 inc/helpers/cpt.php:35
#: inc/options/general/back-to-top.php:37
#: inc/options/general/breadcrumbs.php:12 inc/options/general/meta.php:163
#: inc/options/general/meta.php:372 inc/options/general/page-title.php:732
#: inc/options/single-elements/featured-image.php:79
#: inc/options/single-elements/post-nav.php:41
#: inc/options/single-elements/related-posts.php:145
#: inc/options/woocommerce/card-product-elements.php:417
#: inc/options/woocommerce/general/product-badges.php:90
#: inc/options/woocommerce/single-product-layers.php:61
#: inc/options/woocommerce/single-product-layers.php:106
#: inc/options/woocommerce/single-product-layers.php:372
#: inc/options/woocommerce/single-product-tabs.php:91
#: inc/panel-builder/footer/middle-row/options.php:435
#: inc/panel-builder/footer/middle-row/options.php:628
#: inc/panel-builder/footer/middle-row/options.php:669
#: inc/panel-builder/footer/options.php:12
#: inc/panel-builder/header/button/options.php:17
#: inc/panel-builder/header/button/options.php:37
#: inc/panel-builder/header/menu/options.php:84
#: inc/panel-builder/header/menu/options.php:667
#: inc/panel-builder/header/middle-row/options.php:41
#: inc/panel-builder/header/middle-row/options.php:306
#: inc/panel-builder/header/middle-row/options.php:413
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/bundle/options.js:49 static/js/backend/woo-variation.js:38
#: static/js/options/components/InputWithValidCssExpression.js:9
#: static/js/options/options/typography/helpers.js:67
#: static/js/options/options/typography/helpers.js:122
msgid "Default"
msgstr "默认"

#: inc/options/general/meta.php:447
#: inc/panel-builder/header/menu/options.php:1030
#: inc/panel-builder/header/mobile-menu/options.php:292
msgid "Items Divider"
msgstr "项目分隔器"

#: inc/options/woocommerce/single-product-tabs.php:165
#: inc/panel-builder/footer/menu/options.php:51
#: inc/panel-builder/header/menu/options.php:95
#: inc/panel-builder/header/menu/options.php:648
#: inc/panel-builder/header/mobile-menu/options.php:120
msgid "Items Spacing"
msgstr "项目间距"

#: inc/options/general/back-to-top.php:108
#: inc/options/general/content-elements.php:50
#: inc/options/general/sidebar.php:46
#: inc/options/woocommerce/single-product-tabs.php:44
#: inc/panel-builder/header/cart/options.php:47
#: inc/panel-builder/header/menu/options.php:65
msgid "Type 4"
msgstr "类型 4"

#: inc/options/general/back-to-top.php:103
#: inc/options/general/breadcrumbs.php:72
#: inc/options/general/content-elements.php:49
#: inc/options/general/sidebar.php:40
#: inc/options/woocommerce/general/product-badges.php:47
#: inc/options/woocommerce/single-product-tabs.php:39
#: inc/panel-builder/header/cart/options.php:42
#: inc/panel-builder/header/menu/options.php:60
#: inc/panel-builder/header/mobile-menu/options.php:70
#: inc/panel-builder/header/trigger/options.php:32
msgid "Type 3"
msgstr "类型 3"

#: inc/panel-builder/header/cart/options.php:7
#: inc/panel-builder/header/menu/options.php:30
msgid "Top Level Options"
msgstr "顶级选项"

#: inc/components/builder/header-elements.php:132
#: inc/panel-builder/footer/menu/config.php:4
#: inc/panel-builder/header/offcanvas/options.php:111
#: inc/panel-builder/header/trigger/options.php:135
#: inc/panel-builder/header/trigger/view.php:11
msgid "Menu"
msgstr "菜单"

#: inc/panel-builder/header/logo/config.php:4
#: inc/panel-builder/header/logo/options.php:10
#: inc/panel-builder/header/offcanvas-logo/options.php:11
msgid "Logo"
msgstr "标识"

#: inc/panel-builder/header/middle-row/options.php:320
#: inc/panel-builder/header/middle-row/options.php:347
#: inc/panel-builder/header/middle-row/options.php:362
#: inc/panel-builder/header/middle-row/options.php:377
msgid "Bottom Border"
msgstr "底部边框"

#: inc/panel-builder/header/middle-row/options.php:213
#: inc/panel-builder/header/middle-row/options.php:240
#: inc/panel-builder/header/middle-row/options.php:254
#: inc/panel-builder/header/middle-row/options.php:269
msgid "Top Border"
msgstr "顶部边框"

#: inc/options/general/back-to-top.php:98
#: inc/options/general/breadcrumbs.php:67
#: inc/options/general/content-elements.php:48
#: inc/options/general/page-title.php:208 inc/options/general/sidebar.php:35
#: inc/options/single-elements/author-box.php:49
#: inc/options/single-elements/post-share-box.php:80
#: inc/options/woocommerce/archive-main.php:70
#: inc/options/woocommerce/general/product-badges.php:42
#: inc/options/woocommerce/general/quantity-input.php:50
#: inc/options/woocommerce/single-product-tabs.php:34
#: inc/panel-builder/header/cart/options.php:37
#: inc/panel-builder/header/menu/options.php:55
#: inc/panel-builder/header/mobile-menu/options.php:65
#: inc/panel-builder/header/trigger/options.php:27
msgid "Type 2"
msgstr "类型 2"

#: inc/options/general/back-to-top.php:93
#: inc/options/general/breadcrumbs.php:62
#: inc/options/general/content-elements.php:47
#: inc/options/general/page-title.php:203 inc/options/general/sidebar.php:30
#: inc/options/single-elements/author-box.php:44
#: inc/options/single-elements/post-share-box.php:75
#: inc/options/woocommerce/archive-main.php:65
#: inc/options/woocommerce/general/product-badges.php:37
#: inc/options/woocommerce/general/quantity-input.php:49
#: inc/options/woocommerce/single-product-tabs.php:29
#: inc/panel-builder/header/cart/options.php:32
#: inc/panel-builder/header/menu/options.php:50
#: inc/panel-builder/header/mobile-menu/options.php:60
#: inc/panel-builder/header/trigger/options.php:22
msgid "Type 1"
msgstr "类型 1"

#: inc/options/customizer.php:50
msgid "General Options"
msgstr "常规选项"

#: inc/components/woocommerce/single/additional-actions-layer.php:97
#: inc/options/general/back-to-top.php:209
#: inc/options/general/page-title.php:313
#: inc/options/general/page-title.php:514
#: inc/options/general/page-title.php:815 inc/options/general/pagination.php:85
#: inc/options/general/pagination.php:104 inc/options/general/sidebar.php:244
#: inc/options/single-elements/author-box.php:225
#: inc/options/single-elements/featured-image.php:126
#: inc/options/single-elements/post-nav.php:121
#: inc/options/single-elements/post-nav.php:141
#: inc/options/single-elements/post-nav.php:159
#: inc/options/single-elements/post-share-box.php:464
#: inc/options/single-elements/post-tags.php:100
#: inc/options/single-elements/related-posts.php:477
#: inc/options/woocommerce/related-upsells.php:81
#: inc/options/woocommerce/related-upsells.php:105
#: inc/panel-builder/footer/copyright/options.php:79
#: inc/panel-builder/footer/menu/options.php:137
#: inc/panel-builder/footer/middle-row/options.php:454
#: inc/panel-builder/footer/options.php:53
#: inc/panel-builder/footer/socials/options.php:198
#: inc/panel-builder/footer/socials/options.php:217
#: inc/panel-builder/header/button/options.php:258
#: inc/panel-builder/header/cart/options.php:96
#: inc/panel-builder/header/logo/options.php:182
#: inc/panel-builder/header/logo/options.php:229
#: inc/panel-builder/header/logo/options.php:369
#: inc/panel-builder/header/search/options.php:75
#: inc/panel-builder/header/socials/options.php:152
#: inc/panel-builder/header/text/options.php:122
#: inc/panel-builder/header/trigger/options.php:99
msgid "Desktop"
msgstr "台式机"

#: inc/options/general/back-to-top.php:197
#: inc/options/general/page-title.php:302
#: inc/options/general/page-title.php:503
#: inc/options/single-elements/author-box.php:213
#: inc/options/single-elements/post-share-box.php:452
#: inc/options/single-elements/post-tags.php:87
#: inc/options/single-elements/related-posts.php:464
msgid "Visibility"
msgstr "能见度"

#: inc/components/woocommerce/single/additional-actions-layer.php:99
#: inc/options/general/back-to-top.php:211
#: inc/options/general/page-title.php:315
#: inc/options/general/page-title.php:516
#: inc/options/general/page-title.php:817 inc/options/general/pagination.php:87
#: inc/options/general/pagination.php:106 inc/options/general/sidebar.php:246
#: inc/options/single-elements/author-box.php:227
#: inc/options/single-elements/featured-image.php:128
#: inc/options/single-elements/post-nav.php:123
#: inc/options/single-elements/post-nav.php:143
#: inc/options/single-elements/post-nav.php:161
#: inc/options/single-elements/post-share-box.php:466
#: inc/options/single-elements/post-tags.php:102
#: inc/options/single-elements/related-posts.php:479
#: inc/options/woocommerce/related-upsells.php:83
#: inc/options/woocommerce/related-upsells.php:107
#: inc/panel-builder/footer/copyright/options.php:81
#: inc/panel-builder/footer/menu/options.php:139
#: inc/panel-builder/footer/middle-row/options.php:456
#: inc/panel-builder/footer/options.php:55
#: inc/panel-builder/footer/socials/options.php:200
#: inc/panel-builder/footer/socials/options.php:219
#: inc/panel-builder/header/button/options.php:203
#: inc/panel-builder/header/button/options.php:260
#: inc/panel-builder/header/cart/options.php:98
#: inc/panel-builder/header/cart/options.php:1129
#: inc/panel-builder/header/logo/options.php:184
#: inc/panel-builder/header/logo/options.php:231
#: inc/panel-builder/header/logo/options.php:371
#: inc/panel-builder/header/middle-row/options.php:128
#: inc/panel-builder/header/mobile-menu/options.php:174
#: inc/panel-builder/header/search/options.php:77
#: inc/panel-builder/header/search/options.php:867
#: inc/panel-builder/header/socials/options.php:154
#: inc/panel-builder/header/socials/options.php:180
#: inc/panel-builder/header/text/options.php:124
#: inc/panel-builder/header/text/options.php:454
#: inc/panel-builder/header/trigger/options.php:101
#: inc/panel-builder/header/trigger/options.php:633
msgid "Mobile"
msgstr "移动设备"

#: inc/components/woocommerce/single/additional-actions-layer.php:98
#: inc/options/general/back-to-top.php:210
#: inc/options/general/page-title.php:314
#: inc/options/general/page-title.php:515
#: inc/options/general/page-title.php:816 inc/options/general/pagination.php:86
#: inc/options/general/pagination.php:105 inc/options/general/sidebar.php:245
#: inc/options/single-elements/author-box.php:226
#: inc/options/single-elements/featured-image.php:127
#: inc/options/single-elements/post-nav.php:122
#: inc/options/single-elements/post-nav.php:142
#: inc/options/single-elements/post-nav.php:160
#: inc/options/single-elements/post-share-box.php:465
#: inc/options/single-elements/post-tags.php:101
#: inc/options/single-elements/related-posts.php:478
#: inc/options/woocommerce/related-upsells.php:82
#: inc/options/woocommerce/related-upsells.php:106
#: inc/panel-builder/footer/copyright/options.php:80
#: inc/panel-builder/footer/menu/options.php:138
#: inc/panel-builder/footer/middle-row/options.php:455
#: inc/panel-builder/footer/options.php:54
#: inc/panel-builder/footer/socials/options.php:199
#: inc/panel-builder/footer/socials/options.php:218
#: inc/panel-builder/header/button/options.php:202
#: inc/panel-builder/header/button/options.php:259
#: inc/panel-builder/header/cart/options.php:97
#: inc/panel-builder/header/cart/options.php:1128
#: inc/panel-builder/header/logo/options.php:183
#: inc/panel-builder/header/logo/options.php:230
#: inc/panel-builder/header/logo/options.php:370
#: inc/panel-builder/header/middle-row/options.php:127
#: inc/panel-builder/header/mobile-menu/options.php:173
#: inc/panel-builder/header/search/options.php:76
#: inc/panel-builder/header/search/options.php:866
#: inc/panel-builder/header/socials/options.php:153
#: inc/panel-builder/header/socials/options.php:179
#: inc/panel-builder/header/text/options.php:123
#: inc/panel-builder/header/text/options.php:453
#: inc/panel-builder/header/trigger/options.php:100
#: inc/panel-builder/header/trigger/options.php:632
msgid "Tablet"
msgstr "平板电脑"

#: inc/options/general/page-title.php:725
#: inc/options/woocommerce/single-product-gallery.php:87
#: inc/panel-builder/footer/middle-row/options.php:427
msgid "Container Width"
msgstr "容器宽度"

#: inc/options/general/posts-listing.php:717
msgid "Card Inner Spacing"
msgstr "卡片内间距"

#: inc/options/general/posts-listing.php:1384
#: inc/options/woocommerce/card-product-elements.php:1051
msgid "Card Background Color"
msgstr "卡片背景颜色"

#: inc/options/general/posts-listing.php:914
msgid "Excerpt Color"
msgstr "摘要颜色"

#: inc/options/general/posts-listing.php:666
msgid "Cards Gap"
msgstr "卡片间距"

#: inc/options/general/meta.php:215 inc/options/general/page-title.php:401
#: inc/options/woocommerce/general/account-page.php:39
msgid "Avatar Size"
msgstr "头像大小"

#: inc/options/general/meta.php:374
#: inc/options/woocommerce/card-product-elements.php:419
#: inc/panel-builder/footer/widget-area-1/options.php:111
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/typography/FontOptions.js:232
msgid "Underline"
msgstr "强调"

#: inc/components/single/single-helpers.php:384
#: inc/options/general/comments-single.php:15 inc/options/general/meta.php:270
#: inc/options/general/page-title.php:545
msgid "Comments"
msgstr "评论"

#: inc/classes/screen-manager.php:209 inc/options/customizer.php:118
#: inc/options/posts/categories.php:10
#: inc/options/woocommerce/card-product-elements.php:406
msgid "Categories"
msgstr "分类"

#: inc/classes/screen-manager.php:210 inc/options/general/meta.php:202
msgid "Author"
msgstr "作者"

#: inc/options/general/page-title.php:528
#: inc/options/general/posts-listing.php:539
msgid "Post Meta"
msgstr "文章元数据"

#: inc/options/general/posts-listing.php:461
#: inc/options/woocommerce/card-product-elements.php:446
msgid "Length"
msgstr "长度"

#: inc/options/general/page-title.php:106
#: inc/options/general/posts-listing.php:442
#: inc/options/woocommerce/card-product-elements.php:443
msgid "Excerpt"
msgstr "摘要"

#: inc/options/general/posts-listing.php:292
#: inc/options/single-elements/featured-image.php:28
#: inc/options/single-elements/related-posts.php:240
#: inc/options/woocommerce/card-product-elements.php:243
#: inc/options/woocommerce/general/cart-page.php:29
#: inc/options/woocommerce/single-product-gallery.php:101
#: inc/panel-builder/header/cart/options.php:682
msgid "Image Ratio"
msgstr "图片比例"

#: inc/options/general/posts-listing.php:270
#: inc/options/single-elements/featured-image.php:11
#: inc/options/single-elements/related-posts.php:222
msgid "Featured Image"
msgstr "特色缩略图"

#: inc/options/general/page-title.php:331
msgid "Heading tag"
msgstr "标题标签"

#: inc/options/general/posts-listing.php:88
msgid "Card Elements"
msgstr "卡片元素"

#: inc/options/general/posts-listing.php:67 inc/options/meta/default.php:98
#: inc/options/meta/page.php:97 inc/options/meta/post.php:93
#: inc/options/single-elements/structure.php:86
#: inc/panel-builder/footer/options.php:13
#: inc/panel-builder/header/middle-row/options.php:42
msgid "Boxed"
msgstr "盒子"

#: inc/options/general/posts-listing.php:60
msgid "Card Type"
msgstr "卡片类型"

#: inc/options/general/posts-listing.php:1636
#: inc/options/woocommerce/card-product-elements.php:152
msgid "Card Options"
msgstr "卡片选项"

#: inc/options/general/posts-listing.php:1492
msgid "Grid"
msgstr "网格"

#: inc/options/general/meta.php:438 inc/options/general/posts-listing.php:66
#: inc/options/general/posts-listing.php:495
#: inc/options/general/posts-listing.php:1482
#: inc/panel-builder/header/cart/options.php:946
#: inc/panel-builder/header/menu/options.php:640
#: inc/panel-builder/header/menu/options.php:670
#: inc/panel-builder/header/mobile-menu/options.php:84
#: inc/panel-builder/header/offcanvas/options.php:232
#: inc/panel-builder/header/search/options.php:695
#: inc/panel-builder/header/trigger/options.php:57
msgid "Simple"
msgstr "简单"

#. translators: placeholder here means the actual structure title.
#: inc/options/general/posts-listing.php:1460
msgid "Set the %s entries default structure."
msgstr "设置 %s 条目的默认结构。"

#. translators: placeholder here means the actual structure title.
#: inc/options/general/posts-listing.php:1455
#: inc/options/posts/custom-post-type-single.php:33
msgid "%s Structure"
msgstr "%s 结构"

#: inc/options/general/colors.php:767
msgid "Site Background"
msgstr "网站背景"

#: inc/options/general/pagination.php:226
#: inc/options/general/posts-listing.php:1203
#: inc/panel-builder/header/button/options.php:715
#: inc/panel-builder/header/button/options.php:744
#: inc/panel-builder/header/button/options.php:774
#: inc/panel-builder/header/button/options.php:802
msgid "Button Color"
msgstr "按钮颜色"

#: inc/options/general/colors.php:508
msgid "Global Colors"
msgstr "全局颜色"

#: inc/options/customizer.php:79 inc/options/general/form-elements.php:203
#: inc/options/general/pagination.php:145
msgid "Colors"
msgstr "颜色"

#: inc/options/general/form-elements.php:199
msgid "Radio & Checkbox"
msgstr "单选与复选框"

#: inc/options/general/form-elements.php:224
#: inc/options/general/form-elements.php:268
#: inc/options/general/form-elements.php:298
#: inc/options/woocommerce/general/account-page.php:101
#: inc/options/woocommerce/general/account-page.php:131
#: inc/options/woocommerce/general/star-rating.php:36
#: inc/options/woocommerce/single-product-tabs.php:231
#: inc/options/woocommerce/single-product-tabs.php:287
#: inc/panel-builder/footer/menu/options.php:196
#: inc/panel-builder/header/menu/options.php:245
#: inc/panel-builder/header/menu/options.php:258
#: inc/panel-builder/header/menu/options.php:307
#: inc/panel-builder/header/menu/options.php:320
#: inc/panel-builder/header/menu/options.php:369
#: inc/panel-builder/header/menu/options.php:382
#: inc/panel-builder/header/menu/options.php:467
#: inc/panel-builder/header/menu/options.php:505
#: inc/panel-builder/header/menu/options.php:543
#: inc/panel-builder/header/menu/options.php:814
#: inc/panel-builder/header/menu/options.php:853
#: inc/panel-builder/header/menu/options.php:892
#: inc/panel-builder/header/mobile-menu/options.php:237
#: inc/panel-builder/header/mobile-menu/options.php:284
#: inc/panel-builder/header/mobile-menu/options.php:409
#: inc/panel-builder/header/mobile-menu/options.php:448
#: inc/panel-builder/header/mobile-menu/options.php:487
msgid "Active"
msgstr "激活"

#: inc/panel-builder/header/mobile-menu/options.php:253
msgid "Dropdown Font Color"
msgstr "下拉文本颜色"

#: inc/options/general/form-elements.php:170
msgid "Textarea Height"
msgstr "文本区域高度"

#: inc/options/general/form-elements.php:160
msgid "Input Height"
msgstr "输入高度"

#: inc/options/general/form-elements.php:150
msgid "Border Size"
msgstr "边框大小"

#: inc/options/general/buttons.php:80 inc/options/general/form-elements.php:119
#: inc/options/general/form-elements.php:276
#: inc/options/general/sidebar.php:384
#: inc/options/single-elements/author-box.php:456
#: inc/options/single-elements/post-share-box.php:635
#: inc/options/woocommerce/card-product-elements.php:100
#: inc/options/woocommerce/general/messages.php:53
#: inc/options/woocommerce/general/messages.php:169
#: inc/options/woocommerce/general/messages.php:283
#: inc/panel-builder/header/cart/options.php:773
#: inc/panel-builder/header/cart/options.php:1029
#: inc/panel-builder/header/menu/options.php:400
#: inc/panel-builder/header/menu/options.php:441
#: inc/panel-builder/header/menu/options.php:480
#: inc/panel-builder/header/menu/options.php:518
#: inc/panel-builder/header/offcanvas/options.php:330
#: inc/panel-builder/header/search/options.php:790
#: inc/panel-builder/header/trigger/options.php:428
#: inc/panel-builder/header/trigger/options.php:470
#: inc/panel-builder/header/trigger/options.php:510
#: inc/panel-builder/header/trigger/options.php:548
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/BackgroundModal.js:144
msgid "Background Color"
msgstr "背景颜色"

#: inc/options/general/form-elements.php:82
msgid "Input & Textarea"
msgstr "输入和文本区域"

#: inc/options/general/form-elements.php:71
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/typography/FontOptions.js:29
msgid "Font Size"
msgstr "字体大小"

#: inc/options/general/form-elements.php:63
#: inc/options/general/form-elements.php:107
#: inc/options/general/form-elements.php:140
#: inc/panel-builder/header/search/options.php:575
#: inc/panel-builder/header/search/options.php:605
msgid "Focus"
msgstr "焦点"

#: inc/options/general/form-elements.php:33
msgid "Modern"
msgstr "现代"

#: inc/options/general/form-elements.php:28
#: inc/options/general/posts-listing.php:1487
msgid "Classic"
msgstr "经典"

#: inc/options/general/form-elements.php:86
#: inc/options/single-elements/author-box.php:423
#: inc/options/woocommerce/single-product-tabs.php:245
#: inc/panel-builder/header/cart/options.php:989
#: inc/panel-builder/header/offcanvas/options.php:289
#: inc/panel-builder/header/search/options.php:751
#: inc/panel-builder/header/trigger/options.php:424
#: inc/panel-builder/header/trigger/options.php:466
#: inc/panel-builder/header/trigger/options.php:506
#: inc/panel-builder/header/trigger/options.php:544
msgid "Border Color"
msgstr "边框颜色"

#: inc/options/general/back-to-top.php:246
#: inc/options/general/back-to-top.php:276 inc/options/general/buttons.php:73
#: inc/options/general/buttons.php:103 inc/options/general/colors.php:556
#: inc/options/general/comments-single.php:168
#: inc/options/general/page-title.php:967
#: inc/options/general/page-title.php:1003
#: inc/options/general/page-title.php:1034
#: inc/options/general/pagination.php:218
#: inc/options/general/pagination.php:248
#: inc/options/general/posts-listing.php:885
#: inc/options/general/posts-listing.php:980
#: inc/options/general/posts-listing.php:1016
#: inc/options/general/posts-listing.php:1047
#: inc/options/general/posts-listing.php:1092
#: inc/options/general/posts-listing.php:1138
#: inc/options/general/posts-listing.php:1184
#: inc/options/general/posts-listing.php:1226
#: inc/options/integrations/tutorlms-single.php:75
#: inc/options/integrations/tutorlms-single.php:120
#: inc/options/single-elements/author-box.php:372
#: inc/options/single-elements/author-box.php:405
#: inc/options/single-elements/post-nav.php:194
#: inc/options/single-elements/post-nav.php:214
#: inc/options/single-elements/post-share-box.php:570
#: inc/options/single-elements/post-share-box.php:628
#: inc/options/single-elements/post-share-box.php:657
#: inc/options/single-elements/related-posts.php:606
#: inc/options/single-elements/related-posts.php:646
#: inc/options/woocommerce/card-product-elements.php:663
#: inc/options/woocommerce/card-product-elements.php:804
#: inc/options/woocommerce/card-product-elements.php:845
#: inc/options/woocommerce/card-product-elements.php:877
#: inc/options/woocommerce/card-product-elements.php:962
#: inc/options/woocommerce/card-product-elements.php:993
#: inc/options/woocommerce/card-product-elements.php:1036
#: inc/options/woocommerce/general/messages.php:46
#: inc/options/woocommerce/general/messages.php:95
#: inc/options/woocommerce/general/messages.php:125
#: inc/options/woocommerce/general/messages.php:161
#: inc/options/woocommerce/general/messages.php:211
#: inc/options/woocommerce/general/messages.php:241
#: inc/options/woocommerce/general/messages.php:276
#: inc/options/woocommerce/general/messages.php:324
#: inc/options/woocommerce/general/messages.php:352
#: inc/options/woocommerce/general/quantity-input.php:89
#: inc/options/woocommerce/general/quantity-input.php:130
#: inc/options/woocommerce/single-product-elements.php:302
#: inc/options/woocommerce/single-product-elements.php:346
#: inc/options/woocommerce/single-product-elements.php:383
#: inc/options/woocommerce/single-product-elements.php:414
#: inc/options/woocommerce/single-product-elements.php:456
#: inc/options/woocommerce/single-product-elements.php:487
#: inc/options/woocommerce/single-product-gallery.php:170
#: inc/options/woocommerce/single-product-gallery.php:200
#: inc/options/woocommerce/single-product-gallery.php:236
#: inc/options/woocommerce/single-product-gallery.php:266
#: inc/options/woocommerce/single-product-tabs.php:224
#: inc/panel-builder/footer/menu/options.php:190
#: inc/panel-builder/footer/socials/options.php:281
#: inc/panel-builder/footer/socials/options.php:320
#: inc/panel-builder/footer/socials/options.php:368
#: inc/panel-builder/header/button/options.php:389
#: inc/panel-builder/header/button/options.php:402
#: inc/panel-builder/header/button/options.php:440
#: inc/panel-builder/header/button/options.php:452
#: inc/panel-builder/header/button/options.php:490
#: inc/panel-builder/header/button/options.php:502
#: inc/panel-builder/header/button/options.php:588
#: inc/panel-builder/header/button/options.php:600
#: inc/panel-builder/header/button/options.php:638
#: inc/panel-builder/header/button/options.php:650
#: inc/panel-builder/header/button/options.php:688
#: inc/panel-builder/header/button/options.php:700
#: inc/panel-builder/header/button/options.php:766
#: inc/panel-builder/header/button/options.php:795
#: inc/panel-builder/header/button/options.php:823
#: inc/panel-builder/header/cart/options.php:223
#: inc/panel-builder/header/cart/options.php:253
#: inc/panel-builder/header/cart/options.php:282
#: inc/panel-builder/header/cart/options.php:348
#: inc/panel-builder/header/cart/options.php:378
#: inc/panel-builder/header/cart/options.php:407
#: inc/panel-builder/header/cart/options.php:977
#: inc/panel-builder/header/cart/options.php:1013
#: inc/panel-builder/header/cart/options.php:1053
#: inc/panel-builder/header/logo/options.php:480
#: inc/panel-builder/header/logo/options.php:511
#: inc/panel-builder/header/logo/options.php:541
#: inc/panel-builder/header/menu/options.php:238
#: inc/panel-builder/header/menu/options.php:252
#: inc/panel-builder/header/menu/options.php:301
#: inc/panel-builder/header/menu/options.php:314
#: inc/panel-builder/header/menu/options.php:363
#: inc/panel-builder/header/menu/options.php:376
#: inc/panel-builder/header/menu/options.php:461
#: inc/panel-builder/header/menu/options.php:500
#: inc/panel-builder/header/menu/options.php:538
#: inc/panel-builder/header/menu/options.php:602
#: inc/panel-builder/header/menu/options.php:808
#: inc/panel-builder/header/menu/options.php:848
#: inc/panel-builder/header/menu/options.php:887
#: inc/panel-builder/header/mobile-menu/options.php:231
#: inc/panel-builder/header/mobile-menu/options.php:278
#: inc/panel-builder/header/mobile-menu/options.php:403
#: inc/panel-builder/header/mobile-menu/options.php:443
#: inc/panel-builder/header/mobile-menu/options.php:482
#: inc/panel-builder/header/offcanvas/options.php:276
#: inc/panel-builder/header/offcanvas/options.php:314
#: inc/panel-builder/header/offcanvas/options.php:355
#: inc/panel-builder/header/search/options.php:202
#: inc/panel-builder/header/search/options.php:232
#: inc/panel-builder/header/search/options.php:261
#: inc/panel-builder/header/search/options.php:328
#: inc/panel-builder/header/search/options.php:359
#: inc/panel-builder/header/search/options.php:389
#: inc/panel-builder/header/search/options.php:545
#: inc/panel-builder/header/search/options.php:635
#: inc/panel-builder/header/search/options.php:664
#: inc/panel-builder/header/search/options.php:738
#: inc/panel-builder/header/search/options.php:774
#: inc/panel-builder/header/search/options.php:813
#: inc/panel-builder/header/socials/options.php:275
#: inc/panel-builder/header/socials/options.php:305
#: inc/panel-builder/header/socials/options.php:334
#: inc/panel-builder/header/socials/options.php:404
#: inc/panel-builder/header/socials/options.php:436
#: inc/panel-builder/header/socials/options.php:467
#: inc/panel-builder/header/socials/options.php:556
#: inc/panel-builder/header/socials/options.php:595
#: inc/panel-builder/header/socials/options.php:634
#: inc/panel-builder/header/trigger/options.php:226
#: inc/panel-builder/header/trigger/options.php:256
#: inc/panel-builder/header/trigger/options.php:285
#: inc/panel-builder/header/trigger/options.php:353
#: inc/panel-builder/header/trigger/options.php:383
#: inc/panel-builder/header/trigger/options.php:412
#: inc/panel-builder/header/trigger/options.php:496
#: inc/panel-builder/header/trigger/options.php:535
#: inc/panel-builder/header/trigger/options.php:573
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-border.js:115
msgid "Hover"
msgstr "悬停"

#: inc/options/general/buttons.php:50
#: inc/options/general/comments-single.php:146
#: inc/options/general/form-elements.php:40
#: inc/options/general/form-elements.php:246
#: inc/options/general/pagination.php:196
#: inc/options/single-elements/post-nav.php:174
#: inc/options/woocommerce/single-product-tabs.php:199
#: inc/panel-builder/footer/copyright/options.php:105
#: inc/panel-builder/footer/menu/options.php:164
#: inc/panel-builder/footer/socials/options.php:258
#: inc/panel-builder/footer/widget-area-1/options.php:62
#: inc/panel-builder/header/cart/options.php:200
#: inc/panel-builder/header/cart/options.php:231
#: inc/panel-builder/header/cart/options.php:260
#: inc/panel-builder/header/cart/options.php:714
#: inc/panel-builder/header/menu/options.php:175
#: inc/panel-builder/header/menu/options.php:204
#: inc/panel-builder/header/menu/options.php:267
#: inc/panel-builder/header/menu/options.php:329
#: inc/panel-builder/header/menu/options.php:751
#: inc/panel-builder/header/menu/options.php:781
#: inc/panel-builder/header/menu/options.php:822
#: inc/panel-builder/header/menu/options.php:861
#: inc/panel-builder/header/mobile-menu/options.php:206
#: inc/panel-builder/header/mobile-menu/options.php:348
#: inc/panel-builder/header/mobile-menu/options.php:377
#: inc/panel-builder/header/mobile-menu/options.php:417
#: inc/panel-builder/header/mobile-menu/options.php:456
#: inc/panel-builder/header/search/options.php:179
#: inc/panel-builder/header/search/options.php:210
#: inc/panel-builder/header/search/options.php:239
#: inc/panel-builder/header/search/options.php:522
#: inc/panel-builder/header/socials/options.php:252
#: inc/panel-builder/header/socials/options.php:283
#: inc/panel-builder/header/socials/options.php:312
#: inc/panel-builder/header/text/options.php:166
#: inc/panel-builder/header/text/options.php:195
#: inc/panel-builder/header/text/options.php:237
#: inc/panel-builder/header/text/options.php:276
#: inc/panel-builder/header/trigger/options.php:203
#: inc/panel-builder/header/trigger/options.php:234
#: inc/panel-builder/header/trigger/options.php:263
msgid "Font Color"
msgstr "字体颜色"

#: inc/options/general/back-to-top.php:219
#: inc/options/general/breadcrumbs.php:175 inc/options/general/buttons.php:45
#: inc/options/general/comments-single.php:141
#: inc/options/general/page-title.php:1237
#: inc/options/general/page-title.php:1328
#: inc/options/general/pagination.php:133
#: inc/options/general/posts-listing.php:813
#: inc/options/general/posts-listing.php:1681
#: inc/options/general/sidebar.php:268
#: inc/options/integrations/the-events-calendar/archive.php:36
#: inc/options/integrations/the-events-calendar/single.php:38
#: inc/options/integrations/tutorlms-archive.php:39
#: inc/options/integrations/tutorlms-single.php:219
#: inc/options/meta/default.php:162 inc/options/meta/page.php:160
#: inc/options/meta/post.php:159 inc/options/pages/page.php:35
#: inc/options/posts/custom-post-type-single.php:51
#: inc/options/posts/post.php:26 inc/options/single-elements/author-box.php:235
#: inc/options/single-elements/featured-image.php:138
#: inc/options/single-elements/post-nav.php:169
#: inc/options/single-elements/post-share-box.php:674
#: inc/options/single-elements/post-tags.php:110
#: inc/options/single-elements/related-posts.php:487
#: inc/options/woocommerce/archive-main.php:165
#: inc/options/woocommerce/card-product-elements.php:590
#: inc/options/woocommerce/general/account-page.php:74
#: inc/options/woocommerce/general/cart-page.php:58
#: inc/options/woocommerce/general/checkout-page.php:200
#: inc/options/woocommerce/general/product-badges.php:182
#: inc/options/woocommerce/general/quantity-input.php:61
#: inc/options/woocommerce/general/store-notice.php:56
#: inc/options/woocommerce/related-upsells.php:115
#: inc/options/woocommerce/single-main.php:61
#: inc/options/woocommerce/single-product-elements.php:129
#: inc/options/woocommerce/single-product-gallery.php:143
#: inc/options/woocommerce/single-product-gallery.php:367
#: inc/options/woocommerce/single-product-tabs.php:182
#: inc/panel-builder/footer/copyright/options.php:89
#: inc/panel-builder/footer/menu/options.php:147
#: inc/panel-builder/footer/middle-row/options.php:464
#: inc/panel-builder/footer/socials/options.php:227
#: inc/panel-builder/footer/widget-area-1/options.php:57
#: inc/panel-builder/header/button/options.php:315
#: inc/panel-builder/header/cart/options.php:146
#: inc/panel-builder/header/cart/options.php:704
#: inc/panel-builder/header/logo/options.php:406
#: inc/panel-builder/header/menu/options.php:152
#: inc/panel-builder/header/menu/options.php:730
#: inc/panel-builder/header/middle-row/options.php:139
#: inc/panel-builder/header/mobile-menu/options.php:186
#: inc/panel-builder/header/offcanvas-logo/options.php:71
#: inc/panel-builder/header/offcanvas/options.php:121
#: inc/panel-builder/header/search/options.php:124
#: inc/panel-builder/header/search/options.php:506
#: inc/panel-builder/header/socials/options.php:191
#: inc/panel-builder/header/text/options.php:150
#: inc/panel-builder/header/trigger/options.php:148
msgid "Design"
msgstr "设计"

#: inc/options/general/pagination.php:118
msgid "Pagination Top Spacing"
msgstr "分页顶部间距"

#: inc/options/general/pagination.php:39
msgid "Infinite Scroll"
msgstr "无限滚动"

#: inc/components/pagination.php:97 inc/options/general/pagination.php:38
#: inc/options/general/pagination.php:61
msgid "Load More"
msgstr "加载更多"

#: inc/options/general/pagination.php:37
msgid "Next/Prev"
msgstr "下一页/上一页"

#: inc/options/general/pagination.php:36
msgid "Standard"
msgstr "标准"

#: inc/options/general/pagination.php:29
msgid "Pagination Type"
msgstr "分页类型"

#: inc/options/customizer.php:55 inc/options/customizer.php:161
#: inc/options/general/back-to-top.php:26
#: inc/options/general/breadcrumbs.php:48 inc/options/general/buttons.php:19
#: inc/options/general/comments-single.php:25
#: inc/options/general/page-title.php:1231
#: inc/options/general/page-title.php:1301
#: inc/options/general/pagination.php:24
#: inc/options/general/posts-listing.php:29
#: inc/options/general/posts-listing.php:1466
#: inc/options/general/sidebar.php:17
#: inc/options/integrations/the-events-calendar/archive.php:25
#: inc/options/integrations/the-events-calendar/single.php:27
#: inc/options/integrations/tutorlms-archive.php:24
#: inc/options/integrations/tutorlms-single.php:207
#: inc/options/meta/default.php:29 inc/options/meta/page.php:27
#: inc/options/meta/post.php:24 inc/options/pages/page.php:24
#: inc/options/posts/custom-post-type-single.php:39
#: inc/options/posts/post.php:16 inc/options/single-elements/author-box.php:21
#: inc/options/single-elements/featured-image.php:21
#: inc/options/single-elements/post-nav.php:29
#: inc/options/single-elements/post-share-box.php:668
#: inc/options/single-elements/post-tags.php:29
#: inc/options/single-elements/related-posts.php:29
#: inc/options/woocommerce/archive-main.php:48
#: inc/options/woocommerce/card-product-elements.php:159
#: inc/options/woocommerce/general/account-page.php:19
#: inc/options/woocommerce/general/cart-page.php:19
#: inc/options/woocommerce/general/checkout-page.php:52
#: inc/options/woocommerce/general/product-badges.php:19
#: inc/options/woocommerce/general/quantity-input.php:19
#: inc/options/woocommerce/general/store-notice.php:24
#: inc/options/woocommerce/related-upsells.php:16
#: inc/options/woocommerce/single-main.php:49
#: inc/options/woocommerce/single-product-elements.php:14
#: inc/options/woocommerce/single-product-gallery.php:10
#: inc/options/woocommerce/single-product-gallery.php:304
#: inc/options/woocommerce/single-product-tabs.php:16
#: inc/panel-builder/footer/copyright/options.php:5
#: inc/panel-builder/footer/menu/options.php:33
#: inc/panel-builder/footer/middle-row/options.php:23
#: inc/panel-builder/footer/socials/options.php:5
#: inc/panel-builder/footer/widget-area-1/options.php:9
#: inc/panel-builder/header/button/options.php:5
#: inc/panel-builder/header/cart/options.php:11
#: inc/panel-builder/header/cart/options.php:576
#: inc/panel-builder/header/logo/options.php:5
#: inc/panel-builder/header/menu/options.php:34
#: inc/panel-builder/header/menu/options.php:590
#: inc/panel-builder/header/middle-row/options.php:27
#: inc/panel-builder/header/mobile-menu/options.php:25
#: inc/panel-builder/header/offcanvas-logo/options.php:6
#: inc/panel-builder/header/offcanvas/options.php:5
#: inc/panel-builder/header/search/options.php:42
#: inc/panel-builder/header/search/options.php:415
#: inc/panel-builder/header/socials/options.php:5
#: inc/panel-builder/header/text/options.php:5
#: inc/panel-builder/header/trigger/options.php:5
msgid "General"
msgstr "常规"

#: inc/options/general/back-to-top.php:241
#: inc/options/general/back-to-top.php:270 inc/options/general/buttons.php:68
#: inc/options/general/buttons.php:98 inc/options/general/colors.php:526
#: inc/options/general/colors.php:551 inc/options/general/colors.php:606
#: inc/options/general/colors.php:628 inc/options/general/colors.php:649
#: inc/options/general/colors.php:671 inc/options/general/colors.php:693
#: inc/options/general/colors.php:715 inc/options/general/colors.php:737
#: inc/options/general/colors.php:759
#: inc/options/general/comments-single.php:162
#: inc/options/general/form-elements.php:57
#: inc/options/general/form-elements.php:102
#: inc/options/general/form-elements.php:135
#: inc/options/general/form-elements.php:219
#: inc/options/general/form-elements.php:262
#: inc/options/general/form-elements.php:292 inc/options/general/general.php:65
#: inc/options/general/page-title.php:876
#: inc/options/general/page-title.php:961
#: inc/options/general/page-title.php:997
#: inc/options/general/page-title.php:1028
#: inc/options/general/page-title.php:1090
#: inc/options/general/pagination.php:212
#: inc/options/general/pagination.php:242
#: inc/options/general/posts-listing.php:855
#: inc/options/general/posts-listing.php:927
#: inc/options/general/posts-listing.php:974
#: inc/options/general/posts-listing.php:1010
#: inc/options/general/posts-listing.php:1041
#: inc/options/general/posts-listing.php:1086
#: inc/options/general/posts-listing.php:1132
#: inc/options/general/posts-listing.php:1178
#: inc/options/general/posts-listing.php:1220
#: inc/options/general/sidebar.php:296 inc/options/general/sidebar.php:397
#: inc/options/integrations/tutorlms-single.php:30
#: inc/options/integrations/tutorlms-single.php:69
#: inc/options/integrations/tutorlms-single.php:114
#: inc/options/integrations/tutorlms-single.php:154
#: inc/options/integrations/tutorlms-single.php:176
#: inc/options/single-elements/author-box.php:261
#: inc/options/single-elements/author-box.php:366
#: inc/options/single-elements/author-box.php:399
#: inc/options/single-elements/author-box.php:438
#: inc/options/single-elements/post-nav.php:189
#: inc/options/single-elements/post-share-box.php:500
#: inc/options/single-elements/post-share-box.php:564
#: inc/options/single-elements/post-share-box.php:623
#: inc/options/single-elements/post-share-box.php:652
#: inc/options/single-elements/post-tags.php:136
#: inc/options/single-elements/related-posts.php:510
#: inc/options/single-elements/related-posts.php:572
#: inc/options/single-elements/related-posts.php:640
#: inc/options/woocommerce/card-product-elements.php:76
#: inc/options/woocommerce/card-product-elements.php:87
#: inc/options/woocommerce/card-product-elements.php:123
#: inc/options/woocommerce/card-product-elements.php:134
#: inc/options/woocommerce/card-product-elements.php:629
#: inc/options/woocommerce/card-product-elements.php:701
#: inc/options/woocommerce/card-product-elements.php:741
#: inc/options/woocommerce/card-product-elements.php:799
#: inc/options/woocommerce/card-product-elements.php:839
#: inc/options/woocommerce/card-product-elements.php:871
#: inc/options/woocommerce/card-product-elements.php:918
#: inc/options/woocommerce/card-product-elements.php:956
#: inc/options/woocommerce/card-product-elements.php:987
#: inc/options/woocommerce/card-product-elements.php:1031
#: inc/options/woocommerce/card-product-elements.php:1065
#: inc/options/woocommerce/general/account-page.php:95
#: inc/options/woocommerce/general/account-page.php:125
#: inc/options/woocommerce/general/account-page.php:151
#: inc/options/woocommerce/general/messages.php:41
#: inc/options/woocommerce/general/messages.php:66
#: inc/options/woocommerce/general/messages.php:89
#: inc/options/woocommerce/general/messages.php:119
#: inc/options/woocommerce/general/messages.php:155
#: inc/options/woocommerce/general/messages.php:182
#: inc/options/woocommerce/general/messages.php:205
#: inc/options/woocommerce/general/messages.php:235
#: inc/options/woocommerce/general/messages.php:271
#: inc/options/woocommerce/general/messages.php:296
#: inc/options/woocommerce/general/messages.php:319
#: inc/options/woocommerce/general/messages.php:347
#: inc/options/woocommerce/general/quantity-input.php:82
#: inc/options/woocommerce/general/quantity-input.php:117
#: inc/options/woocommerce/general/quantity-input.php:124
#: inc/options/woocommerce/general/store-notice.php:76
#: inc/options/woocommerce/general/store-notice.php:97
#: inc/options/woocommerce/related-upsells.php:144
#: inc/options/woocommerce/single-product-elements.php:162
#: inc/options/woocommerce/single-product-elements.php:203
#: inc/options/woocommerce/single-product-elements.php:296
#: inc/options/woocommerce/single-product-elements.php:330
#: inc/options/woocommerce/single-product-elements.php:338
#: inc/options/woocommerce/single-product-elements.php:377
#: inc/options/woocommerce/single-product-elements.php:408
#: inc/options/woocommerce/single-product-elements.php:450
#: inc/options/woocommerce/single-product-elements.php:481
#: inc/options/woocommerce/single-product-elements.php:549
#: inc/options/woocommerce/single-product-gallery.php:164
#: inc/options/woocommerce/single-product-gallery.php:194
#: inc/options/woocommerce/single-product-gallery.php:230
#: inc/options/woocommerce/single-product-gallery.php:260
#: inc/options/woocommerce/single-product-tabs.php:219
#: inc/options/woocommerce/single-product-tabs.php:258
#: inc/panel-builder/footer/copyright/options.php:127
#: inc/panel-builder/footer/menu/options.php:185
#: inc/panel-builder/footer/middle-row/options.php:496
#: inc/panel-builder/footer/socials/options.php:275
#: inc/panel-builder/footer/socials/options.php:314
#: inc/panel-builder/footer/socials/options.php:363
#: inc/panel-builder/footer/widget-area-1/options.php:84
#: inc/panel-builder/header/button/options.php:382
#: inc/panel-builder/header/button/options.php:396
#: inc/panel-builder/header/button/options.php:434
#: inc/panel-builder/header/button/options.php:446
#: inc/panel-builder/header/button/options.php:484
#: inc/panel-builder/header/button/options.php:496
#: inc/panel-builder/header/button/options.php:582
#: inc/panel-builder/header/button/options.php:594
#: inc/panel-builder/header/button/options.php:632
#: inc/panel-builder/header/button/options.php:644
#: inc/panel-builder/header/button/options.php:682
#: inc/panel-builder/header/button/options.php:694
#: inc/panel-builder/header/button/options.php:760
#: inc/panel-builder/header/button/options.php:790
#: inc/panel-builder/header/button/options.php:818
#: inc/panel-builder/header/cart/options.php:217
#: inc/panel-builder/header/cart/options.php:248
#: inc/panel-builder/header/cart/options.php:277
#: inc/panel-builder/header/cart/options.php:343
#: inc/panel-builder/header/cart/options.php:373
#: inc/panel-builder/header/cart/options.php:402
#: inc/panel-builder/header/cart/options.php:766
#: inc/panel-builder/header/cart/options.php:787
#: inc/panel-builder/header/cart/options.php:972
#: inc/panel-builder/header/cart/options.php:1007
#: inc/panel-builder/header/cart/options.php:1047
#: inc/panel-builder/header/logo/options.php:475
#: inc/panel-builder/header/logo/options.php:506
#: inc/panel-builder/header/logo/options.php:536
#: inc/panel-builder/header/logo/options.php:617
#: inc/panel-builder/header/logo/options.php:639
#: inc/panel-builder/header/logo/options.php:660
#: inc/panel-builder/header/menu/options.php:233
#: inc/panel-builder/header/menu/options.php:296
#: inc/panel-builder/header/menu/options.php:358
#: inc/panel-builder/header/menu/options.php:803
#: inc/panel-builder/header/menu/options.php:843
#: inc/panel-builder/header/menu/options.php:882
#: inc/panel-builder/header/menu/options.php:952
#: inc/panel-builder/header/menu/options.php:984
#: inc/panel-builder/header/menu/options.php:1014
#: inc/panel-builder/header/mobile-menu/options.php:226
#: inc/panel-builder/header/mobile-menu/options.php:273
#: inc/panel-builder/header/mobile-menu/options.php:398
#: inc/panel-builder/header/mobile-menu/options.php:438
#: inc/panel-builder/header/mobile-menu/options.php:477
#: inc/panel-builder/header/offcanvas/options.php:270
#: inc/panel-builder/header/offcanvas/options.php:308
#: inc/panel-builder/header/offcanvas/options.php:349
#: inc/panel-builder/header/search/options.php:196
#: inc/panel-builder/header/search/options.php:227
#: inc/panel-builder/header/search/options.php:256
#: inc/panel-builder/header/search/options.php:322
#: inc/panel-builder/header/search/options.php:354
#: inc/panel-builder/header/search/options.php:384
#: inc/panel-builder/header/search/options.php:540
#: inc/panel-builder/header/search/options.php:570
#: inc/panel-builder/header/search/options.php:600
#: inc/panel-builder/header/search/options.php:630
#: inc/panel-builder/header/search/options.php:659
#: inc/panel-builder/header/search/options.php:732
#: inc/panel-builder/header/search/options.php:768
#: inc/panel-builder/header/search/options.php:807
#: inc/panel-builder/header/socials/options.php:269
#: inc/panel-builder/header/socials/options.php:300
#: inc/panel-builder/header/socials/options.php:329
#: inc/panel-builder/header/socials/options.php:398
#: inc/panel-builder/header/socials/options.php:431
#: inc/panel-builder/header/socials/options.php:462
#: inc/panel-builder/header/socials/options.php:551
#: inc/panel-builder/header/socials/options.php:590
#: inc/panel-builder/header/socials/options.php:629
#: inc/panel-builder/header/trigger/options.php:220
#: inc/panel-builder/header/trigger/options.php:251
#: inc/panel-builder/header/trigger/options.php:280
#: inc/panel-builder/header/trigger/options.php:347
#: inc/panel-builder/header/trigger/options.php:378
#: inc/panel-builder/header/trigger/options.php:407
#: inc/panel-builder/header/trigger/options.php:490
#: inc/panel-builder/header/trigger/options.php:530
#: inc/panel-builder/header/trigger/options.php:568
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/BackgroundModal.js:154
#: static/js/options/options/background/ImagePicker.js:190
#: static/js/options/options/background/PatternPicker.js:170
#: static/js/options/options/ct-border.js:94
msgid "Initial"
msgstr "初始"

#: inc/options/general/cards-reveal-effect.php:13
msgid "Cards Reveal Effect"
msgstr "卡片显示效果"

#: inc/options/general/performance.php:41
msgid "Lazy Load Images"
msgstr "延迟加载图像"

#: inc/options/general/page-title.php:15
#: inc/options/general/page-title.php:1305 inc/options/meta/page.php:8
#: inc/options/pages/author-page.php:25 inc/options/pages/page.php:99
#: inc/options/pages/search-page.php:25 inc/options/posts/blog.php:31
#: inc/options/posts/categories.php:18
#: inc/options/posts/custom-post-type-archive.php:28
#: inc/options/posts/custom-post-type-single.php:66
#: inc/options/posts/post.php:91 inc/options/woocommerce/archive-main.php:17
#: inc/options/woocommerce/single-main.php:18
msgid "Page Title"
msgstr "页面标题"

#: inc/options/general/sidebar-particular.php:44
#: inc/options/meta/default.php:65 inc/options/meta/page.php:64
#: inc/options/meta/post.php:60 inc/options/single-elements/structure.php:47
msgid "Right Sidebar"
msgstr "右侧边栏"

#: inc/options/general/sidebar-particular.php:39
#: inc/options/meta/default.php:60 inc/options/meta/page.php:59
#: inc/options/meta/post.php:55 inc/options/single-elements/structure.php:42
msgid "Left Sidebar"
msgstr "左侧边栏"

#: inc/options/meta/default.php:55 inc/options/meta/page.php:54
#: inc/options/meta/post.php:50 inc/options/single-elements/structure.php:37
msgid "Normal Width"
msgstr "正常宽度"

#: inc/options/meta/default.php:50 inc/options/meta/page.php:49
#: inc/options/meta/post.php:45 inc/options/single-elements/structure.php:32
msgid "Narrow Width"
msgstr "狭窄宽度"

#: inc/options/meta/default.php:45 inc/options/meta/page.php:44
#: inc/options/meta/post.php:40
msgid "Inherit from customizer"
msgstr "从编辑器继承"

#: inc/options/meta/page.php:23 inc/options/pages/page.php:19
#: inc/options/pages/page.php:121
#: inc/options/posts/custom-post-type-single.php:88
#: inc/options/posts/post.php:113 inc/options/woocommerce/single-main.php:45
msgid "Page Structure"
msgstr "页面结构"

#: inc/integrations/beaver-themer.php:8 inc/options/customizer.php:61
msgid "Header"
msgstr "页眉"

#: inc/components/back-to-top.php:54
msgid "Go to top"
msgstr "转到顶部"

#: inc/options/single-elements/post-share-box.php:203
msgid "LinkedIn"
msgstr "LinkedIn"

#: inc/options/general/page-title.php:327
#: inc/options/general/page-title.php:360
#: inc/options/general/posts-listing.php:221
#: inc/options/woocommerce/card-product-elements.php:332
#: inc/options/woocommerce/single-product-layers.php:83
#: inc/options/woocommerce/single-product-layers.php:150
#: inc/options/woocommerce/single-product-layers.php:246
#: inc/options/woocommerce/single-product-layers.php:309
#: inc/options/woocommerce/single-product-layers.php:399
msgid "Title"
msgstr "标题"

#. Translators: %s is the theme name.
#: admin/helpers/meta-boxes.php:141
msgid "%s Settings"
msgstr "%s 设置"

#: inc/components/pagination.php:38
msgid "No more posts to load"
msgstr "没有更多的文章加载"

#: inc/options/single-elements/post-share-box.php:173
msgid "Facebook"
msgstr "Facebook"

#: inc/components/single/comments.php:136
msgid "Website"
msgstr "站点"

#: woocommerce/product-searchform.php:26
msgid "Search products&hellip;"
msgstr "搜索产品 &hellip;"

#: woocommerce/product-searchform.php:24
msgid "Search for:"
msgstr "搜索:"

#: woocommerce/cart/cart.php:41 woocommerce/cart/cart.php:188
msgid "Quantity"
msgstr "数量"

#: woocommerce/cart/mini-cart.php:144
msgid "No products in the cart."
msgstr "购物车里没有产品。"

#: woocommerce/cart/cart.php:42 woocommerce/cart/cart.php:210
msgid "Subtotal"
msgstr "小计"

#: searchform.php:10
msgctxt "placeholder"
msgid "Search"
msgstr "搜索"

#: template-parts/404.php:11
msgid "It looks like nothing was found at this location. Maybe try to search for something else?"
msgstr "看起来在这个地方什么也没找到。也许试着搜索其他东西?"

#: template-parts/404.php:7
msgid "Oops! That page can&rsquo;t be found."
msgstr "哎呀! 不能找到该网页。"

#: admin/notices/templates.php:45
msgid "This way you will have access to custom extensions, demo templates and many other awesome features"
msgstr "这样你就可以使用自定义扩展, 演示模板和许多其他很棒的功能"

#: admin/notices/templates.php:43 admin/dashboard/static/bundle/main.js:6
#: admin/dashboard/static/js/screens/Home.js:459
msgid "Blocksy Companion"
msgstr "Blocksy Companion"

#: admin/dashboard/core.php:189
msgid "You do not have sufficient permissions to access this page."
msgstr "你没有足够的权限访问此页面。"

#: inc/options/general/page-title.php:1274
#: inc/options/general/page-title.php:1315
#: inc/options/general/posts-listing.php:1664 inc/options/meta/default.php:143
#: inc/options/meta/page.php:142 inc/options/meta/post.php:141
#: inc/options/single-elements/structure.php:114
#: inc/options/woocommerce/archive-main.php:148
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:151
msgid "Disabled"
msgstr "禁止"

#: inc/components/archive/archive-card.php:265
#: inc/options/general/posts-listing.php:509
msgid "Read More"
msgstr "继续阅读"

#. Theme Name of the theme
#. Translators: %s is the theme name.
#: style.css admin/helpers/meta-boxes.php:142
msgid "Blocksy"
msgstr "Blocksy"

#. Author URI of the theme
#: style.css
#, gp-priority: low
msgid "https://creativethemes.com"
msgstr "https://creativethemes.com"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "CreativeThemes"
msgstr "CreativeThemes"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://creativethemes.com/blocksy/"
msgstr "https://creativethemes.com/blocksy/"