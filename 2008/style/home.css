@charset "utf-8";
div {

}
body {
	background-repeat: repeat-x;
	margin:0;
	padding:0;
	background-image: url(lond.png);
}
*{margin:0;
	padding:0;
	border:0;
}
#warpper {
	background-image: url(lond2.png);
	background-repeat: no-repeat;
	background-position: center;
	height: 532px;
	width: 810px;
	margin-right: auto;
	margin-left: auto;
}
#d_content {
	height: 280px;
	display: block;
	padding-top: 110px;
	padding-bottom: 75px;
}
.CleanFloat {
	clear: both;
}

#d_left {
	float: left;
	margin-top: 50px;
	padding-left: 68px;
}
#d_right {
	float: right;
	border-left-width: 1px;
	border-left-style: dashed;
	border-left-color: #CCC;
	width: 430px;
}
#logion {
	color: #666;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	margin-top: 50px;
	letter-spacing: 1px;
	display: block;
	padding-right: 100px;
	padding-left: 50px;
}
#nav {
	font-size: 13px;
	color: #360;
	font-weight: bold;
	margin-top: 52px;
	display: block;
	padding-left: 32px;
}
#nav ul {
	list-style-type: none;
}
#nav li {
	float: left;
	margin-right: 16px;
}
#nav li a {
	height: 120px;
	width: 71px;
	display: block;
}
#nav li a:hover {
	background-position: 0px 120px;
}
.button1 a {
	background-image: url(button_b.jpg);
}
.button1 a:hover {
	background-image: url(button_b.jpg);
}
.button2 a {
	background-image: url(button_d.jpg);
}
.button2 a:hover {
	background-image: url(button_d.jpg);
}
.button3 a {
	background-image: url(button_pc.jpg);
}
.button3 a:hover {
	background-image: url(button_pc.jpg);
}
.button4 a {
	background-image: url(button_c.jpg);
}
.button4 a:hover {
	background-image: url(button_c.jpg);
}
#d_footer {
	float: left;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 10px;
	color: #006666;
	font-weight: normal;
	width: 810px;
	text-align: center;
	letter-spacing: 1px;
}
