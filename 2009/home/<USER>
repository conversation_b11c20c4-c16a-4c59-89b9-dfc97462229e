@charset "utf-8";
/* CSS Document */

* { margin:0; padding:0; }
body { font-size:13px; line-height:1.231em;
	background:#7D766F url(home.jpg) no-repeat left top; }
a img { border:0; }

.page { width:1010px; }
.logo { margin:15px 55px 90px; display:block; }
.logo a { font-size:40px; line-height:40px; color:#C7C2C0;
	text-decoration:none; font-style:italic; height:40px; }
.logo p {
	font-size:13px;
	line-height:20px;
	color:#B6AB85;
	font-weight: lighter;
}
.body { overflow:hidden; zoom:1; }
.side { width:225px; float:left; }
.side li { display:block; list-style:none; margin-right:100px; }
.side li a { display:block; line-height:65px; text-align:center; cursor:pointer;
	text-decoration:none; font-weight:bold; width:100%;
	font-family:"Gabriola"; font-family:"Segoe Script"; color:#FC0; font-size:20px;
	background:url(link-bg.png) no-repeat right center; }
.main { width:625px; height:368px; float:left; overflow:hidden; position:relative; }
.main dl { position:absolute; width:32000px; height:360px; overflow:hidden; zoom:1; }
.foot { padding:70px 0px 20px; font-size:11px; text-align:center; color:#CCC; }
.foot a { color:#CCC; text-decoration:none; }
.trim { clear:both; line-height:0; height:0; overflow:hidden; font-size:0; }

.core { display:block; float:left; height:400px; width:640px; overflow:hidden; }
.core h2 { font-size:15px; font-weight:normal; color:#333; display:block; height:35px; line-height:35px; }

#blog li {
	margin:0 2em; padding:0.5em 0 0.5em 1em;
	list-style-image: url(ico1.png);
	border-bottom:1px dashed #DDD;
}
#blog li:hover { background:#EEE; }
#blog li a { color:#B6AB85; font-size:14px; }
#blog li p { color:#666; font-size:12px; }
#blog .more { padding:7px; }

#myphoto {
	border-right-width: 1px;
	border-right-style: dashed;
	border-right-color: #BBB;
	float: left;
}
#myphoto img {
	border: 2px solid #BEAE85;
	margin: 20px 18px 0px 13px;
	padding: 1px;
}
#myphoto p {
	background-image: url(pg1.jpg);
	background-repeat: no-repeat;
	line-height: 70px;
	color: #FFF;
	text-indent: 90px;
	font-size: 14px;
	background-position: 25px;
}
#aboutme {
	float: right;
	width: 245px;
	padding-top: 20px;
}
#aboutme p { margin:1em 0; }
#wellcome {
	float: left;
	width: 220px;
	padding: 20px 0px 0px 16px;
	color: #036;
}
#wellcome P{
	text-indent: 2em;
	margin:1em 0;
	font-size: 14px;
	font-family: Arial, Helvetica, sans-serif;
	}
#newRelease {
	border-left-width: 1px;
	border-left-style: dashed;
	border-left-color: #BBB;
	float: right;}
#newRelease img{
	border: 2px solid #BEAE85;
	margin: 10px 25px 8px 15px;
	padding: 1px;}
#newRelease p {
	background-image: url(pg3.jpg);
	background-repeat: no-repeat;
	line-height: 60px;
	color: #FFF;
	background-position: 9px;
}
#link img {border:1px solid #CCC; margin:16px 2px 0px 2px; padding: 1px;}
#link img:hover { border-color:#99B; }
#work img {border:1px solid #BBB; margin:13px 4px 0px 16px; padding: 2px; background:#E3E3E3;}
#work img:hover { border-color:#99B; }
.more {display:block; width:6em; padding-left: 2em; color:#666; text-decoration:none;}
/*test*/




