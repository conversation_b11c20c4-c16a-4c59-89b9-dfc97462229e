// JavaScript Document charset=utf-8 by anyLiv 1210-2009

jQuery(function($){


// for ie6 PNG FixPlus ...
if( $.browser.msie && $.browser.version == 6 ){
	$(document).pngFix();
}


// for remove dotted line ...
$('.side a').focus(function(){
	this.blur();
});

//
$('dd.core a').attr('target','_blank');

// for side li's animate ...
var objList = $('.side li');

objList.each(function(){
	var n = objList.index(this);
	var m = 100 - 7 * n;
//	$(this).css('margin-right', m);
	$(this).animate({ marginRight:m },'slow');
});

objList.hover(function(){
	$(this).stop().animate({ marginRight:'50px' },'fast');
},function(){
	var m = 100 - 7 * objList.index(this);
	$(this).stop().animate({ marginRight:m },'slow');
});


// main vertical scroll
$('.main').scrollable({ size:1, clickable:false }).navigator({ 
	navi: '.side', naviItem: 'a'
});



/*
//
jQuery.ajax({
	url:'/blog/?feed=rss2',
	type:'GET', dataType:'xml', timeout:10000,
	error: function(){ $('#blog .temp').html('》<a href="/blog/">点击这里访问我的博客</a>《'); },
	success: function(xml){
	var tmpItem = 0;
		$(xml).find('item').each(function(){
			var txtHead = $(this).find('title').text();
			var txtHref = $(this).find('link').text();
			var txtLink = '<a target="_blank" href="' + txtHref + '">' + txtHead + '</a>';
			var txtBody = '<p>' + $(this).find('description').text().substring(0,46) + '...</p>';
			if( tmpItem < 6 ){ $('#blog > ul').append('<li>' + txtLink + txtBody + '</li>'); }
			tmpItem++;
		});
		$('.temp').hide();
	}
});
*/


//


//


});
